---
alwaysApply: true
---
## 📊 核心技术要点速查表

| 技术类别 | 核心文件 | 主要功能 | 文档章节 |
|---------|---------|---------|---------|
| 🔧 **数据库** | `/include/dbinfo.inc.php` | 数据库连接配置 | [数据库配置](#数据库连接配置规范) |
| 🔧 **数据库** | `/include/class/dsql.class.php` | 数据库操作核心类 | [数据库操作](#数据库操作核心类) |
| ⏰ **定时任务** | `/include/class/cron.class.php` | 定时任务管理 | [定时任务](#定时任务核心类) |
| ⏰ **定时任务** | `/include/cron.php` | 定时任务执行入口 | [定时任务配置](#定时任务系统配置) |
| 💾 **缓存** | `/include/class/FileCache.class.php` | 文件缓存系统 | [文件缓存](#文件缓存系统) |
| 👤 **认证** | `/include/class/userLogin.class.php` | 用户登录认证 | [用户认证](#用户认证系统) |
| 🔌 **API** | `/api/handlers/handlers.class.php` | API核心处理器 | [API处理器](#api核心处理器) |
| 📺 **直播** | `/api/handlers/live.class.php` | 直播模块API | [直播API](#直播模块api) |
| 🏠 **家政** | `/api/handlers/homemaking.controller.php` | 家政模块API | [家政API](#家政模块api) |
| 🕷️ **采集** | `/4/index.php` | 采集插件主界面 | [采集插件](#采集插件主界面) |
| 🕷️ **采集** | `/4/getNews.php` | 内容采集执行器 | [内容采集](#内容采集执行器) |
| 🛡️ **安全** | `/include/uploadsafe.inc.php` | 文件上传安全 | [上传安全](#文件上传安全) |
| 🛡️ **安全** | `/include/config/siteConfig.inc.php` | 系统安全配置 | [安全配置](#安全配置文件) |
| 📁 **文件** | `/include/class/file.class.php` | 文件处理功能 | [文件处理](#文件处理功能) |
| 🔧 **系统** | `/include/common.inc.php` | 系统核心入口 | [系统配置](#系统核心配置) |

1. **📖 详细文档索引系统**
   - ✅ 创建了可点击的主要章节目录
   - ✅ 添加了按功能分类的快速索引
   - ✅ 提供了核心配置文件、类库文件、API接口文件、采集插件文件的直接链接

2. **🔗 优化章节标识和锚点**
   - ✅ 为所有重要技术点添加了清晰的标题标识
   - ✅ 使用统一的标题层级结构(H1-H6)
   - ✅ 为核心配置文件、类文件、API接口添加了独立锚点标识

3. **🔍 快速搜索标记**
   - ✅ 为配置文件路径添加了🔧标记
   - ✅ 为目录结构添加了📁标记
   - ✅ 为API接口添加了🔌标记
   - ✅ 为安全机制添加了🔐标记
   - ✅ 为数据库相关添加了🗄️标记

4. **📊 技术要点速查表**
   - ✅ 创建了核心技术要点速查表
   - ✅ 按技术类别组织(数据库、定时任务、缓存、认证、API、采集、安全、文件、系统)
   - ✅ 包含文件路径、主要功能和对应文档章节的快速索引

### 🎯 预期效果

- **AI查询效率提升**: 通过索引可快速定位相关技术信息
- **减少搜索时间**: 避免全文搜索，直接跳转到目标章节
- **提高可读性**: 清晰的结构和视觉标识提升文档实用性
- **便于团队使用**: 开发团队可快速查找特定技术细节

---

## 🏗️ 系统架构图

### 整体架构设计

```mermaid
graph TB
    A[用户访问层] --> B[Web服务器层]
    B --> C[应用程序层]
    C --> D[数据存储层]

    subgraph "用户访问层"
        A1[PC端浏览器]
        A2[移动端浏览器]
        A3[微信小程序]
        A4[Android APP]
    end

    subgraph "Web服务器层"
        B1[Nginx/Apache]
        B2[PHP-FPM]
        B3[宝塔面板]
    end

    subgraph "应用程序层"
        C1[前端模板系统]
        C2[API接口层]
        C3[业务逻辑层]
        C4[插件系统]
        C5[定时任务系统]
    end

    subgraph "数据存储层"
        D1[MySQL数据库]
        D2[文件缓存]
        D3[OSS云存储]
        D4[日志文件]
    end
```

### 核心文件组织架构

基于实际目录结构分析：

```
hawaiihub.net/
├── 📁 admin/                    # 后台功能目录
│   ├── 📁 app/                 # 后台APP配置目录
│   ├── 📁 business/            # 后台商家配置目录
│   ├── 📁 inc/                 # 后台公共文件
│   ├── 📁 member/              # 后台会员配置目录
│   ├── 📁 siteConfig/          # 后台系统配置目录
│   ├── 📁 templates/           # 后台模板目录
│   │   ├── 📁 app/             # 后台APP配置模板目录
│   │   ├── 📁 business/        # 后台商家配置模板目录
│   │   ├── 📁 member/          # 后台会员配置模板目录
│   │   ├── 📁 siteConfig/      # 后台系统配置模板目录
│   │   ├── 📁 wechat/          # 后台微信配置模板目录
│   │   └── 📁 article/         # 后台资讯信息模块配置模板目录
│   ├── 📁 wechat/              # 后台微信配置目录
│   ├── 📁 article/             # 后台资讯信息模块配置目录
│   ├── 📄 exit.php             # 后台退出功能文件
│   ├── 📄 funSearch.php        # 后台搜索功能文件
│   ├── 📄 index.php            # 后台首页功能文件
│   ├── 📄 index_body.php       # 后台内容功能文件
│   └── 📄 login.php            # 后台登录入口文件
├── 📁 api/                      # 系统核心接口目录
│   ├── 📁 bbs/                 # 整合论坛功能目录
│   ├── 📁 handlers/            # 系统核心接口目录
│   │   ├── 📄 article.class.php      # 新闻文章API核心类
│   │   ├── 📄 education.class.php    # 教育模块API类
│   │   ├── 📄 tieba.class.php        # 贴吧模块API类
│   │   └── 📄 pension.class.php      # 养老模块API类
│   ├── 📁 live/                # 第三方直播接口目录
│   ├── 📁 login/               # 第三方登录接口目录
│   ├── 📁 map/                 # 第三方地图接口目录
│   ├── 📁 payment/             # 第三方支付接口目录
│   ├── 📁 upload/              # 第三方上传接口目录
│   ├── 📁 weixin/              # 微信开发者模式配置目录
│   ├── 📄 appConfig.json       # APP配置文件
│   ├── 📄 login.php            # 第三方登录入口文件
│   ├── 📄 printReport.php      # 打印机上报文件
│   ├── 📄 uc.php               # 论坛整合会员接口文件
│   ├── 📄 weixinAudioUpload.php # 微信录音上传及转码文件
│   └── 📄 weixinImageUpload.php # 微信图片上传文件
├── 📁 data/                     # 系统核心目录
│   ├── 📁 admin/               # 系统核心配置文件
│   ├── 📁 backup/              # 数据库在线备份存储目录
│   ├── 📁 module/              # 后台商店在线安装临时存储目录
│   └── 📄 checkSql_safe.txt    # SQL语句安全检测记录日志文件
├── 📁 design/                   # 专题、自助建站设计视图功能目录
├── 📁 include/                  # 系统核心目录
│   ├── 📁 class/               # 类库目录
│   │   ├── 📄 dsql.class.php         # 数据库操作核心类
│   │   ├── 📄 cron.class.php         # 定时任务核心类
│   │   └── 📄 aliyunOSS.class.php    # 阿里云OSS集成类
│   ├── 📁 config/              # 系统及各模块配置目录
│   ├── 📁 cron/                # 计划任务执行文件目录
│   ├── 📁 data/                # 字体、水印相关目录
│   ├── 📁 lang/                # 多语言配置目录
│   ├── 📁 tpl/                 # smarty模板引擎目录
│   ├── 📁 ueditor/             # 编辑器目录
│   ├── 📄 360panorama.php      # 全景预览程序文件
│   ├── 📄 ajax.php             # 系统核心接口入口文件
│   ├── 📄 attachment.php       # 附件中转文件
│   ├── 📄 common.func.php      # 系统公共函数文件
│   ├── 📄 common.inc.php       # 系统公共核心入口文件
│   ├── 📄 cron.php             # 定时任务执行入口
│   ├── 📄 dbinfo.inc.php       # 数据库连接配置
│   └── 📄 upload.inc.php       # 文件上传处理
├── 📁 4/                        # 采集插件目录
│   ├── 📄 index.php            # 插件主界面
│   ├── 📄 insertNode.php       # 采集节点管理
│   ├── 📄 insertBodyRules.php  # 内容提取规则管理
│   └── 📄 getNews.php          # 内容采集执行器
├── 📁 docs/                     # 文档目录
└── 📁 template/                 # 前端模板文件
```

## 🔧 核心模块说明

### 系统核心配置

#### 核心技术栈
- **后端语言**: PHP (面向对象架构)
- **数据库**: MySQL 
- **前端**: HTML + CSS + JavaScript
- **服务器**: 支持宝塔面板管理
- **缓存机制**: 内置文件缓存系统
- **存储**: 支持本地存储 + 阿里云OSS等云存储

### 项目目录结构概览
```
hawaiihub.net/
├── admin/                    # 后台功能目录 (完整的后台管理系统)
├── api/                      # 系统核心接口目录 (RESTful API接口)
│   ├── handlers/            # 系统核心接口目录 (业务逻辑处理)
│   ├── bbs/                 # 整合论坛功能目录
│   ├── live/                # 第三方直播接口目录
│   ├── login/               # 第三方登录接口目录
│   ├── map/                 # 第三方地图接口目录
│   ├── payment/             # 第三方支付接口目录
│   ├── upload/              # 第三方上传接口目录
│   └── weixin/              # 微信开发者模式配置目录
├── data/                     # 系统核心目录 (数据存储和配置)
│   ├── admin/               # 系统核心配置文件
│   ├── backup/              # 数据库在线备份存储目录
│   └── module/              # 后台商店在线安装临时存储目录
├── design/                   # 专题、自助建站设计视图功能目录
├── include/                  # 系统核心目录 (核心类库和配置)
│   ├── class/               # 类库目录
│   ├── config/              # 系统及各模块配置目录
│   ├── cron/                # 计划任务执行文件目录
│   ├── data/                # 字体、水印相关目录
│   ├── lang/                # 多语言配置目录
│   ├── tpl/                 # smarty模板引擎目录
│   └── ueditor/             # 编辑器目录
├── 4/                       # 采集插件目录 (内容采集系统)
├── docs/                    # 文档目录
└── template/                # 前端模板文件
