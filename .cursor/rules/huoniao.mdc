---
alwaysApply: true
---
使用codebase-retrieval、view、或其他工具查询和分析HawaiiHub.net项目中的火鸟门户系统相关文件时，请在完成分析后执行以下步骤：

1. 检查 `docs/火鸟门户系统技术架构完整分析文档.md` 文件是否已包含本次分析发现的新技术信息
2. 如果发现以下任一情况，请使用str-replace-editor工具更新该文档：
   - 新发现的核心文件或功能模块
   - 新的数据库表结构或字段信息
   - 新的API接口或方法
   - 新的技术实现细节或架构设计
   - 新的配置文件或系统机制

3. 更新时请遵循以下原则：
   - 保持文档的整体结构和格式一致性
   - 将新信息归类到相应的章节中
   - 提供具体的代码示例和文件路径
   - 确保所有信息都基于实际代码分析，不添加推测内容

4. 更新完成后，简要说明添加了哪些新内容，以便我了解文档的最新状态

这样可以确保该技术架构文档始终保持最新和完整，成为火鸟门户系统的权威技术参考资料。