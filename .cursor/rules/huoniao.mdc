---
alwaysApply: true
---
使用codebase-retrieval、view、或其他工具查询和分析HawaiiHub.net项目中的火鸟门户系统相关文件时，请在完成分析后执行以下步骤：

1. 检查 `docs/火鸟门户系统技术架构完整分析文档.md` 文件是否已包含本次分析发现的新技术信息
2. 如果发现以下任一情况，请使用str-replace-editor工具更新该文档：
   - 新发现的核心文件或功能模块
   - 新的数据库表结构或字段信息
   - 新的API接口或方法
   - 新的技术实现细节或架构设计
   - 新的配置文件或系统机制

3. 更新时请遵循以下原则：
   - 保持文档的整体结构和格式一致性
   - 将新信息归类到相应的章节中
   - 提供具体的代码示例和文件路径
   - 确保所有信息都基于实际代码分析，不添加推测内容

4. 更新完成后，简要说明添加了哪些新内容，以便我了解文档的最新状态

文档管理agent规则
在创建任何新文档之前，必须执行以下预检查流程：

✅ 每次创建前都先检查现有文档
✅ 优先考虑更新而非新建
✅ 新文档必须有明确的独特价值
✅ 标注清楚文档关系和创建理由
✅ 创建后立即检查整合

1. **重复性检查**：使用`view`工具扫描docs目录，搜索是否已存在相同或相似主题的文档
2. **内容评估**：如果发现现有文档，评估是否可以通过更新现有文档来满足需求，而不是创建新文档
3. **创建决策**：只有在以下情况下才创建新文档：
   - 完全没有相关现有文档
   - 现有文档内容完全不同且无法合并
   - 需要创建专门的子主题文档

4. **创建时标注**：如果确需创建新文档，必须在文档开头注明：
   - 与哪些现有文档的关系
   - 为什么需要单独创建
   - 最后检查时间

5. **即时整合**：创建完成后立即检查是否与其他文档产生重复，如有重复立即处理
去重原则: 一个主题一个权威文档
分类原则: 按功能模块严格分类
时效性原则: 定期检查和更新


🏗️ 结构标准:

推荐的目录结构
清晰的命名规范 （中文命名）
禁止和谨慎操作指南

**核心原则**：预防胜于治疗 - 在源头控制文档重复，而不是事后清理。每次创建文档都要问自己："这个内容是否已经存在？是否可以更新现有文档？"

这样可以确保该技术架构文档始终保持最新和完整，成为火鸟门户系统的权威技术参考资料。