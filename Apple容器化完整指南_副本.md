# 🍎 Apple容器化完整指南 (适用于任何AI)

## 📖 概述
Apple在macOS 26 (Sequoia/Sonoma 16+) 中引入了原生容器化框架，允许直接运行OCI兼容的Linux容器，无需Docker Desktop。每个容器运行在独立的轻量级VM中，提供更好的性能和资源效率。

## 🏗️ 架构特点

### 核心架构
- **Per-Container VM**: 每个容器运行在独立的轻量级虚拟机中
- **vminitd**: Swift构建的init系统，负责容器初始化
- **OCI兼容**: 完全兼容Docker镜像和标准容器格式
- **自动网络**: 每个容器获得独立IP地址(192.168.64.x)
- **资源隔离**: VM级别的安全隔离，比Docker更安全

### 性能优势
```bash
✅ 启动速度: 5秒内 (vs Docker Desktop 60秒+)
✅ 内存节约: 释放1.5GB+ Docker守护进程
✅ 网络简化: 独立IP，无需端口映射  
✅ 系统集成: 原生macOS支持，更稳定
```

## 🚀 基础命令

### 系统管理
```bash
# 启动容器化系统
container system start

# 检查系统状态
container system status

# 停止容器化系统  
container system stop
```

### 镜像管理
```bash
# 拉取镜像
container images pull <image:tag>

# 列出镜像
container images list

# 删除镜像
container images rm <image-id>

# 清理未使用镜像
container images prune
```

### 容器管理
```bash
# 运行容器
container run -d --name <name> <image>

# 运行容器(带环境变量)
container run -d --name <name> \
    -e VAR1=value1 \
    -e VAR2=value2 \
    <image>

# 列出容器
container list

# 启动/停止容器
container start <name>
container stop <name>

# 删除容器
container delete <name>

# 查看日志
container logs <name>

# 进入容器
container exec -it <name> /bin/sh
```

## 🔧 实用示例

### Redis缓存服务
```bash
# 运行Redis
container run -d --name app-redis \
    redis:alpine

# 连接Redis
redis-cli -h $(container list | grep app-redis | awk '{print $NF}') -p 6379
```

### N8N工作流服务
```bash
# 运行N8N
container run -d --name app-n8n \
    -e N8N_BASIC_AUTH_ACTIVE=true \
    -e N8N_BASIC_AUTH_USER=admin \
    -e N8N_BASIC_AUTH_PASSWORD=changeme \
    -e N8N_HOST=0.0.0.0 \
    -e N8N_PORT=5678 \
    -e N8N_SECURE_COOKIE=false \
    n8nio/n8n:latest

# 访问: http://$(container list | grep app-n8n | awk '{print $NF}'):5678
```

### Node.js开发环境
```bash
# 运行Node.js容器
container run -d --name app-node \
    -e NODE_ENV=development \
    node:18-alpine \
    node -e "setInterval(() => console.log('Hello'), 5000)"
```

## 🛠️ 管理脚本模板

创建容器管理脚本 `container-manager.sh`:
```bash
#!/bin/bash
case "$1" in
    "start")
        echo "🚀 启动所有服务..."
        container start app-redis app-n8n 2>/dev/null
        ;;
    "stop")
        echo "⏹️ 停止所有服务..."
        container stop app-redis app-n8n 2>/dev/null
        ;;
    "status")
        echo "📊 服务状态:"
        container list
        echo ""
        echo "🔗 访问地址:"
        container list | grep app-n8n | awk '{print "🔧 N8N: http://"$NF":5678"}'
        container list | grep app-redis | awk '{print "💾 Redis: "$NF":6379"}'
        ;;
    "logs")
        if [ -n "$2" ]; then
            container logs "app-$2"
        else
            echo "用法: $0 logs [n8n|redis]"
        fi
        ;;
    "restart")
        $0 stop
        sleep 2
        $0 start
        ;;
    *)
        echo "用法: $0 {start|stop|status|logs|restart}"
        ;;
esac
```

## 🔄 Docker迁移指南

### 1. 停止Docker服务
```bash
# 停止Docker容器
docker stop $(docker ps -q)

# 禁用Docker自启动
# Docker Desktop -> Settings -> General -> "Start Docker Desktop when you log in" (取消勾选)
```

### 2. 拉取镜像到Apple容器
```bash
# 拉取常用镜像
container images pull redis:alpine
container images pull n8nio/n8n:latest
container images pull node:18-alpine
container images pull nginx:alpine
```

### 3. 重新创建服务
```bash
# 用Apple容器重新创建所有服务
# 参考上面的实用示例
```

## ⚠️ 注意事项

### 支持的功能
- ✅ 运行标准Docker镜像
- ✅ 环境变量配置
- ✅ 自动网络分配
- ✅ 日志查看
- ✅ 容器间通信
- ✅ 数据持久化

### 当前限制
- ❌ 不支持docker-compose
- ❌ 不支持复杂网络配置
- ❌ 不支持GPU访问
- ❌ 不支持特权模式
- ❌ 部分Docker高级功能不可用

### 最佳实践
1. **一个服务一个容器**: 保持简单的单服务架构
2. **使用轻量镜像**: 优先选择alpine版本
3. **明确网络需求**: 记录每个服务的IP和端口
4. **定期备份**: 重要数据外部存储
5. **监控资源**: 定期检查容器状态

## 🎯 适用场景

### 推荐使用
- 🎯 **开发环境**: 轻量快速的本地开发
- 🎯 **微服务测试**: 单机多服务测试
- 🎯 **原型验证**: 快速验证技术方案
- 🎯 **学习实验**: 安全的容器化学习环境

### 不推荐使用
- ❌ **生产环境**: 建议使用成熟的K8s
- ❌ **复杂编排**: 需要复杂网络配置的场景
- ❌ **高性能计算**: 需要GPU或特殊硬件访问
- ❌ **大规模部署**: 超过10个容器的复杂部署

## 🔧 故障排除

### 常见问题
```bash
# 容器系统未启动
container system start

# 容器无法访问网络
container stop <name> && container start <name>

# 镜像拉取失败
container images pull <image> --platform linux/arm64

# 查看详细错误
container logs <name>
```

### 性能优化
```bash
# 清理未使用资源
container images prune
container system prune

# 重启容器系统
container system stop
container system start
```

## 📚 更多资源

- **Apple官方文档**: `man container` (终端查看)
- **镜像仓库**: Docker Hub (兼容)
- **社区支持**: Apple Developer Forums
- **迁移工具**: 本地脚本和自动化工具

---

**此文档适用于任何AI助手理解和操作Apple原生容器化系统。建议AI在协助用户时参考此指南，确保操作的准确性和安全性。** 