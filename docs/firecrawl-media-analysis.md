
# Firecrawl图片和媒体内容处理分析
## HawaiiHub.net多媒体内容采集方案

### 📸 图片爬取能力测试结果

#### ✅ 支持的图片格式和功能
```json
{
  "formats": ["markdown", "html", "screenshot@fullPage"],
  "includeTags": ["img", "picture", "figure"],
  "extractImages": true,
  "imageAltText": true,
  "imageCaptions": true
}
```

#### 🎯 图片处理特性分析

**1. 图片URL提取**
```markdown
# Firecrawl自动提取的图片信息
![Hurricane Iona and Tropical Depression Two-C](https://gray-khnl-prod.gtv-cdn.com/resizer/v2/ZT7XZ5JSHFERPBKNZSZMZXPHCY.png?auth=9577e42a342872070b502cadefd294f139ec0bec8f07241629cce507d1667f11&width=800&height=600&smart=true)

![<PERSON> receives her white coat](https://gray-khnl-prod.gtv-cdn.com/resizer/v2/2GS3A4EUBNGZ3IHNK2GAGR6G7M.jpg?auth=93c50fa36368f87e9441efed5b71d189076964a341a2a5e771ef4b95c3507a33&width=800&height=533&smart=true)
```

**2. Alt文本和描述保留**
- ✅ 自动保留图片的alt属性
- ✅ 提取图片标题和描述
- ✅ 保持图片与文本的关联关系

**3. Credits消耗分析**
```
基础爬取: 1 credit/页面
包含图片: 仍然是 1 credit/页面 (不额外收费)
截图功能: 1 credit/页面 (screenshot@fullPage)
```

### 🖼️ 夏威夷本地新闻图片处理效果

#### Hawaii News Now图片特点
```python
# 从测试结果分析的图片特征
hawaii_news_images = {
    "weather_maps": "高质量天气图表和雷达图",
    "news_photos": "本地新闻现场照片",
    "people_photos": "人物采访和活动照片",
    "landscape": "夏威夷风景和地标照片"
}

# 图片URL模式分析
image_patterns = {
    "cdn_domain": "gray-khnl-prod.gtv-cdn.com",
    "format": "resizer/v2/[hash].jpg|png",
    "parameters": "auth, width, height, smart=true",
    "sizes": ["800x600", "800x533", "800x450"]
}
```

#### 图片质量评估
- **分辨率**: 高质量 (800px+)
- **格式**: JPG/PNG 优化
- **加载速度**: CDN加速
- **移动适配**: 响应式设计

### 🔧 图文并茂内容采集实现

#### 增强的内容结构
```python
class MediaEnhancedContent:
    def __init__(self):
        self.title = ""
        self.content = ""
        self.images = []
        self.videos = []
        self.links = []
    
    def extract_media_from_firecrawl(self, firecrawl_result):
        """从Firecrawl结果中提取媒体内容"""
        markdown_content = firecrawl_result.get('markdown', '')
        
        # 提取图片信息
        import re
        image_pattern = r'!\[(.*?)\]\((.*?)\)'
        images = re.findall(image_pattern, markdown_content)
        
        for alt_text, url in images:
            self.images.append({
                'url': url,
                'alt_text': alt_text,
                'caption': self._extract_caption(alt_text),
                'type': self._classify_image_type(alt_text, url)
            })
        
        return self
    
    def _classify_image_type(self, alt_text, url):
        """分类图片类型"""
        if 'weather' in alt_text.lower() or 'forecast' in alt_text.lower():
            return 'weather'
        elif 'breaking' in alt_text.lower() or 'news' in alt_text.lower():
            return 'breaking_news'
        elif any(name in alt_text.lower() for name in ['receives', 'ceremony', 'award']):
            return 'people_event'
        else:
            return 'general'
```

#### n8n工作流中的图片处理
```javascript
// n8n节点：图片内容增强处理
{
  "name": "Media Content Processor",
  "type": "n8n-nodes-base.function",
  "parameters": {
    "functionCode": `
      // 处理Firecrawl返回的图文内容
      const content = items[0].json;
      const markdown = content.markdown || '';
      
      // 提取图片信息
      const imageRegex = /!\\[(.*?)\\]\\((.*?)\\)/g;
      const images = [];
      let match;
      
      while ((match = imageRegex.exec(markdown)) !== null) {
        images.push({
          alt: match[1],
          url: match[2],
          type: classifyImageType(match[1]),
          priority: calculateImagePriority(match[1], match[2])
        });
      }
      
      // 分类图片类型
      function classifyImageType(altText) {
        const alt = altText.toLowerCase();
        if (alt.includes('weather') || alt.includes('forecast')) return 'weather';
        if (alt.includes('breaking') || alt.includes('urgent')) return 'breaking';
        if (alt.includes('ceremony') || alt.includes('event')) return 'event';
        return 'general';
      }
      
      // 计算图片优先级
      function calculateImagePriority(altText, url) {
        let priority = 1;
        if (altText.toLowerCase().includes('breaking')) priority += 3;
        if (url.includes('800x')) priority += 2; // 高分辨率
        if (altText.length > 20) priority += 1; // 详细描述
        return priority;
      }
      
      return [{
        json: {
          ...content,
          images: images,
          image_count: images.length,
          has_breaking_images: images.some(img => img.type === 'breaking'),
          primary_image: images.find(img => img.priority >= 4) || images[0]
        }
      }];
    `
  }
}
```

### 📱 移动端和响应式处理

#### 图片优化策略
```python
class ImageOptimizer:
    def __init__(self):
        self.size_variants = {
            'thumbnail': '300x200',
            'medium': '600x400', 
            'large': '800x600',
            'hero': '1200x800'
        }
    
    def optimize_for_hawaiihub(self, image_url):
        """为HawaiiHub优化图片"""
        # 检查是否为Hawaii News Now的CDN
        if 'gray-khnl-prod.gtv-cdn.com' in image_url:
            # 利用其CDN的resize功能
            base_url = image_url.split('?')[0]
            return {
                'thumbnail': f"{base_url}?width=300&height=200&smart=true",
                'medium': f"{base_url}?width=600&height=400&smart=true",
                'large': f"{base_url}?width=800&height=600&smart=true"
            }
        
        # 其他图片使用本地处理
        return self._local_resize(image_url)
```

### 🎥 视频内容处理

#### 视频链接提取
```python
def extract_video_content(markdown_content):
    """提取视频内容"""
    video_patterns = [
        r'https://.*?\.mp4',
        r'https://.*?youtube\.com/watch\?v=([a-zA-Z0-9_-]+)',
        r'https://.*?vimeo\.com/(\d+)',
        r'https://.*?\.m3u8'  # HLS流媒体
    ]
    
    videos = []
    for pattern in video_patterns:
        matches = re.findall(pattern, markdown_content)
        videos.extend(matches)
    
    return videos
```

### 💾 媒体内容存储策略

#### 本地存储vs CDN引用
```python
class MediaStorageStrategy:
    def __init__(self):
        self.storage_rules = {
            'breaking_news': 'local_copy',  # 重要新闻本地备份
            'weather': 'cdn_reference',     # 天气图表引用即可
            'events': 'local_copy',         # 社区活动本地保存
            'general': 'cdn_reference'      # 一般图片引用
        }
    
    def decide_storage(self, image_info):
        """决定存储策略"""
        image_type = image_info.get('type', 'general')
        strategy = self.storage_rules.get(image_type, 'cdn_reference')
        
        if strategy == 'local_copy':
            return self._download_and_store(image_info['url'])
        else:
            return image_info['url']
```

### 📊 图片内容分析和分类

#### 与现有AI系统集成
```python
class ImageContentClassifier:
    def __init__(self, hawaii_classifier):
        self.classifier = hawaii_classifier
        self.image_categories = {
            'weather': ['forecast', 'hurricane', 'rain', 'sunny'],
            'news': ['breaking', 'accident', 'crime', 'politics'],
            'community': ['ceremony', 'festival', 'celebration'],
            'nature': ['beach', 'mountain', 'ocean', 'sunset']
        }
    
    def classify_image_content(self, image_info, article_content):
        """结合图片和文章内容进行分类"""
        # 基于alt文本的初步分类
        alt_classification = self._classify_by_alt_text(image_info['alt_text'])
        
        # 结合文章内容的上下文分类
        context_classification = self.classifier.classify_content(
            image_info['alt_text'], 
            article_content
        )
        
        # 综合判断
        return self._merge_classifications(alt_classification, context_classification)
```

### 🔍 图片质量评估

#### 自动质量检测
```python
def assess_image_quality(image_info):
    """评估图片质量"""
    quality_score = 0
    
    # URL质量指标
    if 'width=800' in image_info['url']:
        quality_score += 3
    if 'smart=true' in image_info['url']:
        quality_score += 2
    
    # Alt文本质量
    alt_text = image_info['alt_text']
    if len(alt_text) > 10:
        quality_score += 2
    if any(keyword in alt_text.lower() for keyword in ['hawaii', 'honolulu', 'oahu']):
        quality_score += 1
    
    # 内容相关性
    if image_info['type'] in ['breaking', 'weather', 'event']:
        quality_score += 2
    
    return min(quality_score, 10)  # 最高10分
```

### 📈 性能优化建议

#### 图片处理性能优化
```python
class ImageProcessingOptimizer:
    def __init__(self):
        self.cache = {}
        self.batch_size = 10
    
    def batch_process_images(self, image_list):
        """批量处理图片"""
        results = []
        
        for i in range(0, len(image_list), self.batch_size):
            batch = image_list[i:i + self.batch_size]
            batch_results = self._process_batch(batch)
            results.extend(batch_results)
            
            # 避免过度请求
            time.sleep(1)
        
        return results
    
    def _process_batch(self, batch):
        """处理单个批次"""
        return [self._process_single_image(img) for img in batch]
```

### 🎯 Credits使用优化

#### 图片相关的Credits管理
```python
class MediaCreditsManager:
    def __init__(self):
        self.daily_image_budget = 50  # 每日图片处理预算
        self.priority_threshold = 7   # 高优先级图片阈值
    
    def should_process_image(self, image_info, current_usage):
        """判断是否应该处理这张图片"""
        priority = image_info.get('priority', 1)
        
        # 高优先级图片总是处理
        if priority >= self.priority_threshold:
            return True
        
        # 检查预算
        if current_usage >= self.daily_image_budget:
            return False
        
        return True
```

### 📋 实施建议

#### 立即可执行的步骤
1. **启用图片格式**: 在Firecrawl请求中添加`"formats": ["markdown", "html"]`
2. **图片信息提取**: 使用正则表达式提取图片URL和alt文本
3. **质量评估**: 实施图片质量评分系统
4. **存储策略**: 根据内容类型决定本地存储vs引用
5. **性能监控**: 跟踪图片处理的Credits消耗

#### 与现有系统集成
```python
# 在现有的内容分类器中添加图片支持
def enhanced_classify_content(self, title, content, images=None):
    """增强的内容分类，包含图片信息"""
    base_classification = self.classify_content(title, content)
    
    if images:
        image_context = self._analyze_image_context(images)
        return self._merge_with_image_context(base_classification, image_context)
    
    return base_classification
```

### 快速发现网站所有URL
```python
# 快速发现网站所有URL
map_result = app.map_url("https://hawaiinews.com")
# 返回网站所有可访问页面列表
```

这个图片和媒体处理方案将显著提升HawaiiHub.net的内容丰富度和用户体验！
