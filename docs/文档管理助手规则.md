# 📚 文档管理助手规则
## HawaiiHub.net项目文档管理标准

> **制定时间**: 2025年1月  
> **适用范围**: 所有项目文档  
> **更新频率**: 每季度评估一次

---

## 🎯 核心原则

### 1. 去重原则 (DEDUPLICATION)
- **一个主题，一个权威文档**
- 发现重复内容时，保留最完整、最新的版本
- 删除过时、不完整的重复文档
- 在保留文档中注明整合信息

### 2. 分类原则 (CATEGORIZATION)
- 按功能模块严格分类存放
- 每个目录保持自包含性
- 避免跨目录的内容重复
- 使用清晰的命名规范

### 3. 时效性原则 (TIMELINESS)
- 定期检查文档的时效性
- 标记过时信息并及时更新
- 保留历史版本的价值记录
- 删除完全过时的文档

---

## 📋 文档整理检查清单

### 🔍 发现阶段
- [ ] 扫描所有目录，识别重复文件名
- [ ] 检查相似主题的文档内容
- [ ] 标记过时的时间戳和版本信息
- [ ] 识别孤儿文档（无明确用途）

### 📊 分析阶段
- [ ] 比较重复文档的内容完整性
- [ ] 评估文档的使用频率和重要性
- [ ] 确定每类文档的权威版本
- [ ] 分析目录结构的合理性

### 🗂️ 整理阶段
- [ ] 保留权威版本，删除重复文档
- [ ] 将独特内容合并到主文档中
- [ ] 更新文档间的交叉引用
- [ ] 调整目录结构和文件位置

### ✅ 验证阶段
- [ ] 确认所有链接和引用正常
- [ ] 验证文档结构的逻辑性
- [ ] 检查是否遗漏重要信息
- [ ] 更新README和索引文档

---

## 🏗️ 目录结构标准

### 推荐结构
```
docs/
├── 01_系统管理/          # 系统配置和管理
├── 02_API接口/           # API开发文档
├── 03_功能模块/          # 功能模块说明
├── 04_开发文档/          # 开发指南和规范
├── 05_用户指南/          # 用户使用手册
├── 06_采集插件/          # 数据采集工具
├── backend-configuration/ # 后台配置专项
├── 项目进展记录/          # 阶段性部署文档
└── 专项技术方案/         # 如Firecrawl等技术方案
```

### 命名规范
- **中文文档**: 使用清晰的中文描述
- **技术文档**: 英文+中文组合，如`firecrawl-overview.md`
- **版本标识**: 避免在文件名中使用版本号，用文档内容标识
- **时间标识**: 重要的时间节点可在文档内标注

---

## 🚫 禁止行为

### 严禁操作
- ❌ 删除系统核心配置文件
- ❌ 删除数据库结构定义文件
- ❌ 删除正在使用的API文档
- ❌ 删除用户手册的唯一版本

### 谨慎操作
- ⚠️ 合并不同作者的文档时需确认
- ⚠️ 删除历史记录文档前需评估价值
- ⚠️ 移动文档位置前需检查引用关系
- ⚠️ 重命名文档前需考虑外部链接

---

## 🔄 定期维护流程

### 月度检查 (每月1日)
1. 扫描新增文档，检查是否有重复
2. 更新文档的时效性标记
3. 检查README和索引的准确性
4. 统计文档数量变化

### 季度整理 (每季度末)
1. 全面的重复内容检查
2. 目录结构优化评估
3. 过时文档清理
4. 文档使用情况分析

### 年度归档 (每年末)
1. 重要历史文档归档
2. 文档管理规则更新
3. 年度文档统计报告
4. 下一年度规划制定

---

## 📈 质量指标

### 文档健康度指标
- **重复率**: < 5% (重复文档数/总文档数)
- **过时率**: < 10% (过时文档数/总文档数)
- **完整性**: > 90% (有效文档数/应有文档数)
- **可用性**: > 95% (可正常访问的文档比例)

### 整理效果评估
- 文档数量变化趋势
- 用户查找文档的效率
- 文档维护工作量
- 文档质量反馈

---

## 🛠️ 工具和方法

### 推荐工具
- **文档扫描**: 使用`find`命令查找重复文件名
- **内容比较**: 使用`diff`或专业比较工具
- **链接检查**: 定期检查文档间的引用关系
- **版本控制**: 利用Git跟踪文档变更

### 自动化脚本
```bash
# 查找重复文件名
find docs/ -name "*.md" | sort | uniq -d

# 统计文档数量
find docs/ -name "*.md" | wc -l

# 检查空文档
find docs/ -name "*.md" -size 0
```

---

## 📞 执行责任

### 文档管理员职责
- 执行定期检查和整理
- 制定和更新管理规则
- 培训团队成员遵守规范
- 处理文档冲突和争议

### 团队成员职责
- 创建文档时检查是否重复
- 更新文档时保持格式规范
- 发现问题及时报告
- 配合文档整理工作

---

## 📝 记录要求

### 整理记录
每次文档整理需记录：
- 整理时间和执行人
- 删除的文档列表和原因
- 合并的文档说明
- 移动或重命名的记录

### 变更日志
在README中维护变更日志：
- 重大结构调整
- 重要文档的增删
- 规则的更新修订
- 问题和解决方案

---

## 🎯 成功标准

文档管理成功的标志：
- ✅ 团队成员能快速找到所需文档
- ✅ 文档内容准确、及时、完整
- ✅ 没有明显的重复和冗余
- ✅ 文档结构清晰、逻辑合理
- ✅ 维护工作量可控且高效

---

**最后更新**: 2025年1月  
**下次评估**: 2025年4月
