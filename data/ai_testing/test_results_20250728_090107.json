[{"test_name": "智能分类准确性测试", "start_time": 1753729266.748039, "end_time": 1753729266.777637, "duration": 0.02959299087524414, "memory_usage_mb": 117.5625, "cpu_usage_percent": 95.9, "success_count": 0, "total_count": 50, "accuracy_rate": 0.0, "error_messages": ["处理文章 1753722365092681 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342885 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342884 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342883 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342882 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342881 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342880 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342879 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342878 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342877 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342876 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342875 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342874 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342873 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342872 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342871 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342870 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342869 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342868 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342867 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342866 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342865 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753722365092680 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342864 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342863 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753722365092679 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753722365092678 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342862 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342861 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342860 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342859 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753722365092677 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753722365092676 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342858 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753722365092675 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342857 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342856 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342855 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342854 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753722365092674 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342853 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753722365092673 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753722365092672 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342852 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342851 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342850 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342849 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753723281342848 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753722365092671 时出错: argument of type 'ClassificationResult' is not iterable", "处理文章 1753722365092670 时出错: argument of type 'ClassificationResult' is not iterable"], "additional_data": {"classification_results": [], "avg_processing_time": 0.0005918598175048828}}, {"test_name": "重复检测准确性测试", "start_time": 1753729266.778021, "end_time": 1753729267.5721412, "duration": 0.794111967086792, "memory_usage_mb": 194.796875, "cpu_usage_percent": 98.8, "success_count": 3, "total_count": 50, "accuracy_rate": 12.0, "error_messages": [], "additional_data": {"total_groups": 3, "total_duplicates": 6, "detection_rate": 12.0, "group_sizes": [2, 2, 2], "avg_similarity_scores": [0.5508828084418698, 0.5870914779059168, 0.5872741996210733]}}]