{"timestamp": "2025-07-28T07:50:36.950817", "total_groups": 3, "total_duplicates": 6, "config": {"fingerprint_threshold": 0.6, "semantic_threshold": 0.5, "overall_threshold": 0.55, "title_weight": 0.4, "content_weight": 0.6, "merge_strategy": "keep_latest", "min_content_length": 50, "max_similarity_candidates": 100, "detection_settings": {"enable_fingerprint_detection": true, "enable_semantic_detection": true, "enable_title_similarity": true, "enable_content_similarity": true}, "quality_scoring": {"content_length_weight": 0.3, "title_quality_weight": 0.2, "source_credibility_weight": 0.3, "publish_time_weight": 0.2}, "trusted_sources": ["hawaii news now", "honolulu star-advertiser", "hawaii tribune-herald", "west hawaii today", "maui news", "garden island"], "language_settings": {"auto_detect": true, "supported_languages": ["zh", "en"], "chinese_segmentation": true}}, "groups": [{"group_id": "dup_20250728_075036_0", "article_count": 2, "similarity_scores": [0.5508828084418698], "merge_strategy": "keep_latest", "articles": [{"id": "1753722365092681", "title": "希望可以对 https://aicoding.juejin.cn/ 进行RSS订阅", "content": "类型 编程 网站地址 https://aicoding.juejin.cn/ 网站描述 掘金的AI编程 需要生成什么内容？ 根据热门、最新、还有标签，进行RSS输出 额外描述 No response 这不是重复的 RSS 请求 [x] 我已经搜索了现有 issue 和 pull requests，以确保该 RSS 尚未被请求。", "url": "https://github.com/DIYgod/RSSHub/issues/19701", "source": ";rocona", "publish_date": 1753697912}, {"id": "1753722365092672", "title": "增加bangdream live/event订阅", "content": "类型 二次元 网站地址 https://bang-dream.com/events 网站描述 bangdream官方演唱会资讯 需要生成什么内容？ 各场演唱会的公演名、日程、会场、出演者、票务信息 额外描述 No response 这不是重复的 RSS 请求 [x] 我已经搜索了现有 issue 和 pull requests，以确保该 RSS 尚未被请求。", "url": "https://github.com/DIYgod/RSSHub/issues/19598", "source": ";SlightDust", "publish_date": 1752407458}], "primary_article": {"id": "1753722365092681", "title": "希望可以对 https://aicoding.juejin.cn/ 进行RSS订阅", "content": "类型 编程 网站地址 https://aicoding.juejin.cn/ 网站描述 掘金的AI编程 需要生成什么内容？ 根据热门、最新、还有标签，进行RSS输出 额外描述 No response 这不是重复的 RSS 请求 [x] 我已经搜索了现有 issue 和 pull requests，以确保该 RSS 尚未被请求。", "url": "https://github.com/DIYgod/RSSHub/issues/19701", "source": ";rocona", "publish_date": 1753697912}, "merged_content": {"id": "merged_dup_20250728_075036_0", "title": "希望可以对 https://aicoding.juejin.cn/ 进行RSS订阅", "content": "类型 编程 网站地址 https://aicoding.juejin.cn/ 网站描述 掘金的AI编程 需要生成什么内容？ 根据热门、最新、还有标签，进行RSS输出 额外描述 No response 这不是重复的 RSS 请求 [x] 我已经搜索了现有 issue 和 pull requests，以确保该 RSS 尚未被请求。", "url": "https://github.com/DIYgod/RSSHub/issues/19701", "source": ";rocona", "publish_date": 1753697912, "merged_from": ["1753722365092681", "1753722365092672"], "merge_strategy": "keep_latest", "duplicate_count": 2}}, {"group_id": "dup_20250728_075036_22", "article_count": 2, "similarity_scores": [0.5870914779059168], "merge_strategy": "keep_latest", "articles": [{"id": "1753722365092680", "title": "蓝点网 403 Forbidden", "content": "路由地址 /landiannews/ 完整路由地址 /landiannews 相关文档 https://docs.rsshub.app/zh/routes/new-media#首页-7 预期是什么？ 正常获取内容 实际发生了什么？ FetchError: [GET] \"https://www.landiannews.com/wp-json/wp/v2/posts?_embed\": 403 Forbidden 部署 RSSHub 演示 (https://rsshub.app) 部署相关信息 No response 额外信息 自建同样的报错 这不是重复的 issue [x] 我已经搜索了 现有 issue，以确保该错误尚未被报告。", "url": "https://github.com/DIYgod/RSSHub/issues/19684", "source": ";LinxHex", "publish_date": 1753348393}, {"id": "1753722365092675", "title": "百度精品帖子403", "content": "路由地址 /baidu/tieba/forum/good/:kw/:cid?/:sortBy? 完整路由地址 /baidu/tieba/forum/good/女图 相关文档 https://docs.rsshub.app/zh/routes/bbs#精品帖子 预期是什么？ 应能正常获取对应百度贴吧结果 实际发生了什么？ 访问该路由无法获取贴吧结果，返回403，尝试多个实例和自建均复现 部署 RSSHub 演示 (https://rsshub.app) 部署相关信息 No response 额外信息 FetchError: [GET] \"https://tieba.baidu.com/f?kw=%25E5%25A5%25B3%25E5%259B%25BE&amp;tab=good&amp;cid=0\": 403 Forbidden 这不是重复的 issue [x] 我已经搜索了 现有 issue，以确保该错误尚未被报告。", "url": "https://github.com/DIYgod/RSSHub/issues/19642", "source": ";ljc1357", "publish_date": 1752948719}], "primary_article": {"id": "1753722365092680", "title": "蓝点网 403 Forbidden", "content": "路由地址 /landiannews/ 完整路由地址 /landiannews 相关文档 https://docs.rsshub.app/zh/routes/new-media#首页-7 预期是什么？ 正常获取内容 实际发生了什么？ FetchError: [GET] \"https://www.landiannews.com/wp-json/wp/v2/posts?_embed\": 403 Forbidden 部署 RSSHub 演示 (https://rsshub.app) 部署相关信息 No response 额外信息 自建同样的报错 这不是重复的 issue [x] 我已经搜索了 现有 issue，以确保该错误尚未被报告。", "url": "https://github.com/DIYgod/RSSHub/issues/19684", "source": ";LinxHex", "publish_date": 1753348393}, "merged_content": {"id": "merged_dup_20250728_075036_22", "title": "蓝点网 403 Forbidden", "content": "路由地址 /landiannews/ 完整路由地址 /landiannews 相关文档 https://docs.rsshub.app/zh/routes/new-media#首页-7 预期是什么？ 正常获取内容 实际发生了什么？ FetchError: [GET] \"https://www.landiannews.com/wp-json/wp/v2/posts?_embed\": 403 Forbidden 部署 RSSHub 演示 (https://rsshub.app) 部署相关信息 No response 额外信息 自建同样的报错 这不是重复的 issue [x] 我已经搜索了 现有 issue，以确保该错误尚未被报告。", "url": "https://github.com/DIYgod/RSSHub/issues/19684", "source": ";LinxHex", "publish_date": 1753348393, "merged_from": ["1753722365092680", "1753722365092675"], "merge_strategy": "keep_latest", "duplicate_count": 2}}, {"group_id": "dup_20250728_075036_31", "article_count": 2, "similarity_scores": [0.5872741996210733], "merge_strategy": "keep_latest", "articles": [{"id": "1753722365092677", "title": "Add rss feed for naturalism.org", "content": "Category Social Media Website URL https://naturalism.org/ Website description A philosophy website on naturalism. What content should be included? Recent posts, if feasible then rss by search Additional description the current rss feed is no longer updated/is-empty This is not a duplicated rss request [x] I have searched existing issues and pull requests to ensure this rss proposal has not already been requested", "url": "https://github.com/DIYgod/RSSHub/issues/19644", "source": ";FallCheetah7373", "publish_date": 1753030562}, {"id": "1753722365092676", "title": "Add rss feed for teamblind.com", "content": "Category Social Media Website URL https://www.teamblind.com/ Website description Anonymous community with worksplace verification. What content should be included? Popular feeds and feeds by channel Additional description There's no rss available This is not a duplicated rss request [x] I have searched existing issues and pull requests to ensure this rss proposal has not already been requested", "url": "https://github.com/DIYgod/RSSHub/issues/19643", "source": ";FallCheetah7373", "publish_date": 1753030411}], "primary_article": {"id": "1753722365092677", "title": "Add rss feed for naturalism.org", "content": "Category Social Media Website URL https://naturalism.org/ Website description A philosophy website on naturalism. What content should be included? Recent posts, if feasible then rss by search Additional description the current rss feed is no longer updated/is-empty This is not a duplicated rss request [x] I have searched existing issues and pull requests to ensure this rss proposal has not already been requested", "url": "https://github.com/DIYgod/RSSHub/issues/19644", "source": ";FallCheetah7373", "publish_date": 1753030562}, "merged_content": {"id": "merged_dup_20250728_075036_31", "title": "Add rss feed for naturalism.org", "content": "Category Social Media Website URL https://naturalism.org/ Website description A philosophy website on naturalism. What content should be included? Recent posts, if feasible then rss by search Additional description the current rss feed is no longer updated/is-empty This is not a duplicated rss request [x] I have searched existing issues and pull requests to ensure this rss proposal has not already been requested", "url": "https://github.com/DIYgod/RSSHub/issues/19644", "source": ";FallCheetah7373", "publish_date": 1753030562, "merged_from": ["1753722365092677", "1753722365092676"], "merge_strategy": "keep_latest", "duplicate_count": 2}}]}