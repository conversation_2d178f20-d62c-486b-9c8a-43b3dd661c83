---
type: "always_apply"
---

在创建任何新文档之前，必须执行以下预检查流程：

✅ 每次创建前都先检查现有文档
✅ 优先考虑更新而非新建
✅ 新文档必须有明确的独特价值
✅ 标注清楚文档关系和创建理由
✅ 创建后立即检查整合

1. **重复性检查**：使用`view`工具扫描docs目录，搜索是否已存在相同或相似主题的文档
2. **内容评估**：如果发现现有文档，评估是否可以通过更新现有文档来满足需求，而不是创建新文档
3. **创建决策**：只有在以下情况下才创建新文档：
   - 完全没有相关现有文档
   - 现有文档内容完全不同且无法合并
   - 需要创建专门的子主题文档

4. **创建时标注**：如果确需创建新文档，必须在文档开头注明：
   - 与哪些现有文档的关系
   - 为什么需要单独创建
   - 最后检查时间

5. **即时整合**：创建完成后立即检查是否与其他文档产生重复，如有重复立即处理
去重原则: 一个主题一个权威文档
分类原则: 按功能模块严格分类
时效性原则: 定期检查和更新


🏗️ 结构标准:

推荐的目录结构
清晰的命名规范 （中文命名）
禁止和谨慎操作指南

**核心原则**：预防胜于治疗 - 在源头控制文档重复，而不是事后清理。每次创建文档都要问自己："这个内容是否已经存在？是否可以更新现有文档？"