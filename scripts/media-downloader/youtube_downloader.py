#!/usr/bin/env python3
"""
夏威夷华人平台 - YouTube视频下载器
支持YouTube、TikTok等平台的视频下载
"""

import os
import sys
import json
import logging
import subprocess
from datetime import datetime
from pathlib import Path

# 确保日志目录存在
log_dir = Path("media/videos")
log_dir.mkdir(parents=True, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'download.log'),
        logging.StreamHandler()
    ]
)

class MediaDownloader:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent.parent
        self.download_dir = self.base_dir / "media" / "videos"
        self.config_file = self.base_dir / "scripts" / "media-downloader" / "download_config.json"
        
        # 确保目录存在
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        # 默认配置
        self.default_config = {
            "channels": [
                {
                    "name": "Hawaii News Now",
                    "url": "https://www.youtube.com/@HawaiiNewsNow",
                    "category": "hawaii_news",
                    "max_videos": 5
                },
                {
                    "name": "KHON2 News",
                    "url": "https://www.youtube.com/@KHON2",
                    "category": "hawaii_news", 
                    "max_videos": 5
                }
            ],
            "quality": "best[height<=720]",
            "format": "mp4",
            "subtitle": True,
            "max_file_size": "100M"
        }
        
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        else:
            self.config = self.default_config
            self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def download_video(self, url, category="general"):
        """下载单个视频"""
        try:
            # 创建分类目录
            category_dir = self.download_dir / category
            category_dir.mkdir(exist_ok=True)
            
            # yt-dlp命令参数
            cmd = [
                "yt-dlp",
                "--format", self.config.get("quality", "best[height<=720]"),
                "--output", str(category_dir / "%(uploader)s_%(title)s_%(id)s.%(ext)s"),
                "--write-info-json",
                "--write-description",
                "--max-filesize", self.config.get("max_file_size", "100M"),
                "--no-playlist"
            ]
            
            # 添加字幕选项
            if self.config.get("subtitle", True):
                cmd.extend(["--write-subs", "--write-auto-subs", "--sub-lang", "en,zh"])
            
            cmd.append(url)
            
            logging.info(f"开始下载: {url}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logging.info(f"下载成功: {url}")
                return True
            else:
                logging.error(f"下载失败: {url}, 错误: {result.stderr}")
                return False
                
        except Exception as e:
            logging.error(f"下载异常: {url}, 错误: {str(e)}")
            return False
    
    def download_channel_latest(self, channel_config):
        """下载频道最新视频"""
        try:
            max_videos = channel_config.get("max_videos", 5)
            category = channel_config.get("category", "general")
            url = channel_config["url"]
            
            # 获取频道最新视频列表
            cmd = [
                "yt-dlp",
                "--flat-playlist",
                "--playlist-end", str(max_videos),
                "--print", "%(webpage_url)s",
                url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                video_urls = result.stdout.strip().split('\n')
                logging.info(f"找到 {len(video_urls)} 个视频从频道: {channel_config['name']}")
                
                success_count = 0
                for video_url in video_urls:
                    if video_url.strip():
                        if self.download_video(video_url.strip(), category):
                            success_count += 1
                
                logging.info(f"频道 {channel_config['name']} 下载完成: {success_count}/{len(video_urls)}")
                return success_count
            else:
                logging.error(f"获取频道视频列表失败: {channel_config['name']}")
                return 0
                
        except Exception as e:
            logging.error(f"下载频道异常: {channel_config['name']}, 错误: {str(e)}")
            return 0
    
    def download_all_channels(self):
        """下载所有配置的频道"""
        total_downloaded = 0
        
        for channel in self.config.get("channels", []):
            downloaded = self.download_channel_latest(channel)
            total_downloaded += downloaded
        
        logging.info(f"批量下载完成，总共下载 {total_downloaded} 个视频")
        return total_downloaded
    
    def cleanup_old_files(self, days=7):
        """清理旧文件"""
        try:
            import time
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            
            deleted_count = 0
            for file_path in self.download_dir.rglob("*"):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1
            
            logging.info(f"清理完成，删除了 {deleted_count} 个旧文件")
            return deleted_count
            
        except Exception as e:
            logging.error(f"清理文件异常: {str(e)}")
            return 0

def main():
    """主函数"""
    downloader = MediaDownloader()
    
    if len(sys.argv) > 1:
        # 下载指定URL
        url = sys.argv[1]
        category = sys.argv[2] if len(sys.argv) > 2 else "manual"
        downloader.download_video(url, category)
    else:
        # 下载所有配置的频道
        downloader.download_all_channels()
        # 清理7天前的文件
        downloader.cleanup_old_files(7)

if __name__ == "__main__":
    main()
