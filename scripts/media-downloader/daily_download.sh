#!/bin/bash

# 夏威夷华人平台 - 每日视频下载脚本
# 用于定时执行视频下载任务

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"

# 设置日志文件
LOG_FILE="$PROJECT_DIR/media/videos/daily_download.log"

# 记录开始时间
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始每日视频下载任务" >> "$LOG_FILE"

# 切换到项目目录
cd "$PROJECT_DIR"

# 激活Python虚拟环境（如果存在）
if [ -d "venv" ]; then
    source venv/bin/activate
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 激活虚拟环境" >> "$LOG_FILE"
fi

# 执行Python下载脚本
python3 "$SCRIPT_DIR/youtube_downloader.py" >> "$LOG_FILE" 2>&1

# 记录完成时间
echo "$(date '+%Y-%m-%d %H:%M:%S') - 每日视频下载任务完成" >> "$LOG_FILE"
echo "----------------------------------------" >> "$LOG_FILE"

# 检查磁盘空间，如果超过80%则清理更多文件
DISK_USAGE=$(df "$PROJECT_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 磁盘使用率 ${DISK_USAGE}%，开始清理旧文件" >> "$LOG_FILE"
    python3 -c "
from scripts.media_downloader.youtube_downloader import MediaDownloader
downloader = MediaDownloader()
downloader.cleanup_old_files(3)  # 清理3天前的文件
" >> "$LOG_FILE" 2>&1
fi
