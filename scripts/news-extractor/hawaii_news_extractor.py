#!/usr/bin/env python3
"""
夏威夷华人平台 - 新闻内容提取器
使用Newspaper3k提取夏威夷本地新闻网站内容
"""

import os
import sys
import json
import logging
import requests
from datetime import datetime
from pathlib import Path
from newspaper import Article, Source
from bs4 import BeautifulSoup

# 确保日志目录存在
log_dir = Path("data/logs")
log_dir.mkdir(parents=True, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'news_extraction.log'),
        logging.StreamHandler()
    ]
)

class HawaiiNewsExtractor:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent.parent
        self.output_dir = self.base_dir / "data" / "extracted_news"
        self.config_file = self.base_dir / "scripts" / "news-extractor" / "news_sources.json"
        
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 默认新闻源配置
        self.default_sources = {
            "hawaii_sources": [
                {
                    "name": "Hawaii News Now",
                    "url": "https://www.hawaiinewsnow.com",
                    "category": "hawaii_local",
                    "language": "en"
                },
                {
                    "name": "Honolulu Star-Advertiser",
                    "url": "https://www.staradvertiser.com",
                    "category": "hawaii_local", 
                    "language": "en"
                },
                {
                    "name": "Hawaii Tribune-Herald",
                    "url": "https://www.hawaiitribune-herald.com",
                    "category": "hawaii_local",
                    "language": "en"
                },
                {
                    "name": "West Hawaii Today",
                    "url": "https://www.westhawaiitoday.com",
                    "category": "hawaii_local",
                    "language": "en"
                }
            ],
            "chinese_sources": [
                {
                    "name": "BBC中文",
                    "url": "https://www.bbc.com/zhongwen/simp",
                    "category": "international_chinese",
                    "language": "zh"
                },
                {
                    "name": "CNN中文",
                    "url": "https://edition.cnn.com/china",
                    "category": "international_chinese", 
                    "language": "zh"
                }
            ]
        }
        
        self.load_config()
    
    def load_config(self):
        """加载新闻源配置"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.sources = json.load(f)
        else:
            self.sources = self.default_sources
            self.save_config()
    
    def save_config(self):
        """保存新闻源配置"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.sources, f, indent=2, ensure_ascii=False)
    
    def extract_article(self, url):
        """提取单篇文章内容"""
        try:
            article = Article(url)
            article.download()
            article.parse()
            article.nlp()
            
            # 提取文章信息
            article_data = {
                "url": url,
                "title": article.title,
                "authors": article.authors,
                "publish_date": article.publish_date.isoformat() if article.publish_date else None,
                "text": article.text,
                "summary": article.summary,
                "keywords": article.keywords,
                "top_image": article.top_image,
                "movies": article.movies,
                "meta_keywords": article.meta_keywords,
                "meta_description": article.meta_description,
                "canonical_link": article.canonical_link,
                "extracted_at": datetime.now().isoformat()
            }
            
            logging.info(f"成功提取文章: {article.title[:50]}...")
            return article_data
            
        except Exception as e:
            logging.error(f"提取文章失败 {url}: {str(e)}")
            return None
    
    def extract_source_articles(self, source_config, max_articles=10):
        """提取新闻源的文章"""
        try:
            source_url = source_config["url"]
            source_name = source_config["name"]
            category = source_config["category"]
            
            logging.info(f"开始提取新闻源: {source_name}")
            
            # 创建新闻源对象
            source = Source(source_url)
            source.build()
            
            articles_data = []
            article_count = 0
            
            for article_url in source.article_urls()[:max_articles]:
                if article_count >= max_articles:
                    break
                    
                article_data = self.extract_article(article_url)
                if article_data:
                    article_data["source_name"] = source_name
                    article_data["source_url"] = source_url
                    article_data["category"] = category
                    articles_data.append(article_data)
                    article_count += 1
            
            logging.info(f"从 {source_name} 提取了 {len(articles_data)} 篇文章")
            return articles_data
            
        except Exception as e:
            logging.error(f"提取新闻源失败 {source_config['name']}: {str(e)}")
            return []
    
    def extract_all_sources(self, max_articles_per_source=5):
        """提取所有配置的新闻源"""
        all_articles = []
        
        # 提取夏威夷本地新闻
        for source in self.sources.get("hawaii_sources", []):
            articles = self.extract_source_articles(source, max_articles_per_source)
            all_articles.extend(articles)
        
        # 提取中文新闻
        for source in self.sources.get("chinese_sources", []):
            articles = self.extract_source_articles(source, max_articles_per_source)
            all_articles.extend(articles)
        
        return all_articles
    
    def save_articles(self, articles, filename=None):
        """保存提取的文章到JSON文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"extracted_articles_{timestamp}.json"
        
        output_file = self.output_dir / filename
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(articles, f, indent=2, ensure_ascii=False)
        
        logging.info(f"保存了 {len(articles)} 篇文章到 {output_file}")
        return output_file
    
    def generate_rss_feed(self, articles, filename=None):
        """生成RSS feed文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"hawaii_news_feed_{timestamp}.xml"
        
        output_file = self.output_dir / filename
        
        # RSS模板
        rss_template = '''<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0">
<channel>
<title>夏威夷华人平台 - 本地新闻聚合</title>
<description>夏威夷本地新闻和华人社区资讯</description>
<link>https://hawaiihub.net</link>
<lastBuildDate>{build_date}</lastBuildDate>
{items}
</channel>
</rss>'''
        
        # 生成RSS项目
        items = []
        for article in articles[:20]:  # 限制20篇最新文章
            item = f'''<item>
<title><![CDATA[{article.get('title', '无标题')}]]></title>
<description><![CDATA[{article.get('summary', article.get('text', '')[:200])}]]></description>
<link>{article.get('url', '')}</link>
<pubDate>{article.get('publish_date', article.get('extracted_at', ''))}</pubDate>
<source>{article.get('source_name', '未知来源')}</source>
</item>'''
            items.append(item)
        
        # 生成完整RSS
        rss_content = rss_template.format(
            build_date=datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0000"),
            items='\n'.join(items)
        )
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(rss_content)
        
        logging.info(f"生成RSS feed: {output_file}")
        return output_file

def main():
    """主函数"""
    extractor = HawaiiNewsExtractor()
    
    if len(sys.argv) > 1:
        # 提取指定URL
        url = sys.argv[1]
        article = extractor.extract_article(url)
        if article:
            extractor.save_articles([article], "single_article.json")
    else:
        # 提取所有配置的新闻源
        articles = extractor.extract_all_sources(max_articles_per_source=3)
        if articles:
            # 保存JSON格式
            extractor.save_articles(articles)
            # 生成RSS feed
            extractor.generate_rss_feed(articles)
            logging.info(f"提取完成，总共处理了 {len(articles)} 篇文章")
        else:
            logging.warning("没有提取到任何文章")

if __name__ == "__main__":
    main()
