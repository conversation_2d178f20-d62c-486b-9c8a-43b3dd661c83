#!/bin/bash

# HawaiiHub.net Firecrawl集成测试启动脚本
# 作者: AI运营官
# 日期: 2025-01-28

echo "🌺 HawaiiHub.net Firecrawl集成测试启动脚本"
echo "================================================"

# 检查Python环境
echo "🐍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装，请先安装Python3"
    exit 1
fi

PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
echo "✅ Python版本: $PYTHON_VERSION"

# 检查必要的包
echo "📦 检查必要的Python包..."
REQUIRED_PACKAGES=("firecrawl" "pydantic" "requests" "asyncio")

for package in "${REQUIRED_PACKAGES[@]}"; do
    if python3 -c "import $package" 2>/dev/null; then
        echo "✅ $package 已安装"
    else
        echo "❌ $package 未安装，正在安装..."
        pip3 install $package
    fi
done

# 检查环境变量
echo "🔑 检查环境变量..."
if [ -z "$FIRECRAWL_API_KEY" ]; then
    echo "⚠️ FIRECRAWL_API_KEY环境变量未设置，使用默认密钥"
    export FIRECRAWL_API_KEY="fc-0a2c801f433d4718bcd8189f2742edf4"
fi

# 检查AI服务状态
echo "🤖 检查AI服务状态..."
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ AI分类服务运行正常"
else
    echo "⚠️ AI分类服务未运行，部分测试可能失败"
fi

if curl -s http://localhost:8001/health > /dev/null 2>&1; then
    echo "✅ 重复检测服务运行正常"
else
    echo "⚠️ 重复检测服务未运行，部分测试可能失败"
fi

# 创建测试结果目录
echo "📁 创建测试结果目录..."
mkdir -p test_results
mkdir -p logs

# 运行集成测试
echo "🚀 开始运行Firecrawl集成测试..."
echo "================================================"

# 设置日志文件
LOG_FILE="logs/integration_test_$(date +%Y%m%d_%H%M%S).log"

# 运行测试并记录日志
python3 test_complete_integration.py 2>&1 | tee "$LOG_FILE"

# 检查测试结果
TEST_EXIT_CODE=${PIPESTATUS[0]}

echo "================================================"
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "🎉 集成测试完成！"
    echo "📊 测试结果已保存到: $LOG_FILE"
    
    # 查找生成的测试报告
    REPORT_FILE=$(ls -t test_report_*.json 2>/dev/null | head -1)
    if [ -n "$REPORT_FILE" ]; then
        echo "📋 详细报告: $REPORT_FILE"
        
        # 显示测试摘要
        echo "📈 测试摘要:"
        python3 -c "
import json
try:
    with open('$REPORT_FILE', 'r') as f:
        report = json.load(f)
    summary = report['test_summary']
    print(f\"   总测试数: {summary['total_tests']}\")
    print(f\"   通过测试: {summary['passed_tests']}\")
    print(f\"   失败测试: {summary['failed_tests']}\")
    print(f\"   成功率: {summary['success_rate_percentage']:.1f}%\")
    print(f\"   总耗时: {summary['total_duration_seconds']:.2f}秒\")
except Exception as e:
    print(f\"无法解析测试报告: {e}\")
"
    fi
else
    echo "❌ 集成测试失败，退出码: $TEST_EXIT_CODE"
    echo "📋 请查看日志文件了解详情: $LOG_FILE"
fi

# 清理临时文件
echo "🧹 清理临时文件..."
# 这里可以添加清理逻辑，如果需要的话

echo "✨ 测试脚本执行完成"
exit $TEST_EXIT_CODE
