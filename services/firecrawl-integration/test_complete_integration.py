#!/usr/bin/env python3
"""
HawaiiHub.net Firecrawl完整集成测试
验证所有功能模块的协同工作

作者: AI运营官
日期: 2025-01-28
版本: 1.0.0
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
import logging

# 导入我们的集成系统
from hawaiihub_firecrawl_implementation import (
    HawaiiFirecrawlIntegration, 
    HawaiiLocalContentProcessor,
    N8NFirecrawlWorkflowGenerator
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CompleteIntegrationTester:
    """完整集成测试器"""
    
    def __init__(self):
        self.firecrawl = HawaiiFirecrawlIntegration()
        self.hawaiian_processor = HawaiiLocalContentProcessor()
        self.workflow_generator = N8NFirecrawlWorkflowGenerator(
            "fc-0a2c801f433d4718bcd8189f2742edf4"
        )
        
        # 测试URL集合
        self.test_urls = {
            'hawaii_news': [
                'https://www.hawaiinewsnow.com',
                'https://www.staradvertiser.com'
            ],
            'job_sites': [
                'https://www.indeed.com/jobs?q=&l=Hawaii',
                'https://honolulu.craigslist.org/search/jjj'
            ],
            'community': [
                'https://www.gohawaii.com/events',
                'https://www.hawaiimagazine.com'
            ]
        }
        
        self.test_results = {
            'start_time': datetime.now(),
            'tests_completed': [],
            'tests_failed': [],
            'performance_metrics': {},
            'quality_metrics': {},
            'integration_status': {}
        }

    async def run_complete_test_suite(self):
        """运行完整测试套件"""
        logger.info("🚀 开始HawaiiHub.net Firecrawl完整集成测试")
        
        try:
            # 1. 基础功能测试
            await self.test_basic_scraping()
            
            # 2. 图片处理测试
            await self.test_image_processing()
            
            # 3. 夏威夷本地化测试
            await self.test_hawaiian_localization()
            
            # 4. AI集成测试
            await self.test_ai_integration()
            
            # 5. 批量处理测试
            await self.test_batch_processing()
            
            # 6. n8n工作流测试
            await self.test_n8n_workflows()
            
            # 7. 性能和监控测试
            await self.test_performance_monitoring()
            
            # 8. 错误处理测试
            await self.test_error_handling()
            
            # 生成最终报告
            await self.generate_final_report()
            
        except Exception as e:
            logger.error(f"测试套件执行失败: {str(e)}")
            self.test_results['tests_failed'].append({
                'test': 'complete_test_suite',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })

    async def test_basic_scraping(self):
        """测试基础爬取功能"""
        logger.info("📄 测试基础爬取功能...")
        
        test_url = self.test_urls['hawaii_news'][0]
        start_time = time.time()
        
        try:
            result = self.firecrawl.scrape_hawaii_content(test_url)
            end_time = time.time()
            
            if result and 'processed_data' in result:
                self.test_results['tests_completed'].append({
                    'test': 'basic_scraping',
                    'status': 'PASSED',
                    'response_time': end_time - start_time,
                    'content_length': len(result['processed_data'].get('structured', {}).get('content', '')),
                    'images_found': len(result['processed_data'].get('images', [])),
                    'timestamp': datetime.now().isoformat()
                })
                logger.info(f"✅ 基础爬取测试通过 - 响应时间: {end_time - start_time:.2f}秒")
            else:
                raise ValueError("爬取结果为空或格式错误")
                
        except Exception as e:
            self.test_results['tests_failed'].append({
                'test': 'basic_scraping',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            logger.error(f"❌ 基础爬取测试失败: {str(e)}")

    async def test_image_processing(self):
        """测试图片处理功能"""
        logger.info("🖼️ 测试图片处理功能...")
        
        test_url = self.test_urls['hawaii_news'][0]
        
        try:
            result = self.firecrawl.scrape_hawaii_content(test_url, {
                'formats': ['markdown', 'html'],
                'includeTags': ['img', 'picture', 'figure']
            })
            
            if result:
                images = result['processed_data'].get('images', [])
                
                # 分析图片质量
                high_quality_images = [img for img in images if img.get('type') in ['nature', 'culture', 'weather']]
                hawaii_relevant_images = [img for img in images if 'hawaii' in img.get('alt_text', '').lower()]
                
                self.test_results['tests_completed'].append({
                    'test': 'image_processing',
                    'status': 'PASSED',
                    'total_images': len(images),
                    'high_quality_images': len(high_quality_images),
                    'hawaii_relevant_images': len(hawaii_relevant_images),
                    'timestamp': datetime.now().isoformat()
                })
                logger.info(f"✅ 图片处理测试通过 - 发现{len(images)}张图片，{len(hawaii_relevant_images)}张夏威夷相关")
            else:
                raise ValueError("图片处理结果为空")
                
        except Exception as e:
            self.test_results['tests_failed'].append({
                'test': 'image_processing',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            logger.error(f"❌ 图片处理测试失败: {str(e)}")

    async def test_hawaiian_localization(self):
        """测试夏威夷本地化处理"""
        logger.info("🌺 测试夏威夷本地化处理...")
        
        # 测试内容包含夏威夷语和本地元素
        test_content = """
        Aloha! Welcome to beautiful Oahu. Today's weather in Honolulu is perfect for visiting Waikiki Beach.
        The local keiki are enjoying pau hana time, and the ohana is gathering for a traditional luau.
        Mahalo for visiting our island paradise. Da kine food here is broke da mouth!
        """
        
        try:
            result = self.hawaiian_processor.process_hawaiian_content(test_content)
            
            hawaiian_terms_count = len(result['hawaiian_terms_found'])
            local_places_count = len(result['local_places_mentioned'])
            pidgin_count = len(result['pidgin_phrases'])
            local_score = result['local_context_score']
            
            self.test_results['tests_completed'].append({
                'test': 'hawaiian_localization',
                'status': 'PASSED',
                'hawaiian_terms_found': hawaiian_terms_count,
                'local_places_found': local_places_count,
                'pidgin_phrases_found': pidgin_count,
                'local_context_score': local_score,
                'timestamp': datetime.now().isoformat()
            })
            
            logger.info(f"✅ 夏威夷本地化测试通过 - 本地化评分: {local_score:.2f}")
            logger.info(f"   发现夏威夷语词汇: {hawaiian_terms_count}个")
            logger.info(f"   发现本地地名: {local_places_count}个")
            logger.info(f"   发现Pidgin短语: {pidgin_count}个")
            
        except Exception as e:
            self.test_results['tests_failed'].append({
                'test': 'hawaiian_localization',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            logger.error(f"❌ 夏威夷本地化测试失败: {str(e)}")

    async def test_ai_integration(self):
        """测试AI系统集成"""
        logger.info("🤖 测试AI系统集成...")
        
        test_url = self.test_urls['hawaii_news'][0]
        
        try:
            # 获取内容
            scraped_result = self.firecrawl.scrape_hawaii_content(test_url)
            
            if scraped_result and 'processed_data' in scraped_result:
                # 测试分类结果
                classification = scraped_result['processed_data'].get('classification')
                chinese_relevance = scraped_result['processed_data'].get('chinese_relevance', 0)
                
                self.test_results['tests_completed'].append({
                    'test': 'ai_integration',
                    'status': 'PASSED',
                    'classification_available': classification is not None,
                    'classification_confidence': classification.confidence if classification else 0,
                    'chinese_relevance_score': chinese_relevance,
                    'timestamp': datetime.now().isoformat()
                })
                
                logger.info(f"✅ AI集成测试通过")
                if classification:
                    logger.info(f"   分类结果: {classification.category} (置信度: {classification.confidence:.2f})")
                logger.info(f"   华人相关度: {chinese_relevance:.2f}")
            else:
                raise ValueError("AI集成测试数据不完整")
                
        except Exception as e:
            self.test_results['tests_failed'].append({
                'test': 'ai_integration',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            logger.error(f"❌ AI集成测试失败: {str(e)}")

    async def test_batch_processing(self):
        """测试批量处理功能"""
        logger.info("📦 测试批量处理功能...")
        
        test_urls = self.test_urls['hawaii_news'][:2]  # 限制测试数量
        start_time = time.time()
        
        try:
            results = self.firecrawl.batch_scrape_hawaii_sources(test_urls, batch_size=2)
            end_time = time.time()
            
            successful_results = [r for r in results if r is not None]
            
            self.test_results['tests_completed'].append({
                'test': 'batch_processing',
                'status': 'PASSED',
                'urls_processed': len(test_urls),
                'successful_results': len(successful_results),
                'success_rate': len(successful_results) / len(test_urls) * 100,
                'total_time': end_time - start_time,
                'avg_time_per_url': (end_time - start_time) / len(test_urls),
                'timestamp': datetime.now().isoformat()
            })
            
            logger.info(f"✅ 批量处理测试通过")
            logger.info(f"   处理URL数量: {len(test_urls)}")
            logger.info(f"   成功率: {len(successful_results) / len(test_urls) * 100:.1f}%")
            logger.info(f"   平均处理时间: {(end_time - start_time) / len(test_urls):.2f}秒/URL")
            
        except Exception as e:
            self.test_results['tests_failed'].append({
                'test': 'batch_processing',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            logger.error(f"❌ 批量处理测试失败: {str(e)}")

    async def test_n8n_workflows(self):
        """测试n8n工作流生成"""
        logger.info("⚙️ 测试n8n工作流生成...")
        
        try:
            # 生成新闻采集工作流
            news_workflow = self.workflow_generator.generate_hawaii_news_workflow()
            
            # 生成招聘信息工作流
            job_workflow = self.workflow_generator.generate_job_scraping_workflow()
            
            # 验证工作流结构
            news_valid = self._validate_workflow_structure(news_workflow)
            job_valid = self._validate_workflow_structure(job_workflow)
            
            self.test_results['tests_completed'].append({
                'test': 'n8n_workflows',
                'status': 'PASSED',
                'news_workflow_valid': news_valid,
                'job_workflow_valid': job_valid,
                'news_workflow_nodes': len(news_workflow.get('nodes', [])),
                'job_workflow_nodes': len(job_workflow.get('nodes', [])),
                'timestamp': datetime.now().isoformat()
            })
            
            logger.info(f"✅ n8n工作流测试通过")
            logger.info(f"   新闻工作流节点数: {len(news_workflow.get('nodes', []))}")
            logger.info(f"   招聘工作流节点数: {len(job_workflow.get('nodes', []))}")
            
        except Exception as e:
            self.test_results['tests_failed'].append({
                'test': 'n8n_workflows',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            logger.error(f"❌ n8n工作流测试失败: {str(e)}")

    async def test_performance_monitoring(self):
        """测试性能监控功能"""
        logger.info("📊 测试性能监控功能...")
        
        try:
            # 获取使用统计
            usage_stats = self.firecrawl.get_usage_statistics()
            
            # 验证统计数据结构
            required_fields = ['daily_credits_used', 'monthly_credits_used', 'cache_entries']
            stats_valid = all(field in usage_stats for field in required_fields)
            
            self.test_results['tests_completed'].append({
                'test': 'performance_monitoring',
                'status': 'PASSED',
                'stats_structure_valid': stats_valid,
                'daily_credits_used': usage_stats.get('daily_credits_used', 0),
                'monthly_credits_used': usage_stats.get('monthly_credits_used', 0),
                'cache_entries': usage_stats.get('cache_entries', 0),
                'timestamp': datetime.now().isoformat()
            })
            
            logger.info(f"✅ 性能监控测试通过")
            logger.info(f"   日Credits使用: {usage_stats.get('daily_credits_used', 0)}")
            logger.info(f"   缓存条目数: {usage_stats.get('cache_entries', 0)}")
            
        except Exception as e:
            self.test_results['tests_failed'].append({
                'test': 'performance_monitoring',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            logger.error(f"❌ 性能监控测试失败: {str(e)}")

    async def test_error_handling(self):
        """测试错误处理机制"""
        logger.info("🛡️ 测试错误处理机制...")
        
        # 测试无效URL
        invalid_url = "https://invalid-url-that-does-not-exist.com"
        
        try:
            result = self.firecrawl.scrape_hawaii_content(invalid_url)
            
            # 应该返回None或错误信息，而不是抛出异常
            error_handled = result is None
            
            self.test_results['tests_completed'].append({
                'test': 'error_handling',
                'status': 'PASSED',
                'invalid_url_handled': error_handled,
                'graceful_degradation': True,
                'timestamp': datetime.now().isoformat()
            })
            
            logger.info(f"✅ 错误处理测试通过 - 无效URL被正确处理")
            
        except Exception as e:
            # 如果抛出异常，说明错误处理不够完善
            self.test_results['tests_failed'].append({
                'test': 'error_handling',
                'error': f"错误处理不完善: {str(e)}",
                'timestamp': datetime.now().isoformat()
            })
            logger.error(f"❌ 错误处理测试失败: {str(e)}")

    def _validate_workflow_structure(self, workflow: dict) -> bool:
        """验证工作流结构"""
        required_fields = ['name', 'nodes', 'connections']
        return all(field in workflow for field in required_fields)

    async def generate_final_report(self):
        """生成最终测试报告"""
        logger.info("📋 生成最终测试报告...")
        
        end_time = datetime.now()
        total_duration = (end_time - self.test_results['start_time']).total_seconds()
        
        # 计算测试统计
        total_tests = len(self.test_results['tests_completed']) + len(self.test_results['tests_failed'])
        passed_tests = len(self.test_results['tests_completed'])
        failed_tests = len(self.test_results['tests_failed'])
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 生成报告
        report = {
            'test_summary': {
                'start_time': self.test_results['start_time'].isoformat(),
                'end_time': end_time.isoformat(),
                'total_duration_seconds': total_duration,
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate_percentage': success_rate
            },
            'test_details': {
                'completed_tests': self.test_results['tests_completed'],
                'failed_tests': self.test_results['tests_failed']
            },
            'system_status': {
                'firecrawl_integration': 'OPERATIONAL' if passed_tests > failed_tests else 'DEGRADED',
                'ai_systems': 'OPERATIONAL',
                'hawaiian_localization': 'OPERATIONAL',
                'n8n_workflows': 'OPERATIONAL'
            },
            'recommendations': self._generate_recommendations()
        }
        
        # 保存报告
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        # 输出摘要
        logger.info("=" * 60)
        logger.info("🎉 HawaiiHub.net Firecrawl集成测试完成!")
        logger.info("=" * 60)
        logger.info(f"📊 测试统计:")
        logger.info(f"   总测试数: {total_tests}")
        logger.info(f"   通过测试: {passed_tests}")
        logger.info(f"   失败测试: {failed_tests}")
        logger.info(f"   成功率: {success_rate:.1f}%")
        logger.info(f"   总耗时: {total_duration:.2f}秒")
        logger.info(f"📁 详细报告已保存到: {report_file}")
        
        if failed_tests > 0:
            logger.warning("⚠️ 存在失败的测试，请检查详细报告")
        else:
            logger.info("🎊 所有测试通过！系统已准备好投入生产使用")

    def _generate_recommendations(self) -> list:
        """生成优化建议"""
        recommendations = []
        
        failed_tests = self.test_results['tests_failed']
        
        if any(test['test'] == 'basic_scraping' for test in failed_tests):
            recommendations.append("建议检查Firecrawl API密钥和网络连接")
        
        if any(test['test'] == 'ai_integration' for test in failed_tests):
            recommendations.append("建议检查AI分类服务的运行状态")
        
        if any(test['test'] == 'batch_processing' for test in failed_tests):
            recommendations.append("建议优化批量处理的并发控制和错误处理")
        
        if not recommendations:
            recommendations.append("系统运行良好，建议定期监控Credits使用情况")
            recommendations.append("建议设置自动化监控告警")
            recommendations.append("建议定期备份配置和数据")
        
        return recommendations

# 主执行函数
async def main():
    """主测试执行函数"""
    tester = CompleteIntegrationTester()
    await tester.run_complete_test_suite()

if __name__ == "__main__":
    asyncio.run(main())
