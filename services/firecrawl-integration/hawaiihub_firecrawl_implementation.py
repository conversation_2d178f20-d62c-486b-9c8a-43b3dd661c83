#!/usr/bin/env python3
"""
HawaiiHub.net Firecrawl集成实现
基于官方SDK和最佳实践的完整技术方案

作者: AI运营官
日期: 2025-01-28
版本: 1.0.0
"""

import os
import time
import json
import logging
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from pathlib import Path

# Firecrawl SDK
from firecrawl import FirecrawlApp, ScrapeOptions
from pydantic import BaseModel, Field

# 本地AI系统集成
import sys
sys.path.append('../ai-classifier')
sys.path.append('../ai-testing')
from content_classifier import HawaiiContentClassifier
from duplicate_detector import DuplicateDetector

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hawaiihub_firecrawl.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class FirecrawlConfig:
    """Firecrawl配置类"""
    api_key: str = "fc-0a2c801f433d4718bcd8189f2742edf4"
    base_url: str = "https://api.firecrawl.dev"
    daily_credit_limit: int = 100
    monthly_credit_limit: int = 3000
    retry_attempts: int = 3
    retry_delay: float = 2.0
    timeout: int = 30

class HawaiiContentSchema(BaseModel):
    """夏威夷内容结构化Schema"""
    title: str = Field(description="文章标题")
    content: str = Field(description="文章正文内容")
    url: str = Field(description="原始URL")
    published_date: Optional[str] = Field(description="发布日期")
    author: Optional[str] = Field(description="作者")
    category: Optional[str] = Field(description="内容分类")
    location: Optional[str] = Field(description="地理位置信息")
    images: List[Dict[str, str]] = Field(default=[], description="图片信息列表")
    tags: List[str] = Field(default=[], description="标签列表")
    language: str = Field(default="en", description="内容语言")
    chinese_relevance: float = Field(default=0.0, description="华人相关度评分")

class HawaiiFirecrawlIntegration:
    """HawaiiHub.net Firecrawl集成主类"""
    
    def __init__(self, config: Optional[FirecrawlConfig] = None):
        """初始化Firecrawl集成"""
        self.config = config or FirecrawlConfig()
        
        # 初始化Firecrawl应用
        self.app = FirecrawlApp(api_key=self.config.api_key)
        
        # 初始化本地AI系统
        self.classifier = HawaiiContentClassifier()
        self.duplicate_detector = DuplicateDetector()
        
        # 使用统计
        self.daily_credits_used = 0
        self.monthly_credits_used = 0
        self.last_reset_date = datetime.now().date()
        
        # 缓存系统
        self.cache = {}
        self.cache_ttl = 3600  # 1小时缓存
        
        logger.info("HawaiiFirecrawlIntegration初始化完成")

    def _check_credit_limits(self) -> bool:
        """检查Credits使用限制"""
        # 重置日计数器
        if datetime.now().date() > self.last_reset_date:
            self.daily_credits_used = 0
            self.last_reset_date = datetime.now().date()
        
        if self.daily_credits_used >= self.config.daily_credit_limit:
            logger.warning(f"已达到日Credits限制: {self.daily_credits_used}/{self.config.daily_credit_limit}")
            return False
        
        if self.monthly_credits_used >= self.config.monthly_credit_limit:
            logger.warning(f"已达到月Credits限制: {self.monthly_credits_used}/{self.config.monthly_credit_limit}")
            return False
        
        return True

    def _update_credit_usage(self, credits_used: int = 1):
        """更新Credits使用统计"""
        self.daily_credits_used += credits_used
        self.monthly_credits_used += credits_used
        
        logger.info(f"Credits使用更新: 日用量 {self.daily_credits_used}/{self.config.daily_credit_limit}, "
                   f"月用量 {self.monthly_credits_used}/{self.config.monthly_credit_limit}")

    def _get_cache_key(self, url: str, options: Dict = None) -> str:
        """生成缓存键"""
        import hashlib
        cache_data = f"{url}_{json.dumps(options or {}, sort_keys=True)}"
        return hashlib.md5(cache_data.encode()).hexdigest()

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.cache:
            return False
        
        cached_time = self.cache[cache_key].get('timestamp', 0)
        return time.time() - cached_time < self.cache_ttl

    def scrape_hawaii_content(self, url: str, enhanced_options: Dict = None) -> Optional[Dict]:
        """爬取夏威夷相关内容的增强方法"""
        
        # 检查Credits限制
        if not self._check_credit_limits():
            logger.error("Credits限制，无法执行爬取")
            return None
        
        # 检查缓存
        cache_key = self._get_cache_key(url, enhanced_options)
        if self._is_cache_valid(cache_key):
            logger.info(f"使用缓存数据: {url}")
            return self.cache[cache_key]['data']
        
        try:
            # 默认爬取选项
            default_options = {
                'formats': ['markdown', 'html'],
                'onlyMainContent': True,
                'waitFor': 3000,
                'includeTags': ['img', 'picture', 'figure'],
                'extract': {
                    'schema': HawaiiContentSchema.model_json_schema(),
                    'systemPrompt': "你是专门分析夏威夷本地内容的AI助手，特别关注华人社区相关信息。"
                }
            }
            
            # 合并用户选项
            if enhanced_options:
                default_options.update(enhanced_options)
            
            # 执行爬取
            logger.info(f"开始爬取: {url}")
            result = self.app.scrape_url(url, params=default_options)
            
            if result and result.get('success', False):
                # 更新Credits使用
                self._update_credit_usage(1)
                
                # 处理结果
                processed_result = self._process_scraped_content(result, url)
                
                # 缓存结果
                self.cache[cache_key] = {
                    'data': processed_result,
                    'timestamp': time.time()
                }
                
                logger.info(f"爬取成功: {url}")
                return processed_result
            else:
                logger.error(f"爬取失败: {url}, 结果: {result}")
                return None
                
        except Exception as e:
            logger.error(f"爬取异常: {url}, 错误: {str(e)}")
            return None

    def _process_scraped_content(self, raw_result: Dict, url: str) -> Dict:
        """处理爬取的原始内容"""
        processed = {
            'url': url,
            'timestamp': datetime.now().isoformat(),
            'raw_data': raw_result,
            'processed_data': {}
        }
        
        # 提取基础内容
        markdown_content = raw_result.get('markdown', '')
        html_content = raw_result.get('html', '')
        
        # 提取结构化数据
        if 'extract' in raw_result:
            structured_data = raw_result['extract']
            processed['processed_data']['structured'] = structured_data
        
        # 提取图片信息
        images = self._extract_image_info(markdown_content, html_content)
        processed['processed_data']['images'] = images
        
        # AI内容分类
        if markdown_content:
            title = self._extract_title(markdown_content)
            classification = self.classifier.classify_content(title, markdown_content)
            processed['processed_data']['classification'] = classification
        
        # 华人相关度评分
        chinese_relevance = self._calculate_chinese_relevance(markdown_content)
        processed['processed_data']['chinese_relevance'] = chinese_relevance
        
        return processed

    def _extract_image_info(self, markdown: str, html: str) -> List[Dict]:
        """提取图片信息"""
        import re
        images = []
        
        # 从Markdown中提取图片
        markdown_pattern = r'!\[(.*?)\]\((.*?)\)'
        markdown_matches = re.findall(markdown_pattern, markdown)
        
        for alt_text, url in markdown_matches:
            images.append({
                'url': url,
                'alt_text': alt_text,
                'type': self._classify_image_type(alt_text),
                'source': 'markdown'
            })
        
        # 从HTML中提取更多图片信息
        html_pattern = r'<img[^>]+src="([^"]+)"[^>]*alt="([^"]*)"[^>]*>'
        html_matches = re.findall(html_pattern, html)
        
        for url, alt_text in html_matches:
            # 避免重复
            if not any(img['url'] == url for img in images):
                images.append({
                    'url': url,
                    'alt_text': alt_text,
                    'type': self._classify_image_type(alt_text),
                    'source': 'html'
                })
        
        return images

    def _classify_image_type(self, alt_text: str) -> str:
        """分类图片类型"""
        alt_lower = alt_text.lower()
        
        if any(keyword in alt_lower for keyword in ['weather', 'forecast', 'hurricane', 'storm']):
            return 'weather'
        elif any(keyword in alt_lower for keyword in ['breaking', 'news', 'urgent']):
            return 'breaking_news'
        elif any(keyword in alt_lower for keyword in ['ceremony', 'event', 'festival', 'celebration']):
            return 'community_event'
        elif any(keyword in alt_lower for keyword in ['beach', 'ocean', 'mountain', 'sunset', 'landscape']):
            return 'nature'
        else:
            return 'general'

    def _extract_title(self, markdown: str) -> str:
        """从Markdown中提取标题"""
        lines = markdown.split('\n')
        for line in lines:
            if line.startswith('# '):
                return line[2:].strip()
        return "未知标题"

    def _calculate_chinese_relevance(self, content: str) -> float:
        """计算华人相关度评分"""
        chinese_keywords = [
            'chinese', 'china', 'mandarin', 'cantonese', 'taiwan', 'hong kong',
            '中文', '华人', '中国', '台湾', '香港', '华侨', '中华', '亚洲'
        ]
        
        content_lower = content.lower()
        matches = sum(1 for keyword in chinese_keywords if keyword in content_lower)
        
        # 基础评分
        base_score = min(matches * 0.2, 1.0)
        
        # 长度调整
        length_factor = min(len(content) / 1000, 1.0)
        
        return base_score * length_factor

    def batch_scrape_hawaii_sources(self, urls: List[str], batch_size: int = 5) -> List[Dict]:
        """批量爬取夏威夷信息源"""
        results = []
        
        for i in range(0, len(urls), batch_size):
            batch = urls[i:i + batch_size]
            
            logger.info(f"处理批次 {i//batch_size + 1}: {len(batch)} 个URL")
            
            batch_results = []
            for url in batch:
                result = self.scrape_hawaii_content(url)
                if result:
                    batch_results.append(result)
                
                # 批次间延迟，避免过度请求
                time.sleep(1)
            
            results.extend(batch_results)
            
            # 检查Credits限制
            if not self._check_credit_limits():
                logger.warning("达到Credits限制，停止批量爬取")
                break
            
            # 批次间休息
            if i + batch_size < len(urls):
                time.sleep(2)
        
        return results

    def crawl_hawaii_website(self, base_url: str, crawl_options: Dict = None) -> Dict:
        """爬取整个夏威夷网站"""
        
        if not self._check_credit_limits():
            logger.error("Credits限制，无法执行网站爬取")
            return {}
        
        try:
            default_crawl_options = {
                'limit': 50,
                'scrapeOptions': {
                    'formats': ['markdown', 'html'],
                    'onlyMainContent': True,
                    'waitFor': 3000
                }
            }
            
            if crawl_options:
                default_crawl_options.update(crawl_options)
            
            logger.info(f"开始爬取网站: {base_url}")
            crawl_result = self.app.crawl_url(base_url, **default_crawl_options)
            
            if crawl_result and crawl_result.get('success', False):
                # 更新Credits使用（爬取通常消耗更多Credits）
                pages_crawled = len(crawl_result.get('data', []))
                self._update_credit_usage(pages_crawled)
                
                # 处理爬取结果
                processed_crawl = self._process_crawl_results(crawl_result, base_url)
                
                logger.info(f"网站爬取成功: {base_url}, 页面数: {pages_crawled}")
                return processed_crawl
            else:
                logger.error(f"网站爬取失败: {base_url}")
                return {}
                
        except Exception as e:
            logger.error(f"网站爬取异常: {base_url}, 错误: {str(e)}")
            return {}

    def _process_crawl_results(self, crawl_result: Dict, base_url: str) -> Dict:
        """处理网站爬取结果"""
        processed = {
            'base_url': base_url,
            'timestamp': datetime.now().isoformat(),
            'total_pages': len(crawl_result.get('data', [])),
            'pages': [],
            'summary': {
                'categories': {},
                'total_images': 0,
                'chinese_relevant_pages': 0,
                'duplicate_groups': []
            }
        }
        
        # 处理每个页面
        for page_data in crawl_result.get('data', []):
            processed_page = self._process_scraped_content(page_data, page_data.get('url', ''))
            processed['pages'].append(processed_page)
            
            # 统计分类
            if 'classification' in processed_page.get('processed_data', {}):
                category = processed_page['processed_data']['classification'].get('category', 'unknown')
                processed['summary']['categories'][category] = processed['summary']['categories'].get(category, 0) + 1
            
            # 统计图片
            images_count = len(processed_page.get('processed_data', {}).get('images', []))
            processed['summary']['total_images'] += images_count
            
            # 统计华人相关页面
            chinese_relevance = processed_page.get('processed_data', {}).get('chinese_relevance', 0)
            if chinese_relevance > 0.3:
                processed['summary']['chinese_relevant_pages'] += 1
        
        # 重复内容检测
        if len(processed['pages']) > 1:
            articles_for_duplicate_check = []
            for page in processed['pages']:
                if 'processed_data' in page and 'structured' in page['processed_data']:
                    articles_for_duplicate_check.append({
                        'title': page['processed_data']['structured'].get('title', ''),
                        'content': page['processed_data']['structured'].get('content', ''),
                        'url': page['url']
                    })
            
            if articles_for_duplicate_check:
                duplicate_groups = self.duplicate_detector.detect_duplicates(articles_for_duplicate_check)
                processed['summary']['duplicate_groups'] = [group.to_dict() for group in duplicate_groups]
        
        return processed

    def get_usage_statistics(self) -> Dict:
        """获取使用统计"""
        return {
            'daily_credits_used': self.daily_credits_used,
            'daily_credits_limit': self.config.daily_credit_limit,
            'daily_usage_percentage': (self.daily_credits_used / self.config.daily_credit_limit) * 100,
            'monthly_credits_used': self.monthly_credits_used,
            'monthly_credits_limit': self.config.monthly_credit_limit,
            'monthly_usage_percentage': (self.monthly_credits_used / self.config.monthly_credit_limit) * 100,
            'last_reset_date': self.last_reset_date.isoformat(),
            'cache_entries': len(self.cache)
        }

    def export_results(self, results: Union[Dict, List[Dict]], filename: str = None) -> str:
        """导出结果到JSON文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"hawaiihub_firecrawl_results_{timestamp}.json"
        
        filepath = Path(filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"结果已导出到: {filepath}")
        return str(filepath)

# 使用示例和测试
if __name__ == "__main__":
    # 初始化集成系统
    integration = HawaiiFirecrawlIntegration()
    
    # 测试单个URL爬取
    test_url = "https://www.hawaiinewsnow.com"
    result = integration.scrape_hawaii_content(test_url)
    
    if result:
        print("✅ 单个URL爬取测试成功")
        print(f"华人相关度: {result['processed_data'].get('chinese_relevance', 0):.2f}")
        print(f"图片数量: {len(result['processed_data'].get('images', []))}")
    
    # 测试批量爬取
    hawaii_sources = [
        "https://www.hawaiinewsnow.com",
        "https://www.staradvertiser.com",
        "https://www.hawaiitribune-herald.com"
    ]
    
    batch_results = integration.batch_scrape_hawaii_sources(hawaii_sources[:2])  # 限制测试数量
    print(f"✅ 批量爬取测试完成，处理了 {len(batch_results)} 个URL")
    
    # 显示使用统计
    stats = integration.get_usage_statistics()
    print(f"📊 Credits使用情况: 日用量 {stats['daily_credits_used']}/{stats['daily_credits_limit']} "
          f"({stats['daily_usage_percentage']:.1f}%)")
    
    # 导出结果
    if batch_results:
        export_file = integration.export_results(batch_results)
        print(f"📁 结果已导出到: {export_file}")

class HawaiiLocalContentProcessor:
    """夏威夷本地特色内容处理器"""

    def __init__(self):
        self.hawaiian_terms = {
            # 夏威夷语常用词汇
            'aloha': '你好/再见/爱',
            'mahalo': '谢谢',
            'ohana': '家庭',
            'keiki': '孩子',
            'kupuna': '长者',
            'malama': '照顾',
            'pono': '正确/公正',
            'kokua': '帮助',
            'pau': '完成',
            'da kine': '那个东西',
            'grindz': '食物',
            'broke da mouth': '非常美味',
            'talk story': '聊天',
            'shoots': '好的/同意',
            'choke': '很多'
        }

        self.local_places = {
            # 主要岛屿和地区
            'oahu': '欧胡岛',
            'maui': '毛伊岛',
            'big island': '大岛',
            'kauai': '考艾岛',
            'molokai': '摩洛凯岛',
            'lanai': '拉奈岛',
            'honolulu': '火奴鲁鲁',
            'waikiki': '威基基',
            'pearl harbor': '珍珠港',
            'diamond head': '钻石头山',
            'haleakala': '哈雷阿卡拉火山',
            'kilauea': '基拉韦厄火山'
        }

        self.cultural_events = {
            # 文化活动和节日
            'lei day': '花环节',
            'king kamehameha day': '卡美哈美哈国王节',
            'aloha festivals': '阿罗哈节',
            'merrie monarch': '快乐君主节',
            'lantern floating': '灯笼漂流节',
            'triple crown of surfing': '冲浪三冠王',
            'vans triple crown': '万斯三冠王'
        }

    def process_hawaiian_content(self, content: str) -> Dict:
        """处理包含夏威夷本地特色的内容"""
        processed = {
            'original_content': content,
            'hawaiian_terms_found': [],
            'local_places_mentioned': [],
            'cultural_events_found': [],
            'pidgin_phrases': [],
            'local_context_score': 0.0,
            'translations': {}
        }

        content_lower = content.lower()

        # 识别夏威夷语词汇
        for term, translation in self.hawaiian_terms.items():
            if term in content_lower:
                processed['hawaiian_terms_found'].append({
                    'term': term,
                    'translation': translation,
                    'context': self._extract_context(content, term)
                })
                processed['translations'][term] = translation

        # 识别本地地名
        for place, chinese_name in self.local_places.items():
            if place in content_lower:
                processed['local_places_mentioned'].append({
                    'place': place,
                    'chinese_name': chinese_name,
                    'context': self._extract_context(content, place)
                })

        # 识别文化活动
        for event, chinese_name in self.cultural_events.items():
            if event in content_lower:
                processed['cultural_events_found'].append({
                    'event': event,
                    'chinese_name': chinese_name,
                    'context': self._extract_context(content, event)
                })

        # 识别Pidgin短语
        pidgin_patterns = [
            'da kine', 'broke da mouth', 'talk story', 'shoots', 'choke',
            'stay', 'fo real', 'no can', 'how you stay', 'wot you doing'
        ]

        for phrase in pidgin_patterns:
            if phrase in content_lower:
                processed['pidgin_phrases'].append({
                    'phrase': phrase,
                    'context': self._extract_context(content, phrase)
                })

        # 计算本地化程度评分
        local_score = (
            len(processed['hawaiian_terms_found']) * 0.3 +
            len(processed['local_places_mentioned']) * 0.2 +
            len(processed['cultural_events_found']) * 0.3 +
            len(processed['pidgin_phrases']) * 0.2
        )
        processed['local_context_score'] = min(local_score, 1.0)

        return processed

    def _extract_context(self, content: str, term: str, context_length: int = 50) -> str:
        """提取词汇的上下文"""
        content_lower = content.lower()
        term_lower = term.lower()

        index = content_lower.find(term_lower)
        if index == -1:
            return ""

        start = max(0, index - context_length)
        end = min(len(content), index + len(term) + context_length)

        return content[start:end].strip()

class N8NFirecrawlWorkflowGenerator:
    """n8n Firecrawl工作流生成器"""

    def __init__(self, firecrawl_api_key: str):
        self.api_key = firecrawl_api_key
        self.base_workflow_template = {
            "name": "HawaiiHub Firecrawl 工作流",
            "nodes": [],
            "connections": {},
            "active": True,
            "settings": {},
            "staticData": {}
        }

    def generate_hawaii_news_workflow(self) -> Dict:
        """生成夏威夷新闻采集工作流"""
        workflow = self.base_workflow_template.copy()
        workflow["name"] = "夏威夷新闻智能采集工作流"

        # 定时触发器节点
        cron_trigger = {
            "parameters": {
                "rule": {
                    "interval": [{"field": "hours", "value": 2}]
                }
            },
            "name": "定时触发器",
            "type": "n8n-nodes-base.cron",
            "typeVersion": 1,
            "position": [240, 300]
        }

        # 新闻源配置节点
        news_sources = {
            "parameters": {
                "values": {
                    "string": [
                        {"name": "hawaii_news_now", "value": "https://www.hawaiinewsnow.com"},
                        {"name": "star_advertiser", "value": "https://www.staradvertiser.com"},
                        {"name": "hawaii_tribune", "value": "https://www.hawaiitribune-herald.com"}
                    ]
                }
            },
            "name": "新闻源配置",
            "type": "n8n-nodes-base.set",
            "typeVersion": 1,
            "position": [460, 300]
        }

        # Firecrawl爬取节点
        firecrawl_scrape = {
            "parameters": {
                "url": "={{$json.value}}",
                "method": "POST",
                "headers": {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                "body": {
                    "url": "={{$json.value}}",
                    "formats": ["markdown", "html"],
                    "onlyMainContent": True,
                    "waitFor": 3000,
                    "extract": {
                        "schema": {
                            "type": "object",
                            "properties": {
                                "title": {"type": "string"},
                                "content": {"type": "string"},
                                "published_date": {"type": "string"},
                                "author": {"type": "string"},
                                "images": {"type": "array"}
                            }
                        }
                    }
                }
            },
            "name": "Firecrawl内容爬取",
            "type": "n8n-nodes-base.httpRequest",
            "typeVersion": 1,
            "position": [680, 300]
        }

        # AI分类节点
        ai_classifier = {
            "parameters": {
                "url": "http://localhost:8000/classify",
                "method": "POST",
                "headers": {"Content-Type": "application/json"},
                "body": {
                    "title": "={{$json.extract.title}}",
                    "content": "={{$json.extract.content}}"
                }
            },
            "name": "AI内容分类",
            "type": "n8n-nodes-base.httpRequest",
            "typeVersion": 1,
            "position": [900, 300]
        }

        # 夏威夷本地化处理节点
        hawaiian_processor = {
            "parameters": {
                "functionCode": `
                // 夏威夷本地化内容处理
                const content = items[0].json.extract.content || '';

                const hawaiianTerms = {
                    'aloha': '你好/再见/爱',
                    'mahalo': '谢谢',
                    'ohana': '家庭',
                    'keiki': '孩子'
                };

                const localPlaces = {
                    'oahu': '欧胡岛',
                    'maui': '毛伊岛',
                    'honolulu': '火奴鲁鲁',
                    'waikiki': '威基基'
                };

                let hawaiianTermsFound = [];
                let localPlacesFound = [];
                let localScore = 0;

                // 检测夏威夷语词汇
                for (const [term, translation] of Object.entries(hawaiianTerms)) {
                    if (content.toLowerCase().includes(term)) {
                        hawaiianTermsFound.push({term, translation});
                        localScore += 0.2;
                    }
                }

                // 检测本地地名
                for (const [place, chineseName] of Object.entries(localPlaces)) {
                    if (content.toLowerCase().includes(place)) {
                        localPlacesFound.push({place, chineseName});
                        localScore += 0.15;
                    }
                }

                return [{
                    json: {
                        ...items[0].json,
                        hawaiian_processing: {
                            hawaiian_terms_found: hawaiianTermsFound,
                            local_places_found: localPlacesFound,
                            local_context_score: Math.min(localScore, 1.0),
                            is_hawaii_relevant: localScore > 0.3
                        }
                    }
                }];
                `
            },
            "name": "夏威夷本地化处理",
            "type": "n8n-nodes-base.function",
            "typeVersion": 1,
            "position": [1120, 300]
        }

        # 重复检测节点
        duplicate_detector = {
            "parameters": {
                "url": "http://localhost:8001/detect-duplicate",
                "method": "POST",
                "headers": {"Content-Type": "application/json"},
                "body": {
                    "articles": ["={{$json}}"]
                }
            },
            "name": "重复内容检测",
            "type": "n8n-nodes-base.httpRequest",
            "typeVersion": 1,
            "position": [1340, 300]
        }

        # 数据存储节点
        data_storage = {
            "parameters": {
                "operation": "insert",
                "table": "hawaiihub_content",
                "columns": "url, title, content, category, hawaiian_score, images, created_at",
                "additionalFields": {}
            },
            "name": "数据库存储",
            "type": "n8n-nodes-base.postgres",
            "typeVersion": 1,
            "position": [1560, 300]
        }

        # 组装工作流
        workflow["nodes"] = [
            cron_trigger, news_sources, firecrawl_scrape,
            ai_classifier, hawaiian_processor, duplicate_detector, data_storage
        ]

        # 配置节点连接
        workflow["connections"] = {
            "定时触发器": {"main": [[{"node": "新闻源配置", "type": "main", "index": 0}]]},
            "新闻源配置": {"main": [[{"node": "Firecrawl内容爬取", "type": "main", "index": 0}]]},
            "Firecrawl内容爬取": {"main": [[{"node": "AI内容分类", "type": "main", "index": 0}]]},
            "AI内容分类": {"main": [[{"node": "夏威夷本地化处理", "type": "main", "index": 0}]]},
            "夏威夷本地化处理": {"main": [[{"node": "重复内容检测", "type": "main", "index": 0}]]},
            "重复内容检测": {"main": [[{"node": "数据库存储", "type": "main", "index": 0}]]}
        }

        return workflow

    def generate_job_scraping_workflow(self) -> Dict:
        """生成招聘信息采集工作流"""
        workflow = self.base_workflow_template.copy()
        workflow["name"] = "夏威夷招聘信息智能采集工作流"

        # 招聘网站源
        job_sources = {
            "parameters": {
                "values": {
                    "string": [
                        {"name": "indeed_hawaii", "value": "https://www.indeed.com/jobs?q=&l=Hawaii"},
                        {"name": "craigslist_jobs", "value": "https://honolulu.craigslist.org/search/jjj"},
                        {"name": "hawaii_gov_jobs", "value": "https://www.governmentjobs.com/careers/hawaii"}
                    ]
                }
            },
            "name": "招聘源配置",
            "type": "n8n-nodes-base.set",
            "typeVersion": 1,
            "position": [240, 300]
        }

        # 招聘信息结构化提取
        job_extraction = {
            "parameters": {
                "url": "https://api.firecrawl.dev/v1/scrape",
                "method": "POST",
                "headers": {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                "body": {
                    "url": "={{$json.value}}",
                    "formats": ["extract"],
                    "extract": {
                        "schema": {
                            "type": "object",
                            "properties": {
                                "job_title": {"type": "string", "description": "职位名称"},
                                "company": {"type": "string", "description": "公司名称"},
                                "location": {"type": "string", "description": "工作地点"},
                                "salary": {"type": "string", "description": "薪资范围"},
                                "job_type": {"type": "string", "description": "工作类型（全职/兼职）"},
                                "requirements": {"type": "string", "description": "职位要求"},
                                "description": {"type": "string", "description": "职位描述"},
                                "posted_date": {"type": "string", "description": "发布日期"},
                                "application_url": {"type": "string", "description": "申请链接"}
                            }
                        },
                        "systemPrompt": "你是专门提取招聘信息的AI助手，请准确提取职位相关信息。"
                    }
                }
            },
            "name": "招聘信息提取",
            "type": "n8n-nodes-base.httpRequest",
            "typeVersion": 1,
            "position": [460, 300]
        }

        # 华人友好度评估
        chinese_friendly_assessment = {
            "parameters": {
                "functionCode": `
                // 华人友好度评估
                const jobData = items[0].json.extract;
                let friendlyScore = 0;
                let friendlyIndicators = [];

                const friendlyKeywords = [
                    'bilingual', 'chinese', 'mandarin', 'cantonese',
                    'multicultural', 'diverse', 'international',
                    'asian', 'multilingual', 'language skills'
                ];

                const content = (jobData.description + ' ' + jobData.requirements).toLowerCase();

                friendlyKeywords.forEach(keyword => {
                    if (content.includes(keyword)) {
                        friendlyScore += 0.2;
                        friendlyIndicators.push(keyword);
                    }
                });

                // 检查签证支持
                const visaKeywords = ['visa', 'sponsorship', 'h1b', 'work authorization'];
                let visaSupport = false;

                visaKeywords.forEach(keyword => {
                    if (content.includes(keyword)) {
                        visaSupport = true;
                        friendlyScore += 0.3;
                    }
                });

                return [{
                    json: {
                        ...items[0].json,
                        chinese_assessment: {
                            friendly_score: Math.min(friendlyScore, 1.0),
                            friendly_indicators: friendlyIndicators,
                            visa_support: visaSupport,
                            recommended_for_chinese: friendlyScore > 0.4
                        }
                    }
                }];
                `
            },
            "name": "华人友好度评估",
            "type": "n8n-nodes-base.function",
            "typeVersion": 1,
            "position": [680, 300]
        }

        workflow["nodes"] = [job_sources, job_extraction, chinese_friendly_assessment]

        workflow["connections"] = {
            "招聘源配置": {"main": [[{"node": "招聘信息提取", "type": "main", "index": 0}]]},
            "招聘信息提取": {"main": [[{"node": "华人友好度评估", "type": "main", "index": 0}]]}
        }

        return workflow

    def export_workflow(self, workflow: Dict, filename: str) -> str:
        """导出n8n工作流到JSON文件"""
        filepath = Path(f"n8n_workflows/{filename}")
        filepath.parent.mkdir(exist_ok=True)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, ensure_ascii=False, indent=2)

        logger.info(f"n8n工作流已导出到: {filepath}")
        return str(filepath)
