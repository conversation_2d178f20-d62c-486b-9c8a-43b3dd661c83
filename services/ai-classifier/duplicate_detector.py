#!/usr/bin/env python3
"""
夏威夷华人平台 - 重复内容智能去除系统
基于内容相似度的重复新闻检测算法
"""

import os
import sys
import json
import logging
import hashlib
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from collections import defaultdict
import sqlite3

# 文本处理库
import jieba
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

# 确保日志目录存在
log_dir = Path("../../data/logs")
log_dir.mkdir(parents=True, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'duplicate_detector.log'),
        logging.StreamHandler()
    ]
)

@dataclass
class DuplicateGroup:
    """重复内容组"""
    group_id: str
    articles: List[Dict]
    similarity_scores: List[float]
    merge_strategy: str
    primary_article: Optional[Dict] = None
    merged_content: Optional[Dict] = None

@dataclass
class SimilarityResult:
    """相似度检测结果"""
    article1_id: str
    article2_id: str
    fingerprint_similarity: float
    semantic_similarity: float
    overall_similarity: float
    is_duplicate: bool

class ContentFingerprint:
    """内容指纹生成器"""
    
    def __init__(self):
        # 停用词列表
        self.stop_words = {
            'zh': {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'},
            'en': {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'}
        }
    
    def clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 移除特殊字符，保留中英文、数字、空格
        text = re.sub(r'[^\u4e00-\u9fff\w\s]', ' ', text)
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text.lower()
    
    def extract_keywords(self, text: str, language: str = 'auto') -> List[str]:
        """提取关键词"""
        clean_text = self.clean_text(text)
        
        # 自动检测语言
        if language == 'auto':
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', clean_text))
            language = 'zh' if chinese_chars > len(clean_text) * 0.3 else 'en'
        
        # 分词
        if language == 'zh':
            words = list(jieba.cut(clean_text))
        else:
            words = clean_text.split()
        
        # 过滤停用词和短词
        stop_words = self.stop_words.get(language, set())
        keywords = [word for word in words 
                   if len(word) > 1 and word not in stop_words]
        
        return keywords
    
    def generate_fingerprint(self, title: str, content: str) -> Dict:
        """生成内容指纹"""
        full_text = f"{title} {content}"
        clean_text = self.clean_text(full_text)
        
        # 1. 简单哈希指纹
        simple_hash = hashlib.md5(clean_text.encode('utf-8')).hexdigest()
        
        # 2. 标题指纹
        title_clean = self.clean_text(title)
        title_hash = hashlib.md5(title_clean.encode('utf-8')).hexdigest()
        
        # 3. 关键词指纹
        keywords = self.extract_keywords(full_text)
        keywords_text = ' '.join(sorted(keywords[:20]))  # 取前20个关键词
        keywords_hash = hashlib.md5(keywords_text.encode('utf-8')).hexdigest()
        
        # 4. 长度指纹
        length_bucket = len(clean_text) // 100 * 100  # 按100字符分桶
        
        # 5. 特征词指纹（提取数字、专有名词等）
        features = []
        # 提取数字
        numbers = re.findall(r'\d+', full_text)
        features.extend(numbers[:5])  # 取前5个数字
        
        # 提取可能的专有名词（大写开头的英文词）
        proper_nouns = re.findall(r'\b[A-Z][a-z]+\b', full_text)
        features.extend(proper_nouns[:5])  # 取前5个专有名词
        
        features_text = ' '.join(sorted(features))
        features_hash = hashlib.md5(features_text.encode('utf-8')).hexdigest()
        
        return {
            'simple_hash': simple_hash,
            'title_hash': title_hash,
            'keywords_hash': keywords_hash,
            'length_bucket': length_bucket,
            'features_hash': features_hash,
            'keywords': keywords[:10],  # 保存前10个关键词用于调试
            'text_length': len(clean_text)
        }

class SemanticSimilarity:
    """语义相似度计算器"""
    
    def __init__(self):
        self.vectorizer = None
        self.article_vectors = {}
        self.article_texts = {}
    
    def prepare_corpus(self, articles: List[Dict]):
        """准备语料库"""
        texts = []
        article_ids = []
        
        for article in articles:
            text = f"{article.get('title', '')} {article.get('content', '')}"
            clean_text = self._preprocess_text(text)
            texts.append(clean_text)
            article_ids.append(article['id'])
            self.article_texts[article['id']] = clean_text
        
        # 创建TF-IDF向量化器
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=None,  # 我们已经在预处理中处理了停用词
            ngram_range=(1, 2),  # 使用1-gram和2-gram
            min_df=1,
            max_df=0.95
        )
        
        # 训练并转换文本
        if texts:
            vectors = self.vectorizer.fit_transform(texts)
            for i, article_id in enumerate(article_ids):
                self.article_vectors[article_id] = vectors[i]
        
        logging.info(f"语义相似度计算器已准备完成，处理了 {len(texts)} 篇文章")
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 清理HTML和特殊字符
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'[^\u4e00-\u9fff\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        
        # 中文分词
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        if chinese_chars > len(text) * 0.3:
            # 主要是中文，使用jieba分词
            words = list(jieba.cut(text))
            text = ' '.join(words)
        
        return text.lower()
    
    def calculate_similarity(self, article1_id: str, article2_id: str) -> float:
        """计算两篇文章的语义相似度"""
        if article1_id not in self.article_vectors or article2_id not in self.article_vectors:
            return 0.0
        
        vector1 = self.article_vectors[article1_id]
        vector2 = self.article_vectors[article2_id]
        
        # 计算余弦相似度
        similarity = cosine_similarity(vector1, vector2)[0, 0]
        return float(similarity)
    
    def find_similar_articles(self, article_id: str, threshold: float = 0.3) -> List[Tuple[str, float]]:
        """找到与指定文章相似的文章"""
        if article_id not in self.article_vectors:
            return []
        
        similar_articles = []
        target_vector = self.article_vectors[article_id]
        
        for other_id, other_vector in self.article_vectors.items():
            if other_id != article_id:
                similarity = cosine_similarity(target_vector, other_vector)[0, 0]
                if similarity >= threshold:
                    similar_articles.append((other_id, float(similarity)))
        
        # 按相似度降序排序
        similar_articles.sort(key=lambda x: x[1], reverse=True)
        return similar_articles

class DuplicateDetector:
    """重复内容检测器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent.parent
        self.config_file = Path(__file__).parent / "duplicate_config.json"
        self.results_dir = self.base_dir / "data" / "duplicate_detection"
        
        # 确保目录存在
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self.fingerprint_generator = ContentFingerprint()
        self.semantic_calculator = SemanticSimilarity()
        
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        default_config = {
            "fingerprint_threshold": 0.8,
            "semantic_threshold": 0.7,
            "overall_threshold": 0.75,
            "title_weight": 0.4,
            "content_weight": 0.6,
            "merge_strategy": "keep_latest",  # keep_latest, keep_longest, keep_highest_quality
            "min_content_length": 50,
            "max_similarity_candidates": 100
        }
        
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = {**default_config, **json.load(f)}
        else:
            self.config = default_config
            self.save_config()
    
    def save_config(self):
        """保存配置"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def calculate_fingerprint_similarity(self, fp1: Dict, fp2: Dict) -> float:
        """计算指纹相似度"""
        score = 0.0
        total_weight = 0.0
        
        # 简单哈希匹配（权重最高）
        if fp1['simple_hash'] == fp2['simple_hash']:
            score += 1.0 * 0.4
        total_weight += 0.4
        
        # 标题哈希匹配
        if fp1['title_hash'] == fp2['title_hash']:
            score += 1.0 * 0.3
        total_weight += 0.3
        
        # 关键词哈希匹配
        if fp1['keywords_hash'] == fp2['keywords_hash']:
            score += 1.0 * 0.2
        total_weight += 0.2
        
        # 长度相似性
        len1, len2 = fp1['text_length'], fp2['text_length']
        if len1 > 0 and len2 > 0:
            length_similarity = 1 - abs(len1 - len2) / max(len1, len2)
            score += length_similarity * 0.1
        total_weight += 0.1
        
        return score / total_weight if total_weight > 0 else 0.0
    
    def detect_duplicates(self, articles: List[Dict]) -> List[DuplicateGroup]:
        """检测重复内容"""
        logging.info(f"开始检测 {len(articles)} 篇文章的重复内容...")
        
        # 过滤太短的文章
        filtered_articles = [
            article for article in articles 
            if len(article.get('content', '')) >= self.config['min_content_length']
        ]
        
        logging.info(f"过滤后剩余 {len(filtered_articles)} 篇文章")
        
        # 生成指纹
        fingerprints = {}
        for article in filtered_articles:
            fp = self.fingerprint_generator.generate_fingerprint(
                article.get('title', ''), 
                article.get('content', '')
            )
            fingerprints[article['id']] = fp
        
        # 准备语义相似度计算
        self.semantic_calculator.prepare_corpus(filtered_articles)
        
        # 检测重复
        duplicate_groups = []
        processed_articles = set()
        
        for i, article1 in enumerate(filtered_articles):
            if article1['id'] in processed_articles:
                continue
            
            current_group = [article1]
            similarity_scores = []
            
            for j, article2 in enumerate(filtered_articles[i+1:], i+1):
                if article2['id'] in processed_articles:
                    continue
                
                # 计算相似度
                similarity_result = self._calculate_similarity(
                    article1, article2, 
                    fingerprints[article1['id']], 
                    fingerprints[article2['id']]
                )
                
                if similarity_result.is_duplicate:
                    current_group.append(article2)
                    similarity_scores.append(similarity_result.overall_similarity)
                    processed_articles.add(article2['id'])
            
            # 如果找到重复内容，创建重复组
            if len(current_group) > 1:
                group_id = f"dup_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i}"
                duplicate_group = DuplicateGroup(
                    group_id=group_id,
                    articles=current_group,
                    similarity_scores=similarity_scores,
                    merge_strategy=self.config['merge_strategy']
                )
                
                # 选择主要文章并合并
                self._merge_duplicate_group(duplicate_group)
                duplicate_groups.append(duplicate_group)
                
                processed_articles.add(article1['id'])
        
        logging.info(f"检测完成，发现 {len(duplicate_groups)} 个重复组")
        return duplicate_groups
    
    def _calculate_similarity(self, article1: Dict, article2: Dict, fp1: Dict, fp2: Dict) -> SimilarityResult:
        """计算两篇文章的相似度"""
        # 指纹相似度
        fingerprint_sim = self.calculate_fingerprint_similarity(fp1, fp2)
        
        # 语义相似度
        semantic_sim = self.semantic_calculator.calculate_similarity(
            article1['id'], article2['id']
        )
        
        # 综合相似度
        overall_sim = (
            fingerprint_sim * 0.4 + 
            semantic_sim * 0.6
        )
        
        # 判断是否为重复
        is_duplicate = overall_sim >= self.config['overall_threshold']
        
        return SimilarityResult(
            article1_id=article1['id'],
            article2_id=article2['id'],
            fingerprint_similarity=fingerprint_sim,
            semantic_similarity=semantic_sim,
            overall_similarity=overall_sim,
            is_duplicate=is_duplicate
        )

    def _merge_duplicate_group(self, group: DuplicateGroup):
        """合并重复内容组"""
        if not group.articles:
            return

        strategy = group.merge_strategy

        if strategy == "keep_latest":
            # 保留最新的文章
            primary = max(group.articles, key=lambda x: x.get('publish_date', ''))
        elif strategy == "keep_longest":
            # 保留内容最长的文章
            primary = max(group.articles, key=lambda x: len(x.get('content', '')))
        elif strategy == "keep_highest_quality":
            # 保留质量最高的文章（基于来源可信度等）
            primary = self._select_highest_quality_article(group.articles)
        else:
            # 默认保留第一篇
            primary = group.articles[0]

        group.primary_article = primary

        # 创建合并后的内容
        merged_content = {
            'id': f"merged_{group.group_id}",
            'title': primary['title'],
            'content': primary['content'],
            'url': primary['url'],
            'source': primary['source'],
            'publish_date': primary['publish_date'],
            'merged_from': [article['id'] for article in group.articles],
            'merge_strategy': strategy,
            'duplicate_count': len(group.articles)
        }

        group.merged_content = merged_content

        logging.info(f"合并重复组 {group.group_id}，保留文章 {primary['id']}，合并了 {len(group.articles)} 篇文章")

    def _select_highest_quality_article(self, articles: List[Dict]) -> Dict:
        """选择质量最高的文章"""
        # 简单的质量评分算法
        def quality_score(article):
            score = 0

            # 内容长度得分
            content_length = len(article.get('content', ''))
            score += min(content_length / 1000, 1.0) * 30

            # 标题质量得分
            title = article.get('title', '')
            if len(title) > 10:
                score += 20

            # 来源可信度得分
            source = article.get('source', '').lower()
            trusted_sources = ['hawaii news now', 'honolulu star-advertiser', 'hawaii tribune-herald']
            if any(trusted in source for trusted in trusted_sources):
                score += 30

            # 发布时间得分（越新越好）
            try:
                publish_date = article.get('publish_date', '')
                if publish_date:
                    score += 20
            except:
                pass

            return score

        return max(articles, key=quality_score)

    def save_detection_results(self, duplicate_groups: List[DuplicateGroup]) -> str:
        """保存检测结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = self.results_dir / f"duplicate_detection_{timestamp}.json"

        results = {
            'timestamp': datetime.now().isoformat(),
            'total_groups': len(duplicate_groups),
            'total_duplicates': sum(len(group.articles) for group in duplicate_groups),
            'config': self.config,
            'groups': []
        }

        for group in duplicate_groups:
            group_data = {
                'group_id': group.group_id,
                'article_count': len(group.articles),
                'similarity_scores': group.similarity_scores,
                'merge_strategy': group.merge_strategy,
                'articles': group.articles,
                'primary_article': group.primary_article,
                'merged_content': group.merged_content
            }
            results['groups'].append(group_data)

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        logging.info(f"检测结果已保存到: {results_file}")
        return str(results_file)

    def generate_detection_report(self, duplicate_groups: List[DuplicateGroup]) -> str:
        """生成检测报告"""
        total_articles = sum(len(group.articles) for group in duplicate_groups)
        total_groups = len(duplicate_groups)

        avg_duplicates = total_articles / total_groups if total_groups > 0 else 0

        report = f"""
🔄 重复内容智能去除报告
{'=' * 50}

📊 检测统计:
- 发现重复组数量: {total_groups}
- 涉及重复文章总数: {total_articles}
- 平均每组重复数: {avg_duplicates:.1f}

📈 重复分布:
"""

        # 按重复数量分组统计
        if total_groups > 0:
            size_distribution = defaultdict(int)
            for group in duplicate_groups:
                size_distribution[len(group.articles)] += 1

            for size, count in sorted(size_distribution.items()):
                report += f"- {size}篇重复: {count} 组\n"

            # 显示前5个重复组的详细信息
            report += f"\n📰 重复组详细信息 (前5组):\n"
            for i, group in enumerate(duplicate_groups[:5]):
                report += f"\n{i+1}. 重复组 {group.group_id}:\n"
                report += f"   - 重复文章数: {len(group.articles)}\n"
                report += f"   - 合并策略: {group.merge_strategy}\n"
                if group.primary_article:
                    report += f"   - 保留文章: {group.primary_article['title'][:50]}...\n"

                if group.similarity_scores:
                    avg_similarity = sum(group.similarity_scores) / len(group.similarity_scores)
                    report += f"   - 平均相似度: {avg_similarity:.3f}\n"

                report += f"   - 重复文章列表:\n"
                for j, article in enumerate(group.articles):
                    if group.primary_article:
                        status = "✅ 保留" if article['id'] == group.primary_article['id'] else "❌ 去除"
                    else:
                        status = "📝 待处理"
                    report += f"     {j+1}. {status} {article['title'][:40]}...\n"
        else:
            report += "✅ 未发现重复内容，所有文章都是唯一的！\n"

        report += f"\n📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

        return report

class DuplicateDetectionIntegration:
    """重复检测集成类"""

    def __init__(self):
        self.detector = DuplicateDetector()
        self.base_dir = Path(__file__).parent.parent.parent
        self.freshrss_db = self.base_dir / "services" / "freshrss" / "data" / "users" / "aloha" / "db.sqlite"

    def get_articles_from_rss(self, limit: int = 100) -> List[Dict]:
        """从RSS获取文章"""
        articles = []

        if not self.freshrss_db.exists():
            logging.warning(f"FreshRSS数据库不存在: {self.freshrss_db}")
            return articles

        try:
            conn = sqlite3.connect(self.freshrss_db)
            cursor = conn.cursor()

            query = """
            SELECT id, title, content, link, date, author
            FROM entry
            ORDER BY date DESC
            LIMIT ?
            """

            cursor.execute(query, (limit,))
            rows = cursor.fetchall()

            for row in rows:
                article_id, title, content, link, date, author = row

                # 清理内容
                clean_content = self._clean_html_content(content or "")

                article = {
                    'id': str(article_id),
                    'title': title or "",
                    'content': clean_content,
                    'url': link or "",
                    'source': author or "Unknown",
                    'publish_date': date or ""
                }
                articles.append(article)

            conn.close()
            logging.info(f"从FreshRSS获取了 {len(articles)} 篇文章")

        except Exception as e:
            logging.error(f"读取FreshRSS数据库失败: {str(e)}")

        return articles

    def _clean_html_content(self, html_content: str) -> str:
        """清理HTML内容"""
        import re
        clean_text = re.sub(r'<[^>]+>', '', html_content)
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        return clean_text

    def run_duplicate_detection(self, limit: int = 50) -> Dict:
        """运行重复检测"""
        logging.info("开始重复内容检测任务...")

        # 获取文章
        articles = self.get_articles_from_rss(limit)
        if not articles:
            return {"success": False, "message": "没有找到文章"}

        # 执行重复检测
        duplicate_groups = self.detector.detect_duplicates(articles)

        # 保存结果
        results_file = self.detector.save_detection_results(duplicate_groups)

        # 生成报告
        report = self.detector.generate_detection_report(duplicate_groups)

        # 保存报告
        report_file = self.detector.results_dir / "duplicate_detection_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        return {
            "success": True,
            "total_articles": len(articles),
            "duplicate_groups": len(duplicate_groups),
            "total_duplicates": sum(len(group.articles) for group in duplicate_groups),
            "results_file": results_file,
            "report_file": str(report_file),
            "report": report
        }

def main():
    """主函数"""
    print("🔄 重复内容智能去除系统")
    print("=" * 50)

    integration = DuplicateDetectionIntegration()
    results = integration.run_duplicate_detection(limit=30)

    if results["success"]:
        print(f"✅ 重复检测完成!")
        print(f"📊 处理文章: {results['total_articles']} 篇")
        print(f"🔍 发现重复组: {results['duplicate_groups']} 个")
        print(f"📝 重复文章总数: {results['total_duplicates']} 篇")
        print(f"📄 详细报告: {results['report_file']}")
        print("\n" + results["report"])
    else:
        print(f"❌ 检测失败: {results['message']}")

if __name__ == "__main__":
    main()
