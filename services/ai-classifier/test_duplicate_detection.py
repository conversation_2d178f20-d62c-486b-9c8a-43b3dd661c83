#!/usr/bin/env python3
"""
重复内容检测系统测试
创建测试数据验证重复检测功能
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from duplicate_detector import DuplicateDetector

def create_test_articles():
    """创建测试文章数据"""
    test_articles = [
        {
            'id': 'test_1',
            'title': '夏威夷州政府宣布新的旅游政策',
            'content': '夏威夷州政府今天宣布了一项新的旅游政策，旨在保护当地环境和文化。该政策将于下个月开始实施，要求所有游客在入境时接受环保教育。',
            'url': 'http://example.com/1',
            'source': 'Hawaii News Now',
            'publish_date': '2025-07-28 10:00:00'
        },
        {
            'id': 'test_2',
            'title': '夏威夷州政府发布新旅游政策',
            'content': '夏威夷州政府昨日发布了一项新的旅游政策，目的是保护本地环境和传统文化。新政策将在下月生效，所有入境游客需要接受环保培训。',
            'url': 'http://example.com/2',
            'source': 'Honolulu Star-Advertiser',
            'publish_date': '2025-07-28 11:00:00'
        },
        {
            'id': 'test_3',
            'title': '檀香山华人社区举办春节庆祝活动',
            'content': '檀香山华人社区将在下周举办盛大的春节庆祝活动，包括舞龙舞狮表演、传统美食展示和文化交流活动。',
            'url': 'http://example.com/3',
            'source': 'Chinese Community News',
            'publish_date': '2025-07-28 12:00:00'
        },
        {
            'id': 'test_4',
            'title': '夏威夷新旅游规定出台',
            'content': '为了更好地保护夏威夷的自然环境和本土文化，州政府推出了新的旅游管理规定。游客需要在抵达时参加环保意识培训课程。',
            'url': 'http://example.com/4',
            'source': 'Hawaii Tribune-Herald',
            'publish_date': '2025-07-28 13:00:00'
        },
        {
            'id': 'test_5',
            'title': '夏威夷大学招聘新教授',
            'content': '夏威夷大学马诺阿分校正在招聘计算机科学系新教授，要求博士学位和相关研究经验。',
            'url': 'http://example.com/5',
            'source': 'University of Hawaii',
            'publish_date': '2025-07-28 14:00:00'
        }
    ]
    
    return test_articles

def main():
    """主测试函数"""
    print("🧪 重复内容检测系统测试")
    print("=" * 50)
    
    # 创建测试数据
    test_articles = create_test_articles()
    print(f"📝 创建了 {len(test_articles)} 篇测试文章")
    
    # 初始化检测器
    detector = DuplicateDetector()
    
    # 执行重复检测
    duplicate_groups = detector.detect_duplicates(test_articles)
    
    # 生成报告
    report = detector.generate_detection_report(duplicate_groups)
    
    print(f"\n🔍 检测结果:")
    print(f"- 发现重复组: {len(duplicate_groups)} 个")
    print(f"- 重复文章总数: {sum(len(group.articles) for group in duplicate_groups)} 篇")
    
    print(f"\n📊 详细报告:")
    print(report)
    
    # 保存测试结果
    results_file = detector.save_detection_results(duplicate_groups)
    print(f"\n💾 测试结果已保存到: {results_file}")

if __name__ == "__main__":
    main()
