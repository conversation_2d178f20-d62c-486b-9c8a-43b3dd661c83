#!/usr/bin/env python3
"""
RSS重复内容检测集成脚本
将重复检测器与FreshRSS系统集成
"""

import sys
import json
import logging
from pathlib import Path
from datetime import datetime

# 添加模块路径
sys.path.append(str(Path(__file__).parent))
from duplicate_detector import DuplicateDetectionIntegration

class RSSDeduplicationService:
    """RSS去重服务"""
    
    def __init__(self):
        self.integration = DuplicateDetectionIntegration()
        self.base_dir = Path(__file__).parent.parent.parent
        self.service_log = self.base_dir / "data" / "logs" / "deduplication_service.log"
        
        # 确保日志目录存在
        self.service_log.parent.mkdir(parents=True, exist_ok=True)
        
        # 配置服务日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.service_log),
                logging.StreamHandler()
            ]
        )
    
    def run_daily_deduplication(self, limit: int = 100) -> dict:
        """运行每日去重任务"""
        logging.info("开始每日重复内容去重任务...")
        
        try:
            # 执行重复检测
            results = self.integration.run_duplicate_detection(limit)
            
            if results["success"]:
                # 记录去重统计
                stats = {
                    "timestamp": datetime.now().isoformat(),
                    "processed_articles": results["total_articles"],
                    "duplicate_groups": results["duplicate_groups"],
                    "total_duplicates": results["total_duplicates"],
                    "deduplication_rate": results["total_duplicates"] / results["total_articles"] * 100 if results["total_articles"] > 0 else 0
                }
                
                # 保存统计信息
                stats_file = self.base_dir / "data" / "deduplication_stats.json"
                with open(stats_file, 'w', encoding='utf-8') as f:
                    json.dump(stats, f, indent=2, ensure_ascii=False)
                
                logging.info(f"去重完成: 处理 {results['total_articles']} 篇文章，发现 {results['duplicate_groups']} 个重复组")
                
                return {
                    "success": True,
                    "message": "去重任务完成",
                    "stats": stats,
                    "results": results
                }
            else:
                logging.error(f"去重任务失败: {results.get('message', '未知错误')}")
                return {"success": False, "message": results.get("message", "未知错误")}
                
        except Exception as e:
            logging.error(f"去重任务异常: {str(e)}")
            return {"success": False, "message": f"系统异常: {str(e)}"}
    
    def generate_deduplication_summary(self) -> str:
        """生成去重摘要报告"""
        stats_file = self.base_dir / "data" / "deduplication_stats.json"
        
        if not stats_file.exists():
            return "暂无去重统计数据"
        
        try:
            with open(stats_file, 'r', encoding='utf-8') as f:
                stats = json.load(f)
            
            summary = f"""
📊 重复内容去除摘要报告
{'=' * 40}

🕐 最后执行时间: {stats['timestamp']}
📝 处理文章总数: {stats['processed_articles']} 篇
🔍 发现重复组数: {stats['duplicate_groups']} 个
❌ 重复文章总数: {stats['total_duplicates']} 篇
📈 重复率: {stats['deduplication_rate']:.1f}%

💡 系统状态: {'✅ 正常运行' if stats['duplicate_groups'] >= 0 else '❌ 异常'}
"""
            
            return summary
            
        except Exception as e:
            return f"读取统计数据失败: {str(e)}"

def main():
    """主函数"""
    print("🔄 RSS重复内容检测集成服务")
    print("=" * 50)
    
    service = RSSDeduplicationService()
    
    # 运行去重任务
    results = service.run_daily_deduplication(limit=50)
    
    if results["success"]:
        print("✅ 去重任务执行成功!")
        print(f"📊 处理文章: {results['stats']['processed_articles']} 篇")
        print(f"🔍 发现重复组: {results['stats']['duplicate_groups']} 个")
        print(f"📈 重复率: {results['stats']['deduplication_rate']:.1f}%")
        
        # 显示摘要报告
        summary = service.generate_deduplication_summary()
        print(summary)
        
    else:
        print(f"❌ 去重任务失败: {results['message']}")

if __name__ == "__main__":
    main()
