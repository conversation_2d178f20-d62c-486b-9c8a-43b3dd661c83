#!/usr/bin/env python3
"""
重复内容检测调试测试
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from duplicate_detector import DuplicateDetector, ContentFingerprint, SemanticSimilarity

def create_obvious_duplicates():
    """创建明显重复的测试文章"""
    test_articles = [
        {
            'id': 'original',
            'title': '夏威夷州政府宣布新的旅游政策',
            'content': '夏威夷州政府今天宣布了一项新的旅游政策，旨在保护当地环境和文化。该政策将于下个月开始实施，要求所有游客在入境时接受环保教育。这项政策得到了当地居民的广泛支持。',
            'url': 'http://example.com/1',
            'source': 'Hawaii News Now',
            'publish_date': '2025-07-28 10:00:00'
        },
        {
            'id': 'duplicate1',
            'title': '夏威夷州政府宣布新的旅游政策',
            'content': '夏威夷州政府今天宣布了一项新的旅游政策，旨在保护当地环境和文化。该政策将于下个月开始实施，要求所有游客在入境时接受环保教育。这项政策得到了当地居民的广泛支持。',
            'url': 'http://example.com/2',
            'source': 'Honolulu Star-Advertiser',
            'publish_date': '2025-07-28 11:00:00'
        },
        {
            'id': 'similar',
            'title': '夏威夷新旅游政策发布',
            'content': '夏威夷州政府昨日发布了新的旅游管理政策，目标是保护本地环境和传统文化。新政策下月生效，所有入境游客需要接受环保培训。当地民众对此表示支持。',
            'url': 'http://example.com/3',
            'source': 'Hawaii Tribune-Herald',
            'publish_date': '2025-07-28 12:00:00'
        },
        {
            'id': 'different',
            'title': '檀香山华人社区举办春节庆祝活动',
            'content': '檀香山华人社区将在下周举办盛大的春节庆祝活动，包括舞龙舞狮表演、传统美食展示和文化交流活动。预计将有数千人参加这次庆祝活动。',
            'url': 'http://example.com/4',
            'source': 'Chinese Community News',
            'publish_date': '2025-07-28 13:00:00'
        }
    ]
    
    return test_articles

def debug_similarity_calculation():
    """调试相似度计算"""
    print("🔍 调试相似度计算过程")
    print("=" * 50)
    
    articles = create_obvious_duplicates()
    
    # 测试指纹生成
    fingerprint_gen = ContentFingerprint()
    fingerprints = {}
    
    print("📝 生成内容指纹:")
    for article in articles:
        fp = fingerprint_gen.generate_fingerprint(article['title'], article['content'])
        fingerprints[article['id']] = fp
        print(f"- {article['id']}: {fp['simple_hash'][:8]}... (长度: {fp['text_length']})")
    
    # 测试语义相似度
    semantic_calc = SemanticSimilarity()
    semantic_calc.prepare_corpus(articles)
    
    print(f"\n🧮 计算语义相似度:")
    for i, article1 in enumerate(articles):
        for j, article2 in enumerate(articles[i+1:], i+1):
            semantic_sim = semantic_calc.calculate_similarity(article1['id'], article2['id'])
            print(f"- {article1['id']} vs {article2['id']}: {semantic_sim:.3f}")
    
    # 测试完整检测
    print(f"\n🔄 完整重复检测:")
    detector = DuplicateDetector()
    duplicate_groups = detector.detect_duplicates(articles)
    
    print(f"发现重复组: {len(duplicate_groups)} 个")
    for group in duplicate_groups:
        print(f"- 组 {group.group_id}: {len(group.articles)} 篇文章")
        for article in group.articles:
            print(f"  * {article['id']}: {article['title'][:30]}...")

if __name__ == "__main__":
    debug_similarity_calculation()
