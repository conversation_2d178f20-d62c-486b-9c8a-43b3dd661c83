#!/usr/bin/env python3
"""
本地LLM API服务器
为n8n工作流提供AI驱动的自动化功能
"""

from flask import Flask, request, jsonify
import requests
import json
import sys
import subprocess
import os
from pathlib import Path
from datetime import datetime
import logging

# 添加AI模块路径
sys.path.append(str(Path(__file__).parent))
from content_classifier import HawaiiContentClassifier
from duplicate_detector import DuplicateDetectionIntegration

app = Flask(__name__)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化AI模块
try:
    classifier = HawaiiContentClassifier()
    duplicate_integration = DuplicateDetectionIntegration()
    logger.info("✅ AI模块初始化成功")
except Exception as e:
    logger.error(f"❌ AI模块初始化失败: {str(e)}")
    classifier = None
    duplicate_integration = None

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'services': {
            'classifier': classifier is not None,
            'duplicate_detector': duplicate_integration is not None,
            'ollama': check_ollama_health()
        }
    })

def check_ollama_health():
    """检查Ollama服务状态"""
    try:
        response = requests.get('http://localhost:11434/api/tags', timeout=5)
        return response.status_code == 200
    except:
        return False

@app.route('/classify', methods=['POST'])
def classify_content():
    """内容分类API"""
    try:
        data = request.json
        title = data.get('title', '')
        content = data.get('content', '')

        if not classifier:
            return jsonify({
                'error': '分类器未初始化',
                'success': False
            }), 500

        result = classifier.classify_content(title, content)

        if result:
            return jsonify({
                'category': result.category,
                'subcategory': result.subcategory,
                'confidence': result.confidence,
                'success': True,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'category': 'unknown',
                'subcategory': 'unknown',
                'confidence': 0.0,
                'success': False,
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"分类失败: {str(e)}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/detect-duplicate', methods=['POST'])
def detect_duplicate():
    """重复检测API"""
    try:
        data = request.json
        articles = data.get('articles', [])

        if not duplicate_integration:
            return jsonify({
                'error': '重复检测器未初始化',
                'success': False
            }), 500

        duplicate_groups = duplicate_integration.detector.detect_duplicates(articles)

        return jsonify({
            'duplicate_groups': len(duplicate_groups),
            'total_duplicates': sum(len(group.articles) for group in duplicate_groups),
            'is_duplicate': len(duplicate_groups) > 0,
            'success': True,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"重复检测失败: {str(e)}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/ollama-generate', methods=['POST'])
def ollama_generate():
    """Ollama文本生成API"""
    try:
        data = request.json
        model = data.get('model', 'llama3.2:3b')
        prompt = data.get('prompt', '')

        if not prompt:
            return jsonify({'error': '缺少prompt参数', 'success': False}), 400

        # 调用Ollama API
        ollama_data = {
            'model': model,
            'prompt': prompt,
            'stream': False
        }

        response = requests.post(
            'http://localhost:11434/api/generate',
            json=ollama_data,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            return jsonify({
                'response': result.get('response', ''),
                'model': model,
                'success': True,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'error': f'Ollama API错误: {response.status_code}',
                'success': False
            }), 500

    except Exception as e:
        logger.error(f"Ollama生成失败: {str(e)}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/scrape-website', methods=['POST'])
def scrape_website():
    """网站爬取API"""
    try:
        data = request.json
        url = data.get('url', '')

        if not url:
            return jsonify({'error': '缺少URL参数', 'success': False}), 400

        # 使用requests爬取网页
        headers = {
            'User-Agent': 'HawaiiHub-Bot/1.0 (Hawaiian Chinese Community Platform)'
        }

        response = requests.get(url, headers=headers, timeout=30)

        if response.status_code == 200:
            # 简单的内容提取
            content = response.text

            # 使用Ollama提取关键信息
            extract_prompt = f"""
请从以下HTML内容中提取关键信息，包括标题、正文、发布时间等：

{content[:2000]}...

请以JSON格式返回：
{{
    "title": "文章标题",
    "content": "主要内容",
    "publish_date": "发布时间",
    "summary": "内容摘要"
}}
"""

            ollama_response = requests.post(
                'http://localhost:11434/api/generate',
                json={
                    'model': 'llama3.2:3b',
                    'prompt': extract_prompt,
                    'stream': False
                },
                timeout=60
            )

            extracted_info = {}
            if ollama_response.status_code == 200:
                try:
                    llm_result = ollama_response.json()
                    extracted_info = json.loads(llm_result.get('response', '{}'))
                except:
                    extracted_info = {
                        'title': 'Unknown',
                        'content': content[:1000],
                        'publish_date': datetime.now().isoformat(),
                        'summary': 'Content extracted from ' + url
                    }

            return jsonify({
                'url': url,
                'status_code': response.status_code,
                'extracted_info': extracted_info,
                'success': True,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'error': f'HTTP错误: {response.status_code}',
                'success': False
            }), 400

    except Exception as e:
        logger.error(f"网站爬取失败: {str(e)}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/execute-command', methods=['POST'])
def execute_command():
    """执行系统命令API"""
    try:
        data = request.json
        command = data.get('command', '')
        cwd = data.get('cwd', '/Users/<USER>/Documents/华人平台/hawaiihub.net')

        if not command:
            return jsonify({'error': '缺少command参数', 'success': False}), 400

        # 安全检查 - 只允许特定命令
        allowed_commands = [
            'git pull',
            'npm install',
            'pip install',
            'python3',
            'node',
            'ls',
            'pwd',
            'ps aux',
            'docker',
            'container'
        ]

        command_start = command.split()[0] if command.split() else ''
        if not any(command.startswith(allowed) for allowed in allowed_commands):
            return jsonify({
                'error': f'命令不被允许: {command_start}',
                'success': False
            }), 403

        # 执行命令
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=60
        )

        return jsonify({
            'command': command,
            'cwd': cwd,
            'return_code': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'success': result.returncode == 0,
            'timestamp': datetime.now().isoformat()
        })

    except subprocess.TimeoutExpired:
        return jsonify({
            'error': '命令执行超时',
            'success': False
        }), 408
    except Exception as e:
        logger.error(f"命令执行失败: {str(e)}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/translate', methods=['POST'])
def translate_content():
    """内容翻译API"""
    try:
        data = request.json
        text = data.get('text', '')
        target_lang = data.get('target_lang', 'chinese')

        if not text:
            return jsonify({'error': '缺少text参数', 'success': False}), 400

        # 构建翻译提示
        if target_lang.lower() in ['chinese', 'zh', '中文']:
            prompt = f"请将以下英文内容翻译为简体中文，保持原意和专业性：\n\n{text}"
        else:
            prompt = f"请将以下中文内容翻译为英文，保持原意和专业性：\n\n{text}"

        # 调用Ollama进行翻译
        response = requests.post(
            'http://localhost:11434/api/generate',
            json={
                'model': 'llama3.2:3b',
                'prompt': prompt,
                'stream': False
            },
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            return jsonify({
                'original_text': text,
                'translated_text': result.get('response', ''),
                'target_lang': target_lang,
                'success': True,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'error': f'翻译服务错误: {response.status_code}',
                'success': False
            }), 500

    except Exception as e:
        logger.error(f"翻译失败: {str(e)}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/summarize', methods=['POST'])
def summarize_content():
    """内容摘要API"""
    try:
        data = request.json
        text = data.get('text', '')
        max_length = data.get('max_length', 200)

        if not text:
            return jsonify({'error': '缺少text参数', 'success': False}), 400

        # 构建摘要提示
        prompt = f"请为以下内容生成简洁的中文摘要（不超过{max_length}字）：\n\n{text}"

        # 调用Ollama生成摘要
        response = requests.post(
            'http://localhost:11434/api/generate',
            json={
                'model': 'llama3.2:3b',
                'prompt': prompt,
                'stream': False
            },
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            return jsonify({
                'original_text': text,
                'summary': result.get('response', ''),
                'max_length': max_length,
                'success': True,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'error': f'摘要服务错误: {response.status_code}',
                'success': False
            }), 500

    except Exception as e:
        logger.error(f"摘要生成失败: {str(e)}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api-docs', methods=['GET'])
def api_docs():
    """API文档"""
    docs = {
        'title': 'HawaiiHub本地LLM API服务器',
        'version': '1.0.0',
        'description': '为n8n工作流提供AI驱动的自动化功能',
        'endpoints': {
            '/health': {
                'method': 'GET',
                'description': '健康检查',
                'response': 'JSON格式的服务状态'
            },
            '/classify': {
                'method': 'POST',
                'description': '内容智能分类',
                'parameters': {
                    'title': '文章标题',
                    'content': '文章内容'
                }
            },
            '/detect-duplicate': {
                'method': 'POST',
                'description': '重复内容检测',
                'parameters': {
                    'articles': '文章列表'
                }
            },
            '/ollama-generate': {
                'method': 'POST',
                'description': 'LLM文本生成',
                'parameters': {
                    'model': '模型名称（可选）',
                    'prompt': '输入提示'
                }
            },
            '/scrape-website': {
                'method': 'POST',
                'description': '网站内容爬取',
                'parameters': {
                    'url': '目标网站URL'
                }
            },
            '/execute-command': {
                'method': 'POST',
                'description': '执行系统命令',
                'parameters': {
                    'command': '要执行的命令',
                    'cwd': '工作目录（可选）'
                }
            },
            '/translate': {
                'method': 'POST',
                'description': '内容翻译',
                'parameters': {
                    'text': '要翻译的文本',
                    'target_lang': '目标语言（可选）'
                }
            },
            '/summarize': {
                'method': 'POST',
                'description': '内容摘要',
                'parameters': {
                    'text': '要摘要的文本',
                    'max_length': '最大长度（可选）'
                }
            }
        }
    }

    return jsonify(docs)

if __name__ == '__main__':
    print("🚀 启动HawaiiHub本地LLM API服务器...")
    print("📡 服务地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/api-docs")
    print("🏥 健康检查: http://localhost:8000/health")

    app.run(host='0.0.0.0', port=8000, debug=False)
