#!/usr/bin/env python3
"""
夏威夷华人平台 - 智能内容分类系统
使用AI API进行内容自动分类和标签生成
"""

import os
import sys
import json
import logging
import hashlib
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

# 确保日志目录存在
log_dir = Path("../../data/logs")
log_dir.mkdir(parents=True, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'ai_classifier.log'),
        logging.StreamHandler()
    ]
)

@dataclass
class ClassificationResult:
    """分类结果数据类"""
    category: str
    subcategory: str
    tags: List[str]
    confidence: float
    language: str
    reasoning: str

@dataclass
class ContentItem:
    """内容项数据类"""
    id: str
    title: str
    content: str
    url: str
    source: str
    publish_date: Optional[str] = None
    classification: Optional[ClassificationResult] = None

class HawaiiContentClassifier:
    """夏威夷华人平台内容分类器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent.parent
        self.config_file = Path(__file__).parent / "classifier_config.json"
        self.cache_dir = self.base_dir / "data" / "classification_cache"
        
        # 确保目录存在
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 分类体系定义
        self.categories = {
            "hawaii_local": {
                "name": "🏝️ 夏威夷本地新闻",
                "subcategories": {
                    "government": "政府政策",
                    "weather": "天气气候", 
                    "tourism": "旅游观光",
                    "economy": "经济发展",
                    "education": "教育资讯",
                    "health": "医疗健康",
                    "transportation": "交通出行",
                    "environment": "环境保护"
                }
            },
            "chinese_community": {
                "name": "🏠 华人社区动态",
                "subcategories": {
                    "events": "社区活动",
                    "culture": "文化传承",
                    "business": "华人商业",
                    "immigration": "移民资讯",
                    "language": "中文教育",
                    "festival": "节日庆典",
                    "association": "社团组织",
                    "volunteer": "志愿服务"
                }
            },
            "employment": {
                "name": "💼 招聘信息",
                "subcategories": {
                    "fulltime": "全职工作",
                    "parttime": "兼职工作",
                    "internship": "实习机会",
                    "freelance": "自由职业",
                    "restaurant": "餐饮服务",
                    "retail": "零售销售",
                    "healthcare": "医疗护理",
                    "education": "教育培训"
                }
            },
            "life_services": {
                "name": "🛍️ 生活服务",
                "subcategories": {
                    "housing": "房屋租售",
                    "transportation": "交通出行",
                    "food": "美食餐饮",
                    "shopping": "购物消费",
                    "healthcare": "医疗服务",
                    "education": "教育培训",
                    "legal": "法律咨询",
                    "finance": "金融理财"
                }
            },
            "entertainment": {
                "name": "🎭 娱乐休闲",
                "subcategories": {
                    "events": "活动演出",
                    "sports": "体育运动",
                    "arts": "艺术文化",
                    "music": "音乐表演",
                    "movies": "影视娱乐",
                    "outdoor": "户外活动",
                    "nightlife": "夜生活",
                    "hobbies": "兴趣爱好"
                }
            }
        }
        
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "ai_provider": "mock",  # mock, openai, anthropic, local
            "openai_api_key": "",
            "anthropic_api_key": "",
            "model_name": "gpt-3.5-turbo",
            "max_tokens": 1000,
            "temperature": 0.3,
            "confidence_threshold": 0.7,
            "cache_enabled": True,
            "cache_ttl_hours": 24,
            "batch_size": 10,
            "rate_limit_delay": 1.0
        }
        
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = {**default_config, **json.load(f)}
        else:
            self.config = default_config
            self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)

    def get_cache_key(self, content: str) -> str:
        """生成内容缓存键"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()

    def get_cached_classification(self, content: str) -> Optional[ClassificationResult]:
        """获取缓存的分类结果"""
        if not self.config["cache_enabled"]:
            return None

        cache_key = self.get_cache_key(content)
        cache_file = self.cache_dir / f"{cache_key}.json"

        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 检查缓存是否过期
                cache_time = datetime.fromisoformat(data["timestamp"])
                if (datetime.now() - cache_time).total_seconds() < self.config["cache_ttl_hours"] * 3600:
                    return ClassificationResult(**data["classification"])
            except Exception as e:
                logging.warning(f"读取缓存失败: {str(e)}")

        return None

    def save_classification_cache(self, content: str, result: ClassificationResult):
        """保存分类结果到缓存"""
        if not self.config["cache_enabled"]:
            return

        cache_key = self.get_cache_key(content)
        cache_file = self.cache_dir / f"{cache_key}.json"

        try:
            cache_data = {
                "timestamp": datetime.now().isoformat(),
                "classification": {
                    "category": result.category,
                    "subcategory": result.subcategory,
                    "tags": result.tags,
                    "confidence": result.confidence,
                    "language": result.language,
                    "reasoning": result.reasoning
                }
            }

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logging.warning(f"保存缓存失败: {str(e)}")

    def build_classification_prompt(self, title: str, content: str) -> str:
        """构建分类提示词"""
        categories_text = ""
        for cat_id, cat_info in self.categories.items():
            categories_text += f"\n{cat_info['name']} ({cat_id}):\n"
            for sub_id, sub_name in cat_info['subcategories'].items():
                categories_text += f"  - {sub_name} ({sub_id})\n"

        prompt = f"""
你是夏威夷华人平台的内容分类专家。请对以下内容进行智能分类：

标题: {title}
内容: {content[:1000]}...

可用分类体系：{categories_text}

请返回JSON格式的分类结果，包含：
1. category: 主分类ID
2. subcategory: 子分类ID
3. tags: 相关标签列表（3-5个）
4. confidence: 置信度（0-1）
5. language: 内容语言（zh/en）
6. reasoning: 分类理由

要求：
- 重点关注夏威夷本地相关性
- 考虑华人社区的特殊需求
- 标签要具体且有用
- 置信度要真实反映分类确定性

返回格式：
{{
  "category": "分类ID",
  "subcategory": "子分类ID",
  "tags": ["标签1", "标签2", "标签3"],
  "confidence": 0.85,
  "language": "zh",
  "reasoning": "分类理由说明"
}}
"""
        return prompt

    def classify_with_mock(self, title: str, content: str) -> Optional[ClassificationResult]:
        """使用模拟分类器进行分类（用于测试）"""
        import random

        # 基于关键词的简单分类逻辑
        title_content = f"{title} {content}".lower()

        # 夏威夷本地新闻关键词
        hawaii_keywords = ["hawaii", "honolulu", "maui", "oahu", "kauai", "aloha", "夏威夷", "檀香山"]
        # 华人社区关键词
        chinese_keywords = ["chinese", "华人", "中文", "春节", "中秋", "社区", "文化"]
        # 招聘关键词
        job_keywords = ["job", "hiring", "employment", "work", "招聘", "工作", "职位"]
        # 生活服务关键词
        service_keywords = ["service", "restaurant", "shop", "house", "rent", "服务", "餐厅", "房屋"]

        # 计算关键词匹配度
        hawaii_score = sum(1 for kw in hawaii_keywords if kw in title_content)
        chinese_score = sum(1 for kw in chinese_keywords if kw in title_content)
        job_score = sum(1 for kw in job_keywords if kw in title_content)
        service_score = sum(1 for kw in service_keywords if kw in title_content)

        # 确定主分类
        scores = {
            "hawaii_local": hawaii_score,
            "chinese_community": chinese_score,
            "employment": job_score,
            "life_services": service_score,
            "entertainment": 0
        }

        category = max(scores, key=scores.get)
        confidence = min(0.9, max(0.6, scores[category] * 0.2 + 0.5))

        # 随机选择子分类
        subcategories = list(self.categories[category]["subcategories"].keys())
        subcategory = random.choice(subcategories)

        # 生成标签
        tags = []
        if hawaii_score > 0:
            tags.append("夏威夷")
        if chinese_score > 0:
            tags.append("华人社区")
        if job_score > 0:
            tags.append("招聘")
        if service_score > 0:
            tags.append("生活服务")

        # 补充通用标签
        while len(tags) < 3:
            generic_tags = ["本地", "资讯", "社区", "服务", "信息"]
            tag = random.choice(generic_tags)
            if tag not in tags:
                tags.append(tag)

        # 检测语言
        chinese_chars = sum(1 for char in title_content if '\u4e00' <= char <= '\u9fff')
        language = "zh" if chinese_chars > 10 else "en"

        return ClassificationResult(
            category=category,
            subcategory=subcategory,
            tags=tags[:5],
            confidence=confidence,
            language=language,
            reasoning=f"基于关键词匹配：{category}分类得分{scores[category]}"
        )

    def classify_content(self, title: str, content: str) -> Optional[ClassificationResult]:
        """分类内容"""
        # 检查缓存
        full_content = f"{title}\n{content}"
        cached_result = self.get_cached_classification(full_content)
        if cached_result:
            logging.info("使用缓存的分类结果")
            return cached_result

        # 执行分类
        result = None
        if self.config["ai_provider"] == "mock":
            result = self.classify_with_mock(title, content)
        elif self.config["ai_provider"] == "openai":
            # TODO: 实现OpenAI分类
            logging.warning("OpenAI分类尚未实现，使用模拟分类")
            result = self.classify_with_mock(title, content)
        elif self.config["ai_provider"] == "anthropic":
            # TODO: 实现Anthropic分类
            logging.warning("Anthropic分类尚未实现，使用模拟分类")
            result = self.classify_with_mock(title, content)

        # 保存到缓存
        if result and result.confidence >= self.config["confidence_threshold"]:
            self.save_classification_cache(full_content, result)
            logging.info(f"分类成功: {result.category}/{result.subcategory} (置信度: {result.confidence:.2f})")
        else:
            logging.warning(f"分类置信度过低或失败: {result.confidence if result else 'None'}")

        return result

    def classify_batch(self, content_items: List[ContentItem]) -> List[ContentItem]:
        """批量分类内容"""
        classified_items = []

        for i, item in enumerate(content_items):
            try:
                logging.info(f"正在分类第 {i+1}/{len(content_items)} 项: {item.title[:50]}...")

                result = self.classify_content(item.title, item.content)
                item.classification = result
                classified_items.append(item)

                # 速率限制
                if i < len(content_items) - 1:
                    import time
                    time.sleep(self.config["rate_limit_delay"])

            except Exception as e:
                logging.error(f"分类失败: {item.title[:50]}... - {str(e)}")
                classified_items.append(item)

        return classified_items

    def get_classification_stats(self) -> Dict:
        """获取分类统计信息"""
        stats = {
            "total_cached": 0,
            "categories": {},
            "languages": {"zh": 0, "en": 0},
            "avg_confidence": 0.0
        }

        if not self.cache_dir.exists():
            return stats

        confidences = []
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                classification = data["classification"]
                category = classification["category"]
                language = classification["language"]
                confidence = classification["confidence"]

                stats["total_cached"] += 1
                stats["categories"][category] = stats["categories"].get(category, 0) + 1
                stats["languages"][language] += 1
                confidences.append(confidence)

            except Exception as e:
                logging.warning(f"读取统计缓存失败: {cache_file} - {str(e)}")

        if confidences:
            stats["avg_confidence"] = sum(confidences) / len(confidences)

        return stats

def main():
    """主函数"""
    classifier = HawaiiContentClassifier()

    # 测试分类功能
    test_content = ContentItem(
        id="test_001",
        title="夏威夷州政府宣布新的旅游政策",
        content="夏威夷州政府今日宣布了新的旅游政策，旨在平衡旅游业发展与环境保护。新政策将于下月生效，包括限制某些敏感区域的游客数量，以及推广可持续旅游实践。",
        url="https://example.com/news/001",
        source="Hawaii News Now"
    )

    print("🤖 夏威夷华人平台 - 智能内容分类系统测试")
    print("=" * 50)

    result = classifier.classify_content(test_content.title, test_content.content)
    if result:
        print(f"📰 标题: {test_content.title}")
        print(f"🏷️  分类: {classifier.categories[result.category]['name']} > {classifier.categories[result.category]['subcategories'][result.subcategory]}")
        print(f"🏷️  标签: {', '.join(result.tags)}")
        print(f"📊 置信度: {result.confidence:.2f}")
        print(f"🌐 语言: {result.language}")
        print(f"💭 理由: {result.reasoning}")
    else:
        print("❌ 分类失败")

    # 显示统计信息
    stats = classifier.get_classification_stats()
    print("\n📊 分类统计信息:")
    print(f"缓存总数: {stats['total_cached']}")
    print(f"平均置信度: {stats['avg_confidence']:.2f}")
    print(f"语言分布: 中文 {stats['languages']['zh']}, 英文 {stats['languages']['en']}")

if __name__ == "__main__":
    main()
