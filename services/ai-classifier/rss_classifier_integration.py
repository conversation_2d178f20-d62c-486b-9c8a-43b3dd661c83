#!/usr/bin/env python3
"""
RSS内容分类集成脚本
将智能分类器与FreshRSS系统集成
"""

import sys
import json
import sqlite3
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

# 添加分类器模块路径
sys.path.append(str(Path(__file__).parent))
from content_classifier import HawaiiContentClassifier, ContentItem

class RSSClassifierIntegration:
    """RSS分类器集成类"""
    
    def __init__(self):
        self.classifier = HawaiiContentClassifier()
        self.base_dir = Path(__file__).parent.parent.parent
        self.freshrss_db = self.base_dir / "services" / "freshrss" / "data" / "users" / "aloha" / "db.sqlite"
        self.results_file = self.base_dir / "data" / "classification_results.json"
        
        # 确保结果目录存在
        self.results_file.parent.mkdir(parents=True, exist_ok=True)
        
        logging.info(f"FreshRSS数据库路径: {self.freshrss_db}")
        logging.info(f"分类结果文件: {self.results_file}")
    
    def get_unclassified_articles(self, limit: int = 50) -> List[ContentItem]:
        """从FreshRSS数据库获取未分类的文章"""
        articles = []
        
        if not self.freshrss_db.exists():
            logging.warning(f"FreshRSS数据库不存在: {self.freshrss_db}")
            return articles
        
        try:
            conn = sqlite3.connect(self.freshrss_db)
            cursor = conn.cursor()
            
            # 查询最新的文章
            query = """
            SELECT id, title, content, link, date, author
            FROM entry 
            ORDER BY date DESC 
            LIMIT ?
            """
            
            cursor.execute(query, (limit,))
            rows = cursor.fetchall()
            
            for row in rows:
                article_id, title, content, link, date, author = row
                
                # 清理内容
                clean_content = self.clean_html_content(content or "")
                
                article = ContentItem(
                    id=str(article_id),
                    title=title or "",
                    content=clean_content,
                    url=link or "",
                    source=author or "Unknown",
                    publish_date=date
                )
                articles.append(article)
            
            conn.close()
            logging.info(f"从FreshRSS获取了 {len(articles)} 篇文章")
            
        except Exception as e:
            logging.error(f"读取FreshRSS数据库失败: {str(e)}")
        
        return articles
    
    def clean_html_content(self, html_content: str) -> str:
        """清理HTML内容，提取纯文本"""
        import re
        
        # 移除HTML标签
        clean_text = re.sub(r'<[^>]+>', '', html_content)
        # 移除多余的空白字符
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        # 限制长度
        return clean_text[:2000]
    
    def classify_rss_articles(self, limit: int = 20) -> Dict:
        """分类RSS文章"""
        logging.info("开始RSS文章分类任务...")
        
        # 获取文章
        articles = self.get_unclassified_articles(limit)
        if not articles:
            logging.warning("没有找到可分类的文章")
            return {"success": False, "message": "没有找到文章"}
        
        # 执行分类
        classified_articles = self.classifier.classify_batch(articles)
        
        # 统计结果
        results = {
            "timestamp": datetime.now().isoformat(),
            "total_articles": len(classified_articles),
            "classified_count": 0,
            "failed_count": 0,
            "categories": {},
            "articles": []
        }
        
        for article in classified_articles:
            article_result = {
                "id": article.id,
                "title": article.title,
                "url": article.url,
                "source": article.source,
                "publish_date": article.publish_date
            }
            
            if article.classification:
                results["classified_count"] += 1
                category = article.classification.category
                results["categories"][category] = results["categories"].get(category, 0) + 1
                
                article_result["classification"] = {
                    "category": article.classification.category,
                    "subcategory": article.classification.subcategory,
                    "tags": article.classification.tags,
                    "confidence": article.classification.confidence,
                    "language": article.classification.language,
                    "reasoning": article.classification.reasoning
                }
            else:
                results["failed_count"] += 1
                article_result["classification"] = None
            
            results["articles"].append(article_result)
        
        # 保存结果
        self.save_classification_results(results)
        
        logging.info(f"分类完成: {results['classified_count']}/{results['total_articles']} 成功")
        return results
    
    def save_classification_results(self, results: Dict):
        """保存分类结果"""
        try:
            with open(self.results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logging.info(f"分类结果已保存到: {self.results_file}")
        except Exception as e:
            logging.error(f"保存分类结果失败: {str(e)}")
    
    def load_classification_results(self) -> Optional[Dict]:
        """加载分类结果"""
        if not self.results_file.exists():
            return None
        
        try:
            with open(self.results_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"加载分类结果失败: {str(e)}")
            return None
    
    def generate_classification_report(self) -> str:
        """生成分类报告"""
        results = self.load_classification_results()
        if not results:
            return "没有找到分类结果"
        
        report = f"""
🤖 RSS内容智能分类报告
{'=' * 40}

📊 总体统计:
- 处理文章总数: {results['total_articles']}
- 成功分类数量: {results['classified_count']}
- 分类失败数量: {results['failed_count']}
- 分类成功率: {results['classified_count']/results['total_articles']*100:.1f}%

📈 分类分布:
"""
        
        # 分类分布
        for category, count in results['categories'].items():
            category_name = self.classifier.categories.get(category, {}).get('name', category)
            percentage = count / results['classified_count'] * 100 if results['classified_count'] > 0 else 0
            report += f"- {category_name}: {count} 篇 ({percentage:.1f}%)\n"
        
        # 最新分类文章示例
        report += f"\n📰 最新分类文章示例:\n"
        for i, article in enumerate(results['articles'][:5]):
            if article['classification']:
                cat = article['classification']['category']
                subcat = article['classification']['subcategory']
                confidence = article['classification']['confidence']
                category_name = self.classifier.categories.get(cat, {}).get('name', cat)
                subcat_name = self.classifier.categories.get(cat, {}).get('subcategories', {}).get(subcat, subcat)
                
                report += f"{i+1}. {article['title'][:50]}...\n"
                report += f"   分类: {category_name} > {subcat_name}\n"
                report += f"   置信度: {confidence:.2f}\n"
                report += f"   标签: {', '.join(article['classification']['tags'])}\n\n"
        
        report += f"📅 报告生成时间: {results['timestamp']}\n"
        
        return report

def main():
    """主函数"""
    integration = RSSClassifierIntegration()
    
    print("🤖 RSS内容智能分类集成系统")
    print("=" * 40)
    
    # 执行分类
    results = integration.classify_rss_articles(limit=10)
    
    if results.get("success", True):
        # 生成报告
        report = integration.generate_classification_report()
        print(report)
        
        # 保存报告到文件
        report_file = integration.base_dir / "data" / "classification_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📄 详细报告已保存到: {report_file}")
    else:
        print(f"❌ 分类失败: {results.get('message', '未知错误')}")

if __name__ == "__main__":
    main()
