{"mcpServers": {"firecrawl-mcp": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-0a2c801f433d4718bcd8189f2742edf4"}}, "hawaiihub-local-tools": {"command": "python3", "args": ["/Users/<USER>/Documents/华人平台/hawaiihub.net/services/mcp-config/hawaiihub_mcp_server.py"], "env": {"HAWAIIHUB_ROOT": "/Users/<USER>/Documents/华人平台/hawaiihub.net", "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMDJjMjM0MC00MTRjLTQyNDUtOTQwYS1lMDE2Mjk4ZDg2ZTgiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUzNzMzNzI4fQ.Z8Vi1OBeFkFdWOw_kaE7xiEcdhEHeLQIuphM6Y9TyUg", "FIRECRAWL_API_KEY": "fc-0a2c801f433d4718bcd8189f2742edf4"}}}, "globalShortcut": "Cmd+Shift+.", "allowedHosts": ["localhost", "127.0.0.1", "************", "************", "hawaiihub.net"]}