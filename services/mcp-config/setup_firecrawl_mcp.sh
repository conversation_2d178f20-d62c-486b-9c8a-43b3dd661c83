#!/bin/bash

# Firecrawl MCP自动配置脚本
# HawaiiHub.net AI助手集成

echo "🔥 开始配置Firecrawl MCP集成..."

# API Key
FIRECRAWL_API_KEY="fc-0a2c801f433d4718bcd8189f2742edf4"

# Claude Desktop配置路径
CLAUDE_CONFIG_DIR="$HOME/Library/Application Support/Claude"
CLAUDE_CONFIG_FILE="$CLAUDE_CONFIG_DIR/claude_desktop_config.json"

echo "📁 创建Claude配置目录..."
mkdir -p "$CLAUDE_CONFIG_DIR"

echo "⚙️ 生成Claude Desktop配置..."
cat > "$CLAUDE_CONFIG_FILE" << EOF
{
  "mcpServers": {
    "firecrawl-mcp": {
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "$FIRECRAWL_API_KEY"
      }
    },
    "hawaiihub-tools": {
      "command": "python3",
      "args": ["$(pwd)/hawaiihub_mcp_tools.py"],
      "env": {
        "HAWAIIHUB_ROOT": "$(pwd)",
        "N8N_API_URL": "http://************:5678/api/v1",
        "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMDJjMjM0MC00MTRjLTQyNDUtOTQwYS1lMDE2Mjk4ZDg2ZTgiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUzNzMzNzI4fQ.Z8Vi1OBeFkFdWOw_kaE7xiEcdhEHeLQIuphM6Y9TyUg"
      }
    }
  },
  "globalShortcut": "Cmd+Shift+.",
  "allowedHosts": [
    "localhost",
    "127.0.0.1",
    "************",
    "************",
    "api.firecrawl.dev",
    "firecrawl.dev",
    "hawaiihub.net"
  ]
}
EOF

echo "✅ Claude Desktop配置已创建: $CLAUDE_CONFIG_FILE"

echo "🧪 测试Firecrawl MCP服务器..."
echo "正在启动MCP服务器进行测试..."

# 测试MCP服务器
timeout 10s env FIRECRAWL_API_KEY="$FIRECRAWL_API_KEY" npx -y firecrawl-mcp &
MCP_PID=$!

sleep 5

if kill -0 $MCP_PID 2>/dev/null; then
    echo "✅ Firecrawl MCP服务器启动成功"
    kill $MCP_PID 2>/dev/null
else
    echo "❌ Firecrawl MCP服务器启动失败"
fi

echo "📋 创建使用指南..."
cat > "firecrawl_mcp_usage_guide.md" << 'EOF'
# Firecrawl MCP使用指南

## 🚀 快速开始

### 1. 重启Claude Desktop
配置文件已更新，请重启Claude Desktop应用。

### 2. 验证MCP功能
在Claude Desktop中输入以下测试命令：

```
请使用Firecrawl爬取 https://example.com 的内容
```

### 3. 夏威夷新闻测试
```
请爬取Hawaii News Now的首页内容并分析主要新闻话题
```

## 🎯 实用命令示例

### 内容分析
```
请爬取以下夏威夷新闻网站并对比今日头条：
- https://www.hawaiinewsnow.com
- https://www.staradvertiser.com
```

### 网站质量评估
```
请爬取这个网站并评估其作为内容源的价值：
[网站URL]
```

### 竞品分析
```
请爬取我们竞争对手的网站，分析他们的内容策略和更新频率
```

## 📊 成本监控

每次爬取消耗1个credit，当前配额：
- Hobby计划: 3000 credits/月
- 日均可用: ~100 credits/天

## 🔧 故障排除

如果MCP不工作：
1. 检查Claude Desktop是否重启
2. 验证配置文件格式正确
3. 确认API Key有效
4. 检查网络连接

## 📞 支持

如有问题，请检查：
- Claude Desktop版本是否最新
- MCP服务器是否正常运行
- API配额是否充足
EOF

echo "📚 使用指南已创建: firecrawl_mcp_usage_guide.md"

echo ""
echo "🎉 Firecrawl MCP配置完成！"
echo ""
echo "📋 下一步操作："
echo "1. 重启Claude Desktop应用"
echo "2. 在Claude中测试: '请使用Firecrawl爬取 https://example.com'"
echo "3. 查看使用指南: cat firecrawl_mcp_usage_guide.md"
echo ""
echo "🔑 API Key: $FIRECRAWL_API_KEY"
echo "📁 配置文件: $CLAUDE_CONFIG_FILE"
echo "📊 月度配额: 3000 credits (Hobby计划)"
echo ""
echo "✨ 现在你可以在Claude Desktop中直接使用Firecrawl进行网页爬取和分析了！"
