# Firecrawl MCP完整配置指南
## HawaiiHub.net AI助手集成

### 🚀 官方MCP命令确认

你提供的官方命令：
```bash
env FIRECRAWL_API_KEY=fc-0a2c801f433d4718bcd8189f2742edf4 npx -y firecrawl-mcp
```

这是Firecrawl官方提供的MCP服务器，可以让AI助手直接调用Firecrawl API进行网页爬取。

### 📁 Claude Desktop配置

#### 1. 配置文件位置
```bash
# macOS
~/Library/Application Support/Claude/claude_desktop_config.json

# Windows  
%APPDATA%\Claude\claude_desktop_config.json

# Linux
~/.config/Claude/claude_desktop_config.json
```

#### 2. 完整配置内容
```json
{
  "mcpServers": {
    "firecrawl-mcp": {
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "fc-0a2c801f433d4718bcd8189f2742edf4"
      }
    }
  },
  "globalShortcut": "Cmd+Shift+.",
  "allowedHosts": [
    "localhost",
    "127.0.0.1",
    "api.firecrawl.dev",
    "firecrawl.dev"
  ]
}
```

### 🎯 MCP功能测试

#### 基础爬取测试
在Claude Desktop中可以直接使用：

```
用户: "请使用Firecrawl爬取 https://example.com 的内容"
Claude: [调用firecrawl-mcp] → 返回网页内容
```

#### 夏威夷新闻源测试
```
用户: "请爬取Hawaii News Now首页的最新新闻"
Claude: [调用firecrawl-mcp爬取hawaiinewsnow.com] → 分析新闻内容
```

#### 批量内容分析
```
用户: "请同时爬取以下夏威夷新闻网站并对比内容：
- https://www.hawaiinewsnow.com
- https://www.staradvertiser.com
- https://www.hawaiitribune-herald.com"

Claude: [批量调用firecrawl-mcp] → 内容对比分析
```

### 🔧 高级配置选项

#### 1. 自定义爬取参数
MCP服务器支持Firecrawl的所有参数：

```json
{
  "url": "https://example.com",
  "formats": ["markdown", "json"],
  "onlyMainContent": true,
  "includeTags": ["title", "meta", "article"],
  "excludeTags": ["nav", "footer", "ads"],
  "waitFor": 3000,
  "screenshot": false
}
```

#### 2. 批量爬取配置
```json
{
  "urls": [
    "https://www.hawaiinewsnow.com",
    "https://www.staradvertiser.com"
  ],
  "formats": ["markdown"],
  "onlyMainContent": true,
  "maxDepth": 2,
  "limit": 10
}
```

### 📊 MCP vs API集成对比

| 特性 | MCP集成 | n8n API集成 |
|------|---------|-------------|
| **使用方式** | AI对话中调用 | 自动化工作流 |
| **触发方式** | 手动请求 | 定时/事件触发 |
| **数据处理** | AI实时分析 | 预设处理逻辑 |
| **灵活性** | 极高 | 中等 |
| **自动化程度** | 低 | 高 |
| **适用场景** | 探索分析 | 生产运营 |

### 🎯 HawaiiHub实际应用场景

#### 1. 内容质量评估
```
场景: 评估新的内容源质量
用法: "请爬取这个夏威夷本地网站，分析其内容质量和更新频率"
价值: 快速评估是否值得加入采集列表
```

#### 2. 竞品分析
```
场景: 分析竞争对手网站
用法: "请爬取竞争对手的网站，分析他们的内容策略"
价值: 了解市场动态，优化自身策略
```

#### 3. 紧急内容获取
```
场景: 突发新闻快速获取
用法: "夏威夷刚发生地震，请爬取各大新闻网站的相关报道"
价值: 快速汇总重要信息
```

#### 4. 内容验证
```
场景: 验证自动采集的内容
用法: "请爬取这个页面，检查我们的自动采集是否遗漏了什么"
价值: 质量控制和系统优化
```

### 🔄 与现有系统集成

#### 1. 开发阶段集成
```
开发流程:
1. 使用MCP快速测试新网站
2. 分析爬取结果和数据结构
3. 基于分析结果配置n8n工作流
4. 部署到生产环境
```

#### 2. 运营阶段集成
```
日常运营:
- 自动化: n8n + Firecrawl API (主要)
- 人工干预: Claude + Firecrawl MCP (辅助)
- 问题诊断: MCP快速测试和分析
```

### 💡 最佳实践建议

#### 1. 成本控制
```bash
# 设置环境变量监控
export FIRECRAWL_MONTHLY_LIMIT=3000
export FIRECRAWL_DAILY_LIMIT=100

# 在MCP使用前检查用量
echo "今日已使用: $FIRECRAWL_DAILY_USAGE credits"
```

#### 2. 使用策略
```
高价值场景: 使用MCP进行深度分析
日常采集: 使用API自动化处理
紧急情况: MCP快速响应
质量检查: MCP验证自动化结果
```

#### 3. 数据管理
```
MCP获取的数据 → 手动分析 → 优化自动化策略
API获取的数据 → 自动处理 → 存储到系统
```

### 🚀 立即开始使用

#### 1. 配置Claude Desktop
```bash
# 1. 创建配置文件
mkdir -p ~/Library/Application\ Support/Claude/
cat > ~/Library/Application\ Support/Claude/claude_desktop_config.json << 'EOF'
{
  "mcpServers": {
    "firecrawl-mcp": {
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "fc-0a2c801f433d4718bcd8189f2742edf4"
      }
    }
  }
}
EOF

# 2. 重启Claude Desktop
# 3. 测试MCP功能
```

#### 2. 第一次测试
在Claude Desktop中输入：
```
"请使用Firecrawl爬取 https://www.hawaiinewsnow.com 的首页内容，并分析其主要新闻话题"
```

#### 3. 验证功能
检查是否能看到：
- ✅ 网页内容成功爬取
- ✅ 内容格式清晰（Markdown格式）
- ✅ AI能够分析和总结内容
- ✅ 没有出现API错误

### 📈 预期效果

配置完成后，你将获得：
- **即时网页分析能力**: 任何网站都可以快速爬取分析
- **内容质量评估**: AI辅助评估网站价值
- **竞品监控**: 快速了解竞争对手动态  
- **系统优化**: 基于实际数据优化自动化策略

这个MCP集成将成为HawaiiHub.net内容运营的强大辅助工具！
