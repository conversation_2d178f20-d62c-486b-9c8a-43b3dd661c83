:root {
	--background-color-hover: #2f1d22;
}

.btn:hover {
	background: unset;
	border-color: #ff449a;
	color: unset;
}

.btn-important {
	background: #ffb6c1;
	color: #000;
	border-color: #ffa1af;
}

.btn-important:hover {
	background: #ffa1af;
	color: #000;
}

.switch.active {
	background-color: #ffb6c1;
}

.nav-list .item.active a,
.nav-list .item.active .as-link {
	color: #ffb6c1;
}

.tree-folder.active .tree-folder-title .title {
	color: #ffb6c1;
}

.tree-folder-title:hover button.dropdown-toggle .icon {
	filter: sepia(21%) brightness(44%) hue-rotate(112deg) saturate(1000%) contrast(132%) invert(100%);
}

.tree-folder-title button.dropdown-toggle:hover .icon {
	filter: sepia(21%) brightness(94%) hue-rotate(112deg) saturate(1000%) contrast(132%) invert(100%)
}

.aside_feed .tree-folder-items .item .dropdown-target:target ~ .dropdown-toggle > .icon,
.aside_feed .tree-folder-items .item.active .dropdown-toggle > .icon {
	background-color: #000;
}

#new-article {
	background: #ffa1af;
}

#new-article > a {
	color: #000;
}

#new-article > a:hover {
	background: #ff92a2;
}

.day {
	color: #ffb6c1;
}

.day .name {
	color: #ffb6c1;
}

.flux.current {
	border-color: #787394;
}

.content hr {
	box-shadow: 0 2px 5px #ffb6c1;
}

#bigMarkAsRead.big {
	color: #787394;
}

.nav label {
	color: #4f5e78;
}

input:focus {
	outline: none;
	border: 1px solid #ff449a;
}

.notification.good {
	border-color: #ffb6c1;
}

.notification.bad {
	border-color: #ff449a;
}

.notification.good .close:hover {
	background: #ffb6c1;
}

.notification.bad .close:hover {
	background: #ff449a;
}

.notification.good .close:hover .icon {
	filter: brightness(0.3);
}

.notification.bad .close .icon {
	filter: brightness(0.3);
}

.notification.bad .close:hover .icon {
	filter: brightness(3);
}

.stick .btn-important:first-child {
	border-left-color: #fd6aae;
}

/*=== Change icon colors */
.header > .item.title .logo {
	/* Color FreshRSS logo */
	filter: sepia(62%) brightness(107%) hue-rotate(315deg) saturate(248%) contrast(104%) invert(100%);
}

.header > .item.title a:hover .logo {
	filter: sepia(62%) brightness(70%) hue-rotate(315deg) saturate(248%) contrast(104%) invert(100%);
}

.icon[src*="/all"],
.icon[src*="/down"],
.icon[src*="/help"],
.icon[src*="/label"],
.icon[src*="/link"],
.icon[src*="/login"],
.icon[src*="/logout"],
.icon[src*="/non-starred"],
.icon[src*="/read"],
.icon[src*="/share"],
.icon[src*="/tag"],
.icon[src*="/up"] {
	/* Color dark grey icons */
	filter: sepia(21%) brightness(44%) hue-rotate(112deg) saturate(1000%) contrast(132%) invert(100%);
}

.icon[src*="/view-normal"],
.icon[src*="/view-global"],
.icon[src*="/view-reader"],
.icon[src*="/bookmark-tag"],
.icon[src*="/view-rss"],
.icon[src*="/refresh"],
.icon[src*="/search"],
.icon[src*="/rss"],
.icon[src*="/sort-up"],
.icon[src*="/sort-down"],
.icon[src*="/key"],
.icon[src*="/opml-dyn"],
.icon[src*="/configure"],
.icon[src*="/category"] {
	/* Color light grey icons */
	filter: sepia(62%) brightness(47%) hue-rotate(116deg) saturate(1000%) contrast(119%) invert(100%);
}

#btn-add > img {
	filter: brightness(0.75);
}
