@charset "UTF-8";

/*=== GENERAL */
/*============*/
:root {
	--background-color-dark: #171717;
	--background-color-dark-transparent: #171717aa;
	--background-color-middle: #1f1f1f;
	--background-color-light: #262626;
	--background-color-hover: #1d242f;
	--background-color-active: #333;
	--background-color-slider-shadow: rgba(0, 0, 0, 0.5);

	--contrast-background-color: #0062b7;
	--contrast-background-color-hover: #0078e0;
	--contrast-background-color-active: #003461;
	--contrast-background-font-color: #eee;
	--contrast-border-color-hover: #0078e0;

	--contrast-attention-background-color: #912621;
	--contrast-attention-background-color-hover: #be342c;
	--contrast-attention-font-color: #eee;

	--font-color-dark: #666;
	--font-color-middle: #999;
	--font-color-light: #ccc;
	--font-color-contrast: #0062b7;
	--font-color-error: #912621;

	--border-color-dark: #333;
	--border-color-middle: #666;
	--frss-border-color: var(--border-color-dark);
	--border-color-favorite: #ffc300;
	--border-color-unread: #e74c3c;

	--empty-feed-color: #e67e22;

	--alert-warn-border-color: #615f20;
	--alert-success-border-color: #18521d;
	--alert-error-border-color: #912621;

	--notification-border-color: #eeb;
	--notification-good-border-color: #0062b7;
	--notification-good-background-color: #0062b7;
	--notification-bad-background-color: #fdd;
	--notification-bad-font-color: #912621;
	--notification-bad-border-color: #ecc;

	--frss-box-shadow-color-transparent: #bbb3;
}

html, body {
	background-color: var(--background-color-dark);
	color: var(--font-color-middle);
	font-family: "OpenSans", "Cantarell", "Helvetica", "Arial", sans-serif;
}

/*=== Links */
a, button.as-link {
	color: var(--font-color-light);
	outline: none;
}

a:hover .icon {
	filter: brightness(2);
}

/*=== Forms */
legend {
	margin: 20px 0 5px;
	padding: 5px 0;
	font-size: 1.4em;
	border-bottom: 1px solid var(--border-color-dark);
}

label {
	min-height: 25px;
	padding: 5px 0;
	cursor: pointer;
}

textarea {
	width: 360px;
	height: 100px;
}

input, select, textarea {
	padding: 7px;
	background: var(--background-color-light);
	color: var(--font-color-middle);
	border: 1px solid var(--border-color-middle);
	border-radius: 3px;
	vertical-align: middle;
}

input:hover,
select:hover,
textarea:hover,
label:hover {
	color: var(--font-color-light);
}

option {
	padding: 0 .5em;
}

input[type="checkbox"],
input[type="radio"] {
	accent-color: var(--contrast-background-color);
}

input[type="checkbox"]:hover,
input[type="radio"]:hover {
	accent-color: var(--contrast-background-color-hover);
}

input:disabled, select:disabled {
	color: var(--font-color-dark);
	border-color: var(--border-color-dark);
}

/*=== Tables */
table {
	border-collapse: collapse;
}

th, td {
	border: 1px solid var(--border-color-dark);
}

th {
	background: var(--background-color-light);
}

.config-articleicons td,
.config-articleicons th {
	font-weight: normal;
	text-align: center;
}

/*=== COMPONENTS */
/*===============*/
/*=== Forms */
.form-group.form-actions {
	padding: 5px 0;
	background-color: var(--background-color-dark-transparent);
	border-top: 1px solid var(--border-color-dark);
}

.form-group.form-actions .btn {
	margin: 0 10px;
}

.form-group .group-name {
	padding: 10px 0;
}

.form-group .group-controls {
	min-height: 25px;
	padding: 5px 0;
}

/*=== Buttons */
.stick input,
.group .btn,
.stick .btn {
	border-radius: 0;
}

.stick .btn:first-child,
.group .btn:first-child,
.stick input:first-child,
.group input:first-child {
	border-radius: 3px 0 0 3px;
}

.stick .btn-important:first-child {
	border-right: 1px solid var(--contrast-background-color-hover);
}

.stick .btn:last-child,
.group .btn:last-child,
.stick input:last-child,
.group input:last-child {
	border-radius: 0 3px 3px 0;
}

.stick .btn + .btn,
.group .btn + .btn,
.stick .btn + input,
.stick .btn + .dropdown > .btn,
.group .btn + .dropdown > .btn,
.stick input + .btn,
.stick input + input,
.stick input + .dropdown > .btn,
.stick .dropdown + .btn,
.group .dropdown + .btn,
.stick .dropdown + input,
.stick .dropdown + .dropdown > .btn,
.group .dropdown + .dropdown > .btn {
	border-left: none;
}

.stick .btn + .dropdown > .btn,
.group .dropdown + .dropdown > .btn {
	border-left: none;
	border-radius: 0 3px 3px 0;
}

.btn {
	margin: 0;
	padding: 0.25rem 0.5rem;
	background: var(--background-color-dark);
	color: var(--font-color-middle);
	font-size: 0.9rem;
	border: 1px solid var(--border-color-middle);
	border-radius: 3px;
	min-height: 25px;
	min-width: 15px;
	line-height: 1.7;
	vertical-align: middle;
}

.btn:hover {
	background: var(--background-color-hover);
	text-decoration: none;
	color: var(--font-color-light);
	border: 1px solid var(--contrast-border-color-hover);
}

.btn:hover .icon {
	filter: brightness(2);
}

.btn.active,
.btn:active,
.dropdown-target:target ~ .btn.dropdown-toggle {
	background: var(--background-color-light);
}

.btn-important {
	background: var(--contrast-background-color);
	color: var(--contrast-background-font-color);
	border: 1px solid var(--contrast-border-color-hover);
	font-weight: normal;
}

.btn-important:hover {
	background: var(--contrast-background-color-hover);
	color: var(--contrast-background-font-color);
}

.btn-important .icon {
	filter: brightness(3);
}

.btn-important:active {
	background: var(--contrast-background-color-active);
	box-shadow: none;
}

.btn-attention {
	background-color: var(--contrast-attention-background-color);
	color: var(--contrast-attention-font-color);
}

.btn-attention:hover {
	background-color: var(--contrast-attention-background-color-hover);
	color: var(--contrast-attention-font-color);
}

.btn-attention:active {
	background: var(--contrast-attention-background-color);
	box-shadow: none;
}

.spinner {
	filter: invert(1);
}

a:hover > .spinner {
	filter: invert(1) brightness(2);
}

.flux .item.manage .read:hover .icon.spinner {
	filter: invert(1) grayscale(0.8) brightness(1.7);
}

/*=== switches */
.switch.active {
	background-color: var(--contrast-background-color);
}

.switch.active:hover {
	background-image: url('./icons/disabled-light.svg');
}

/*=== Navigation */
.nav-list {
	color: var(--font-color-light);
	font-size: 0.9rem;
}

.nav-list .item,
.nav-list .item.nav-header {
	min-height: 2.5em;
	line-height: 2.5;
}

.nav-list .item a,
.nav-list .item .as-link {
	color: var(--font-color-middle);
}

.nav-list .item a:hover,
.nav-list .item .as-link:hover {
	background-color: var(--background-color-hover);
	color: var(--font-color-light);
}

.nav-list .item.active a,
.nav-list .item.active .as-link {
	background-color: var(--background-color-dark);
	color: var(--font-color-light);
}

.nav-list .item > a,
.nav-list .item > .as-link {
	padding: 0 1rem;
}

.nav-list a:hover,
.nav-list .as-link:hover {
	text-decoration: none;
}

.nav-list .nav-header {
	padding: 0 1rem;
	color: var(--font-color-middle);
	font-weight: bold;
}

.nav-list .nav-form {
	padding: 3px;
	text-align: center;
}

/*=== Dropdown */
.dropdown-menu {
	margin: 5px 0 0;
	padding: 0.5rem 0 0.25rem 0;
	background: var(--background-color-active);
	font-size: 0.8rem;
	border: 2px solid var(--background-color-light);
	border-radius: 5px;
	text-align: left;
}

.dropdown-menu::after {
	border-color: var(--border-color-dark);
}

.dropdown-header,
.dropdown-section .dropdown-section-title {
	padding: 0.25rem 0.5rem 0.25rem 1rem;
	color: var(--font-color-middle);
	font-weight: bold;
	text-align: left;
}

.dropdown-menu .item > a,
.dropdown-menu .item > span,
.dropdown-menu .item > .as-link {
	padding: 0 22px;
	line-height: 2.5;
	font-size: inherit;
}

.dropdown-menu .dropdown-section .item > a,
.dropdown-menu .dropdown-section .item > span,
.dropdown-menu .dropdown-section .item > .as-link {
	padding-left: 2rem;
}

.dropdown-menu .dropdown-section .item:last-child {
	margin-bottom: 0.5rem;
}

.dropdown-menu .item > a:focus,
.dropdown-menu .item > a:hover,
.dropdown-menu .item > button:focus:not([disabled]),
.dropdown-menu .item > button:hover:not([disabled]),
.dropdown-menu .item > label:focus:not(.noHover),
.dropdown-menu .item > label:hover:not(.noHover) {
	background: var(--background-color-hover);
	color: var(--font-color-light);
}

.dropdown-menu > .item[aria-checked="true"] > a::before {
	font-weight: bold;
	margin: 0 0 0 -14px;
}

.dropdown-menu .input select,
.dropdown-menu .input input {
	margin: 0 auto 5px;
	padding: 2px 5px;
	border-radius: 3px;
}

.dropdown-menu input[type="checkbox"] {
	margin-left: 1rem;
}

.dropdown-menu .item .emptyLabels {
	padding-left: 1rem;
	padding-right: 1rem;
}

.item ~ .dropdown-header,
.dropdown-section ~ .dropdown-section,
.item.separator {
	border-top-color: var(--border-color-dark);
}

/*=== Alerts */
.alert {
	background-color: var(--background-color-dark);
	color: var(--font-color-middle);
	font-size: 0.9em;
	border-width: 2px;
	border-style: solid;
	border-radius: 5px;
}

.alert-head {
	font-size: 1.15em;
}

.alert a {
	color: var(--font-color-middle);
	text-decoration: underline;
}

.alert a:hover {
	color: var(--font-color-light);
}

.alert-warn {
	border-color: var(--alert-warn-border-color);
}

.alert-success {
	border-color: var(--alert-success-border-color);
}

.alert-error {
	border-color: var(--alert-error-border-color);
}

kbd {
	background-color: var(--background-color-middle);
	color: var(--font-color-middle);
	border-color: var(--border-color-middle);
}

/*=== Pagination */
.pagination {
	color: var(--font-color-middle);
	border-top: 1px solid var(--border-color-dark);
	border-bottom: 1px solid var(--border-color-dark);
}

/*=== Boxes */
.box {
	background: var(--background-color-middle);
	border-width: 0;
	border-radius: 3px;
}

.box .box-title {
	background: var(--background-color-light);
	border-radius: 5px 5px 0 0;
}

.box .box-title .configure {
	margin-right: 4px;
}

.box .box-content {
	padding-left: 30px;
	min-height: 2.5em;
	max-height: 260px;
}

.box .box-content .item {
	font-size: 0.9rem;
}

/*=== Tree */
.tree {
	margin: 10px 0;
}

.tree-folder-title {
	position: relative;
	padding: 0 10px;
	background: var(--background-color-light);
	line-height: 2.5;
	font-size: 1rem;
}

.tree-folder-title:hover {
	background: var(--background-color-hover);
}

.tree-folder-title:hover .title {
	color: var(--font-color-light);
}

.tree-folder-title:hover .dropdown-toggle .icon {
	filter: brightness(2);
}

.tree-folder-title:hover button.dropdown-toggle .icon {
	filter: inherit;
}

.tree-folder-title button.dropdown-toggle:hover .icon {
	filter: brightness(2);
}

.tree-folder-title .title {
	background: inherit;
	color: var(--font-color-middle);
}

.tree-folder-title .title:hover {
	text-decoration: none;
}

.tree-folder.active .tree-folder-title {
	background: var(--background-color-dark);
	font-weight: bold;
}

.tree-folder.active .tree-folder-title .title {
	color: var(--font-color-light);
}

.tree-folder-items {
	background: var(--background-color-light);
}

.tree-folder-items:hover {
	background: var(--background-color-middle);
}

.tree-folder-items > .item {
	font-size: 0.8rem;
}

.tree-folder-items > .item.active {
	background: var(--background-color-dark);
}

.tree-folder-items > .item > a {
	text-decoration: none;
}

.tree-folder-items > .item.active > a {
	color: var(--font-color-middle);
}

/*=== STRUCTURE */
/*===============*/
/*=== Header */
.header {
	background: var(--background-color-dark);
}

.header > .item {
	border-bottom: 1px solid var(--border-color-dark);
	vertical-align: middle;
	text-align: center;
}

.header > .item.title .logo {
	filter: grayscale(100%) brightness(2.5);
}

.header > .item.title a:hover .logo {
	filter: grayscale(100%) brightness(2.8);
}

.header > .item.search input {
	width: 350px;
}

/*=== Body */
.aside {
	background: var(--background-color-light);
}

.aside.aside_feed {
	padding: 10px 0;
	text-align: center;
	background: var(--background-color-light);
}

.aside.aside_feed .tree {
	margin: 10px 0 50px;
}

/*=== Aside main page (categories) */
.aside_feed .category .title:not([data-unread="0"])::after {
	padding: 0 10px;
	line-height: 1.5;
}

.aside.aside_feed .category .title:not([data-unread="0"])::after,
.aside.aside_feed .feed .item-title:not([data-unread="0"])::after,
.global .box.category .title:not([data-unread="0"])::after,
.global .feed .item-title:not([data-unread="0"])::after {
	background-color: var(--background-color-dark);
	color: var(--font-color-middle);
}

.aside.aside_feed .category:hover .title:not([data-unread="0"])::after,
.aside.aside_feed .feed:hover .item-title:not([data-unread="0"])::after {
	color: var(--font-color-light);
}

.aside_feed .tree-folder-title button.dropdown-toggle {
	margin: -0.75rem 0 -0.75rem -0.75rem;
	padding: 0.75rem 0 0.75rem 0.75rem;
}

/*=== Aside main page (feeds) */
.feed.item.empty.active,
.feed.item.empty.active > a {
	background: var(--empty-feed-color);
	color: var(--font-color-dark);
}

.feed.item.error.active {
	background: var(--contrast-attention-background-color);
}

.feed.item.empty,
.feed.item.empty > a {
	color: var(--empty-feed-color);
}

.feed.item.error,
.feed.item.error > a {
	color: var(--font-color-error);
}

.feed.item.error.active,
.feed.item.error.active > a {
	color: var(--font-color-light);
}

.aside_feed .tree-folder-items .dropdown-menu::after {
	left: 2px;
}

.theme-preview-list {
	border-color: var(--border-color-middle);
}

.theme-preview-list .properties {
	background-color: var(--background-color-light);
	color: var(--font-color-middle);
	border-color: var(--border-color-middle);
}

/*=== Prompt (centered) */
.prompt .form-group {
	margin-bottom: 1rem;
}

.prompt .form-group::after {
	display: none;
}

.prompt .form-group.form-group-actions {
	display: flex;
	margin-top: 2rem;
	align-items: center;
	justify-content: space-between;
}

.prompt .btn.btn-important {
	padding-left: 1.5rem;
	padding-right: 1.5rem;
	font-size: 1.1rem;
}

/*=== New article notification */
#new-article {
	background: var(--contrast-background-color);
	text-align: center;
	font-size: 0.9em;
}

#new-article > a {
	padding: 0.75rem;
	color: var(--font-color-light);
	font-weight: bold;
}

#new-article > a:hover {
	text-decoration: none;
	background: var(--contrast-background-color-hover);
}

/*=== Day indication */
.day {
	padding: 0 10px;
	font-weight: bold;
	line-height: 3;
	background: var(--background-color-dark);
	color: var(--font-color-light);
	border-top: 1px solid var(--border-color-dark);
}

.day span {
	line-height: 1.5;
}

#new-article + .day {
	border-top: none;
}

.day .name {
	padding: 0 10px 0 0;
	color: var(--font-color-light);
	font-size: 1.8em;
	opacity: 0.3;
	text-shadow: 0px -1px 0px var(--font-color-dark);
	font-style: italic;
	text-align: right;
}

/*=== Index menu */
.nav_menu {
	padding: 5px 0;
	background: var(--background-color-dark);
	text-align: center;
}

/*=== Feed articles */
.flux {
	background: var(--background-color-dark);
	border-left-color: var(--border-color-dark);
	border-left-width: 2px;
	border-left-style: solid;
}

.flux .flux_header:hover {
	background: var(--background-color-hover) !important;
}

.flux.current {
	background: var(--background-color-middle);
	border-left: 2px solid var(--contrast-border-color-hover);
}

.flux.current.active {
	background: var(--background-color-middle);
}

.flux.not_read {
	border-left: 2px solid var(--border-color-unread);
}

.flux.not_read:not(.current) {
	background: var(--background-color-dark);
}

.flux.not_read:not(.current):hover .item .title {
	background: var(--background-color-hover);
}

.flux:not(.current):hover .item .title {
	background: var(--background-color-hover);
}

.flux.current:not(.active) {
	background: var(--background-color-hover);
}

.flux .flux_header .item .title {
	color: var(--font-color-light);
}

.flux .flux_header .item .title .author {
	color: var(--font-color-dark);
}

.flux.favorite {
	border-left: 2px solid var(--border-color-favorite);
}

.flux.favorite:not(.current) {
	background: var(--background-color-dark);
}

.flux_header {
	font-size: 0.8rem;
	border-top: 1px solid var(--border-color-dark);
	cursor: pointer;
}

.flux_header .title {
	font-size: 0.9rem;
}

.flux .website a:hover .favicon,
.flux a.website:hover .favicon {
	filter: grayscale(50%);
}

.flux .item.manage .read:hover .icon {
	filter: grayscale(0.8) brightness(1.7);
}

.flux .flux_header .date,
.flux .flux_content .bottom .date {
	color: var(--font-color-light);
}

.flux .bottom {
	font-size: 0.8rem;
	text-align: center;
}

/*=== Content of feed articles */
.content {
	padding: 20px 10px;
	color: var(--font-color-middle);
}

.content h1,
.content h2,
.content h3 {
	color: var(--font-color-light);
}

.content hr {
	margin: 30px 10px;
	background: var(--font-color-middle);
	height: 1px;
	border: 0;
}

.content pre {
	background: var(--background-color-dark);
	color: var(--font-color-light);
	border-radius: 3px;
}

.content code {
	background: var(--background-color-light);
	border-color: var(--background-color-light);
	border-radius: 3px;
}

.content pre code {
	background: transparent;
	color: var(--font-color-light);
	border: none;
}

.content blockquote {
	margin: 0;
	padding: 25px;
	background: var(--background-color-light);
	display: block;
	color: var(--font-color-middle);
}

.content blockquote p {
	margin: 0;
}

.content > footer {
	border-color: var(--border-color-middle);
}

.content > header a,
.content > header .subtitle,
.content > footer a,
.content > footer .subtitle {
	color: var(--font-color-middle);
}

.content > header h1 a {
	color: var(--font-color-light);
}

/*=== Notification and actualize notification */
.notification {
	font-size: 0.9em;
	border: 2px solid var(--notification-border-color);
	border-radius: 5px;
	text-align: center;
	font-weight: bold;
	vertical-align: middle;
}

.notification.good {
	background-color: var(--background-color-light);
	color: var(--font-color-middle);
	border-color: var(--notification-good-border-color);
}

.notification.good .close:hover {
	background: var(--notification-good-background-color);
}

.notification.bad {
	background-color: var(--notification-bad-background-color);
	color: var(--notification-bad-font-color);
	border-color: var(--notification-bad-border-color);
}

.notification.bad .close:hover {
	background: var(--notification-bad-background-color);
}

.notification .close .icon {
	filter: brightness(1);
}

.notification .close:hover .icon {
	filter: brightness(2);
}

.notification a {
	color: var(--font-color-contrast);
}

/*=== "Load more" part */
#bigMarkAsRead.big {
	background: var(--background-color-dark);
	color: var(--font-color-contrast);
	text-align: center;
	text-decoration: none;
}

#bigMarkAsRead:hover {
	background-color: var(--background-color-hover);
	color: var(--font-color-light);
}

.bigTick {
	display: none!important;
}

/*=== Navigation menu (for articles) */
#nav_entries {
	background: var(--background-color-light);
}

/*=== READER VIEW */
/*================*/
#stream.reader .flux {
	color: var(--font-color-dark);
	background-color: var(--background-color-dark);
	border-bottom: 10px solid var(--background-color-dark);
}

#stream.reader .flux:hover {
	background: inherit !important;
}

#stream.reader .flux .flux_content {
	background-color: var(--background-color-middle);
	border-color: var(--border-color-dark);
}

#stream.reader .flux.current.active {
	background: var(--background-color-middle);
}

#stream.reader .flux .author {
	color: var(--font-color-dark);
}

#stream-footer {
	border-top-color: var(--border-color-dark);
}

/*=== GLOBAL VIEW */
/*================*/
.box.category .box-title .title {
	font-weight: normal;
	text-decoration: none;
	text-align: left;
}

.box.category:not([data-unread="0"]) .box-title {
	background: var(--background-color-light);
}

.box.category:not([data-unread="0"]) .box-title:active {
	background: var(--background-color-dark);
}

.box.category:not([data-unread="0"]) .box-title .title {
	color: var(--font-color-middle);
	font-weight: bold;
}

.box.category .title:not([data-unread="0"])::after {
	background: none;
	color: var(--font-color-light);
	border: 0;
	box-shadow: none;
	position: absolute;
	top: 5px; right: 10px;
	font-weight: bold;
	text-shadow: none;
}

#stream.global .feed .item-title:not([data-unread="0"])::after {
	margin: 1rem 0px 0px;
}

/*=== Slider */
#slider {
	background: var(--background-color-dark);
	border-left: 1px solid var(--border-color-dark);
}

#slider.active:target + #close-slider {
	background-color: var(--background-color-slider-shadow);
}

/*=== SLIDESHOW */
/*==============*/
.nav label {
	color: var(--font-color-light);
}

.nav label:hover {
	color: var(--font-color-contrast);
}

/*=== DIVERS */
/*===========*/
.aside.aside_feed .nav-form input,
.aside.aside_feed .nav-form select {
	width: 140px;
}

.aside.aside_feed .nav-form .dropdown .dropdown-menu {
	right: -20px;
}

.aside.aside_feed .nav-form .dropdown .dropdown-menu::after {
	right: 33px;
}

/*=== STATISTICS */
/*===============*/
.stat {
	margin: 10px 0 20px;
}

.stat th,
.stat td,
.stat tr {
	border: none;
}

.stat > table td,
.stat > table th {
	border-bottom: 1px solid var(--border-color-dark);
}

/*=== MOBILE */
/*===========*/

@media (max-width: 840px) {
	.form-group .group-name {
		padding-bottom: 0;
	}

	.aside {
		box-shadow: 3px 0 3px var(--background-color-active);
		transition: width 200ms linear;
	}

	.aside .toggle_aside,
	#overlay .close,
	.dropdown-menu .toggle_aside,
	#slider .toggle_aside {
		background: var(--background-color-light);
		border-bottom-color: var(--border-color-dark);
	}

	.aside .toggle_aside:hover,
	#overlay .close:hover,
	.dropdown-menu .toggle_aside:hover,
	#slider .toggle_aside:hover {
		background-color: var(--background-color-hover);
	}

	.aside .toggle_aside:hover .icon,
	#overlay .close:hover .icon,
	.dropdown-menu .toggle_aside:hover .icon {
		filter: brightness(2);
	}

	.aside:target + .close-aside, .configure .dropdown-target:target ~ .dropdown-close {
		background-color: var(--background-color-slider-shadow);
	}

	.aside.aside_feed {
		padding: 0;
	}

	.configure .dropdown .dropdown-menu {
		border-width: 0;
		box-shadow: -3px 0 3px var(--background-color-active);
	}

	.nav_menu .btn {
		margin: 5px 10px;
		padding: 3px 5px;
		min-height: 0;
	}

	.nav_menu .stick,
	.nav_menu .group {
		margin: 0 10px;
	}

	.nav_menu .stick .btn,
	.nav_menu .group .btn {
		margin: 5px 0;
	}

	.dropdown-target:target ~ .dropdown-toggle::after {
		background-color: var(--background-color-active);
		border-top: 2px solid var(--background-color-light);
		border-left: 2px solid var(--background-color-light);
	}

	.day .name {
		display: none!important;
		font-size: 1.1rem;
		text-shadow: none;
	}

	.flux:not(.current):hover .item.title {
		background: var(--background-color-hover);
	}

	.notification .close {
		background: transparent;
		display: block;
		left: 0;
	}

	.notification .close:hover {
		opacity: 0.5;
	}

	.notification .close .icon {
		display: none;
	}

	.post {
		padding-left: 15px;
		padding-right: 15px;
	}
}
