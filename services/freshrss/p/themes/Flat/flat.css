@charset "UTF-8";

/*=== GENERAL */
/*============*/
html, body {
	background: #fafafa;
	color: black;
	font-family: "OpenSans", "Cantarell", "Helvetica", "Arial", sans-serif;
}

/*=== Links */
a, button.as-link {
	color: #2980b9;
	outline: none;
}

/*=== Forms */
legend {
	margin: 20px 0 5px;
	padding: 5px 20px;
	background: #ecf0f1;
	display: inline-block;
	width: auto;
	font-size: 1.4em;
	border-radius: 20px;
	clear: both;
}

label {
	min-height: 25px;
	padding: 5px 0;
	cursor: pointer;
	color: #444;
}

textarea {
	width: 360px;
	height: 100px;
}

input, select, textarea {
	padding: 5px;
	background: #fff;
	color: #666;
	border: none;
	border-bottom: 3px solid #ddd;
	border-left-color: #ddd;
	border-radius: 5px;
	min-height: 25px;
	line-height: 1;
	vertical-align: middle;
}

option {
	padding: 0 .5em;
}

input:focus, select:focus, textarea:focus {
	color: #333;
	border-color: #2980b9;
}

input:invalid, select:invalid {
	color: #f00;
	border-color: #f00;
	box-shadow: none;
}

input:disabled, select:disabled {
	background: #eee;
}

/*=== Tables */
table {
	border-collapse: collapse;
}

th, td {
	border: 1px solid #ddd;
}

th {
	background: #f6f6f6;
}

.config-articleicons td,
.config-articleicons th {
	font-weight: normal;
	text-align: center;
}

/*=== COMPONENTS */
/*===============*/
/*=== Forms */
.form-group {
	padding: 5px;
	border: 1px solid transparent;
	border-radius: 3px;
}

.form-group::after {
	display: block;
	clear: both;
}

.form-group:hover {
	background: #fff;
	border: 1px solid #eee;
	border-radius: 3px;
}

.form-group.form-actions {
	margin: 15px 0 25px;
	padding: 5px 0;
	background-color: #e6ecedaa;
	border-top: 3px solid #bdc3c7;
	border-radius: 5px 5px 0 0;
}

.form-group.form-actions .btn {
	margin: 0 10px;
}

.form-group .group-name {
	padding: 10px 0;
}

.form-group .group-controls {
	min-height: 25px;
	padding: 5px 0;
}

.form-group .group-controls .control {
	line-height: 2.0em;
}

/*=== Buttons */
.stick input,
.stick .btn,
.group .btn {
	border-radius: 0;
}

.stick .btn:first-child,
.group .btn:first-child,
.stick input:first-child {
	border-radius: 5px 0 0 5px;
}

.stick .btn:last-child,
.group .btn:last-child,
.stick input:last-child,
.stick .dropdown:last-child > .btn,
.group .dropdown:last-child > .btn {
	border-radius: 0 5px 5px 0;
}

.stick .btn + .btn,
.group .btn + .btn,
.stick .btn + input,
.stick .btn + .dropdown > .btn,
.group .btn + .dropdown > .btn,
.stick input + .btn,
.stick input + input,
.stick input + .dropdown > .btn,
.stick .dropdown + .btn,
.group .dropdown + .btn,
.stick .dropdown + input,
.stick .dropdown + .dropdown > .btn,
.group .dropdown + .dropdown > .btn {
	border-left-width: 1px;
	border-left-style: solid;
}

.btn {
	margin: 0;
	padding: 0.25rem 0.5rem;
	background: #3498db;
	color: #fff;
	font-size: 0.9rem;
	border: none;
	border-bottom: 3px solid #2980b9;
	border-left-color: #2980b9;
	border-radius: 5px;
	min-height: 25px;
	min-width: 15px;
	line-height: 25px;
	vertical-align: middle;
}

.btn:hover {
	text-decoration: none;
}

.btn.active,
.btn:active,
.btn:hover,
.dropdown-target:target ~ .btn.dropdown-toggle {
	background: #2980b9;
}

.btn .icon {
	filter: brightness(5);
}

#toggle-unread .icon,
#toggle-starred .icon {
	filter: none;
}

.btn-important {
	font-weight: normal;
	background: #e67e22;
	color: #fff;
	border-bottom: 3px solid #d35400;
	border-left-color: #d35400;
}

.btn-important:hover,
.btn-important:active {
	background: #d35400;
}

.btn-important .icon {
	filter: brightness(3);
}

.btn-attention {
	background: #e74c3c;
	color: #fff;
	border-bottom: 3px solid #c0392b;
	border-left-color: #c0392b;
}

.btn-attention:hover,
.btn-attention:active {
	background: #c0392b;
}

.switch.active {
	background-color: #2980b9;
}

.switch.active:hover {
	background-image: url('./icons/disabled-light.svg');
}

/*=== Navigation */
.nav-list {
	font-size: 0.9rem;
}

.nav-list .item,
.nav-list .item.nav-header {
	min-height: 2.5em;
	line-height: 2.5;
}

.nav-list .item a:hover,
.nav-list .item .as-link:hover,
.nav-list .item.active {
	background: #2980b9;
	color: #fff;
}

.nav-list .item.active a,
.nav-list .item.active .as-link {
	color: #fff;
}

.nav-list .item > a,
.nav-list .item > .as-link {
	padding: 0 1rem;
}

.nav-list a:hover {
	text-decoration: none;
}

.nav-list .nav-header {
	padding: 0 1rem;
	font-weight: bold;
	background: #34495e;
	color: #fff;
}

.nav-list .nav-form {
	padding: 3px;
	text-align: center;
}

/*=== Dropdown */
.dropdown-menu {
	margin: 0.5rem 0 0;
	padding: 0.5rem 0 0.25rem 0;
	background: #fafafa;
	font-size: 0.8rem;
	border: 1px solid #95a5a6;
	border-radius: 3px;
	text-align: left;
}

.dropdown-menu::after {
	border-color: #95a5a6;
	right: 12px;
}

.dropdown-header,
.dropdown-section .dropdown-section-title {
	padding: 0 0.5rem 0.5rem;
	font-weight: bold;
	text-align: left;
	color: #34495e;
}

.dropdown-header a {
	position: absolute;
	right: 0.5rem;
}

.dropdown-menu .item > a,
.dropdown-menu .item > span,
.dropdown-menu .item > .as-link {
	padding: 0 22px;
	line-height: 2.5em;
	font-size: inherit;
}

.dropdown-menu .dropdown-section .item > a,
.dropdown-menu .dropdown-section .item > span,
.dropdown-menu .dropdown-section .item > .as-link {
	padding-left: 2rem;
}

.dropdown-menu .dropdown-section .item:last-child {
	margin-bottom: 0.5rem;
}

.dropdown-menu .item > a:focus,
.dropdown-menu .item > a:hover,
.dropdown-menu .item > button:focus:not([disabled]),
.dropdown-menu .item > button:hover:not([disabled]),
.dropdown-menu .item > label:focus:not(.noHover),
.dropdown-menu .item > label:hover:not(.noHover) {
	background: #2980b9;
	color: #fff;
}

.dropdown-menu > .item a:hover .icon,
.nav-list.aside .item a:hover .icon {
	filter: brightness(3);
}

.dropdown-menu > .item[aria-checked="true"] > a::before {
	font-weight: bold;
	margin: 0 0 0 -14px;
}

.dropdown-menu .input select,
.dropdown-menu .input input {
	margin: 0 auto 5px;
	padding: 2px 5px;
	border-radius: 3px;
}

.item ~ .dropdown-header,
.dropdown-section ~ .dropdown-section,
.item.separator {
	border-top-color: #ddd;
}

/*=== Alerts */
.alert {
	background: #f4f4f4;
	color: #aaa;
	font-size: 0.9em;
	border: 1px solid #ccc;
	border-right: 1px solid #aaa;
	border-bottom: 1px solid #aaa;
	border-radius: 5px;
	text-shadow: 0 0 1px #eee;
}

.alert-head {
	font-size: 1.15em;
}

.alert > a {
	text-decoration: underline;
	color: inherit;
}

.alert-warn {
	background: #ffe;
	color: #c95;
	border: 1px solid #eeb;
}

.alert-success {
	background: #dfd;
	color: #484;
	border: 1px solid #cec;
}

.alert-error {
	background: #fdd;
	color: #844;
	border: 1px solid #ecc;
}

/*=== Pagination */
.pagination {
	background: #ecf0f1;
	color: #000;
}

.pagination .item a {
	color: #000;
}

#load_more.loading,
#load_more.loading:hover {
	background: url("loader.gif") center center no-repeat #34495e;
}

/*=== Boxes */
.box {
	border: 1px solid #ddd;
	border-radius: 5px;
}

.box .box-title {
	background: #ecf0f1;
	color: #333;
	border-bottom: 1px solid #ddd;
	border-radius: 5px 5px 0 0;
}

.box .box-title .configure {
	margin-right: 4px;
}

.box .box-content {
	padding-left: 30px;
	max-height: 260px;
}

.box .box-content .item {
	font-size: 0.9rem;
}

.box .box-title .configure .icon,
.box .box-content .item .configure .icon {
	vertical-align: middle;
	background-color: #95a5a6;
	border-radius: 3px;
}

/*=== Tree */
.tree {
	margin: 10px 0;
}

.tree-folder-title {
	position: relative;
	padding: 0 10px;
	background: #34495e;
	line-height: 2.5rem;
	font-size: 1rem;
}

.tree-folder-title .title {
	background: inherit;
	color: #fff;
}

.tree-folder-title .title:hover {
	text-decoration: none;
}

.tree-folder.active .tree-folder-title {
	background: #2980b9;
	font-weight: bold;
}

.tree-folder-items {
	background: #2c3e50;
}

.tree-folder-items > .item {
	font-size: 0.8rem;
}

.tree-folder-items > .item.active {
	background: #2980b9;
}

.tree-folder-items > .item > a {
	text-decoration: none;
	color: #fff;
}

/*=== Scrollbar */

@supports (scrollbar-width: thin) {
	#sidebar {
		scrollbar-color: rgba(255, 255, 255, 0.05) rgba(0, 0, 0, 0.0);
	}

	#sidebar:hover {
		scrollbar-color: rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0.0);
	}
}

@supports not (scrollbar-width: thin) {
	#sidebar::-webkit-scrollbar-thumb {
		background: rgba(255, 255, 255, 0.1);
	}

	#sidebar:hover::-webkit-scrollbar-thumb {
		background: rgba(255, 255, 255, 0.3);
	}
}

/*=== STRUCTURE */
/*===============*/
/*=== Header */
.header {
	background: #ecf0f1;
}

.header > .item {
	vertical-align: middle;
	text-align: center;
}

.header > .item.title a:hover .logo {
	filter: brightness(1.5);
}

.header > .item.title h1 {
	margin: 0.5em 0;
}

.header > .item.title h1 a {
	text-decoration: none;
}

.header > .item.search input {
	width: 350px;
}

.aside {
	background: #ecf0f1;
}

.aside.aside_feed {
	padding: 10px 0;
	text-align: center;
	background: #34495e;
	border-radius: 0 10px 0 0;
}

.aside.aside_feed .tree {
	margin: 10px 0 50px;
}

.aside.aside_feed .icon {
	filter: brightness(3);
}

/*=== Aside main page (categories) */
.aside.aside_feed .category .title:not([data-unread="0"])::after {
	background-color: #2c3e50;
}

.aside.aside_feed .feed .item-title:not([data-unread="0"])::after {
	background-color: #34495e;
}

.aside .about {
	margin: 1rem;
	display: block;
	color: #ecf0f1;
	font-style: italic;
}

/*=== Aside main page (feeds) */
.feed.item.empty.active {
	background: #f39c12;
}

.feed.item.error.active {
	background: #bd362f;
}

.feed.item.empty,
.feed.item.empty > a {
	color: #e67e22;
}

.feed.item.error,
.feed.item.error > a {
	color: #bd362f;
}

.feed.item.empty.active,
.feed.item.error.active,
.feed.item.empty.active > a,
.feed.item.error.active > a {
	color: #fff;
}

.aside_feed .tree-folder-items .dropdown-menu::after {
	left: 2px;
}

.aside_feed .tree-folder-items .item .dropdown-target:target ~ .dropdown-toggle > .icon,
.aside_feed .tree-folder-items .item:hover .dropdown-toggle > .icon,
.aside_feed .tree-folder-items .item.active .dropdown-toggle > .icon {
	border-radius: 3px;
}

/*=== Prompt (centered) */
.prompt .form-group {
	margin-bottom: 1rem;
}

.prompt .form-group::after {
	display: none;
}

.prompt .form-group.form-group-actions {
	display: flex;
	margin-top: 2rem;
	align-items: center;
	justify-content: space-between;
}

.prompt .btn.btn-important {
	padding-left: 1.5rem;
	padding-right: 1.5rem;
	font-size: 1.1rem;
}

/*=== New article notification */
#new-article {
	background: #3498db;
	font-size: 0.9em;
	text-align: center;
}

#new-article > a {
	line-height: 3em;
	font-weight: bold;
	color: #fff;
}

#new-article > a:hover {
	text-decoration: none;
	background: #2980b9;
}

/*=== Day indication */
.day {
	padding: 0 10px;
	font-weight: bold;
	line-height: 3em;
	border-left: 2px solid #ecf0f1;
}

.day .name {
	padding: 0 10px 0 0;
	color: #aab;
	font-size: 1.8em;
	opacity: 0.3;
	font-style: italic;
	text-align: right;
}

/*=== Index menu */
.nav_menu {
	text-align: center;
	padding: 5px 0;
}

.dropdown-menu .dropdown-header .icon {
	filter: invert(0.6);
}

.dropdown-menu .dropdown-header a:hover .icon {
	filter: invert(0.6) invert(77%) sepia(63%) saturate(4993%) hue-rotate(177deg) brightness(88%) contrast(96%);
}

/*=== Feed articles */
.flux {
	border-left: 2px solid #ecf0f1;
}

.flux:not(.current):hover .flux_header .title,
.flux .flux_header:hover {
	background: #fff;
}

.flux.current {
	background: #fff;
	border-left-color: #3498db;
}

.flux.not_read {
	border-left-color: #ff5300;
}

.flux.not_read:not(.current) {
	background: #fff3ed;
}

.flux.favorite {
	border-left-color: #ffc300;
}

.flux.favorite:not(.current) {
	background: #fff6da;
}

.flux_header {
	font-size: 0.8rem;
	cursor: pointer;
	border-top: 1px solid #ecf0f1;
}

.flux_header .title {
	font-size: 0.9rem;
}

.flux .flux_header .date,
.flux .flux_content .bottom .date {
	color: #666;
}

.flux .bottom {
	font-size: 0.8rem;
	text-align: center;
}

/*=== Content of feed articles */
.content {
	padding: 20px 10px;
}

.content > h1.title > a {
	color: #000;
}

.content hr {
	margin: 30px 10px;
	background: #ddd;
	height: 1px;
	border: 0;
	box-shadow: 0 2px 5px #ccc;
}

.content pre {
	background: #222;
	color: #fff;
	border-radius: 3px;
}

.content code {
	background: #fafafa;
	color: #d14;
	border-color: #eee;
	border-radius: 3px;
}

.content pre code {
	background: transparent;
	color: #fff;
	border: none;
}

.content blockquote {
	margin: 0;
	padding: 5px 20px;
	background: #fafafa;
	display: block;
	color: #333;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
}

.content blockquote p {
	margin: 0;
}

/*=== Notification and actualize notification */
.notification {
	background: #ddd;
	color: #666;
	font-size: 0.9em;
	border: none;
	border-radius: 3px;
	text-align: center;
	font-weight: bold;
	vertical-align: middle;
}

.notification.good {
	background: #1abc9c;
	color: #fff;
}

.notification.bad {
	background: #e74c3c;
	color: #fff;
}

.notification a {
	color: #000;
}

.notification .close {
	border-radius: 0 3px 3px 0;
}

.notification.good .close:hover {
	background: #16a085;
}

.notification .close .icon {
	filter: brightness(2.5);
}

.notification.bad .close:hover {
	background: #c0392b;
}

.notification .close:hover .icon {
	filter: brightness(4);
}

/*=== "Load more" part */
#bigMarkAsRead.big {
	text-align: center;
	text-decoration: none;
	background: #ecf0f1;
}

#bigMarkAsRead:hover {
	background: #34495e;
	color: #fff;
}

/*=== Navigation menu (for articles) */
#nav_entries {
	background: #34495e;
}

/*=== READER VIEW */
/*================*/
#stream.reader .flux {
	background: #fafafa;
	color: #34495e;
	border: none;
}

#stream.reader .flux .flux_content {
	background-color: #fff;
	border-color: #ecf0f1;
}

#stream.reader .flux .author {
	color: #999;
}

/*=== GLOBAL VIEW */
/*================*/
.box.category .box-title .title {
	font-weight: normal;
	text-decoration: none;
	text-align: left;
}

.box.category:not([data-unread="0"]) .box-title {
	background: #3498db;
}

.box.category:not([data-unread="0"]) .box-title:active {
	background: #2980b9;
}

.box.category:not([data-unread="0"]) .box-title .title {
	font-weight: bold;
	color: #fff;
}

.box.category .title:not([data-unread="0"])::after {
	background: none;
	border: 0;
	position: absolute;
	top: 5px; right: 10px;
	font-weight: bold;
	box-shadow: none;
	text-shadow: none;
}

/*=== DIVERS */
/*===========*/
.aside.aside_feed .nav-form input,
.aside.aside_feed .nav-form select {
	width: 140px;
}

.aside.aside_feed .nav-form .dropdown .dropdown-menu {
	right: -20px;
}

.aside.aside_feed .nav-form .dropdown .dropdown-menu::after {
	right: 33px;
}

/*=== STATISTICS */
/*===============*/
.stat {
	margin: 10px 0 20px;
}

.stat th,
.stat td,
.stat tr {
	border: none;
}

.stat > table td,
.stat > table th {
	border-bottom: 1px solid #ddd;
}

/*=== MOBILE */
/*===========*/

@media (max-width: 840px) {
	.form-group .group-name {
		padding-bottom: 0;
	}

	.aside {
		transition: width 200ms linear;
	}

	.aside .toggle_aside,
	#overlay .close,
	.dropdown-menu .toggle_aside {
		background: #2c3e50;
	}

	.aside.aside_feed {
		padding: 0;
	}

	.nav_menu .btn {
		margin: 5px 10px;
		padding: 3px 5px;
		min-height: 0;
	}

	.nav_menu .stick {
		margin: 0 10px;
	}

	.nav_menu .stick .btn {
		margin: 5px 0;
	}

	.nav_menu .search {
		display: inline-block;
		max-width: 97%;
	}

	.nav_menu .search input {
		padding: 3px 5px;
		max-width: 97%;
		width: 90px;
	}

	.nav_menu .search input:focus {
		width: 400px;
	}

	.dropdown-target:target ~ .dropdown-toggle::after {
		background-color: #fafafa;
		border-top: 1px solid #95a5a6;
		border-left: 1px solid #95a5a6;
	}

	.dropdown-target:target ~ .btn.dropdown-toggle::after {
		bottom: -19px;
	}

	.day .name {
		font-size: 1.1rem;
	}

	.notification {
		border-radius: 0;
	}

	.notification .close {
		background: transparent;
		display: block;
		left: 0;
	}

	.notification .close:hover {
		opacity: 0.5;
	}

	.notification .close .icon {
		display: none;
	}

	.post {
		padding-left: 15px;
		padding-right: 15px;
	}

	#close-slider.active {
		background: #2c3e50;
	}
}
