// The main color of the theme
$main-first: #36c; // main color
$main-first-alt: #25c; // var for hovers
$main-first-light: #effcfd; // var light 1
$main-first-lighter: #f7fdfe; // var light 2
$main-first-dark: #35363c; // var for hovers
$main-first-darker: #1d1e22; // var for hovers

// linked font colors
$main-font-color: #303136;
$light-font-color: #5b6871;
$white: #fff; // white (sometimes we would like to tint it a little)

// the whole palette of grays, very useful
// we will try to tint these grays according to the main color
$grey-darker: #3b3f4d;
$grey-dark: #5b6871;
$grey-medium-dark: #a6a7ae;
$grey-medium: #c5ced3;
$grey-medium-light: #d5d8db;
$grey-light: #eff0f2;
$grey-lighter: #f9fafb;

$unread-font-color: $main-first;
$unread-bg: #f2f6f8;
$unread-bg-light: #fdfdfe;

// alert colors (red, yellow, green)
$alert-bg: #f5633e; // the base
$alert-light: #fde0d8; // +light
$alert-text: #73341f; // +dark

$code-text: #e41212;
$code-bg: #fde3e3;

$warning-bg: #f4f762;
$warning-light: #fdfde0;
$warning-text: #73762f;

$success-bg: #10f587;
$success-light: #cffde7;
$success-text: #0c7540;

// favourites
$fav-bg: #ffc300;
$fav-light: #fff6da;

// the sidebar color, useful if you want a theme that would have a dark sidebar, e.g.
// stylelint-disable-next-line color-hex-length
$sid-font-color: #ffffff; // the background color of the left bar and the header
$sid-bg: #303136; // the general background of the left bar, and of the header
$sid-bg-alt: #26272a; // background inside groups
$sid-bg-dark: #17181a; // the hover
$sid-sep: #3f3f3f; // the separators
$sid-active: $main-first; // the active color
// stylelint-disable-next-line color-hex-length
$sid-active-font: #ffffff; // the active color
$sid-pills: rgba(0,0,0, 0.25); // capsules
