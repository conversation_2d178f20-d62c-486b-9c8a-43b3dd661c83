@use "sass:string";

@use "mixins";

@use "variables";

/*=== Tree */
.tree {
	margin: 10px 0;

	&#sidebar {
		scrollbar-color: rgba(255,255, 0, 0.1) rgba(0, 0, 0, 0.05);
		scrollbar-color: string.unquote(variables.$sid-font-color + "33") string.unquote(variables.$sid-font-color + "22");
	}


	.tree-folder {
		border-bottom: 1px solid variables.$sid-sep;

		.tree-folder-title {
			padding: 0.75rem 1rem;
			background: variables.$sid-bg;
			position: relative;
			font-size: 0.85rem;
			letter-spacing: 1px;
			font-weight: 700;
			text-transform: uppercase;

			button.dropdown-toggle {
				margin: -0.75rem 0.25rem -0.75rem -1rem;
				padding: 0.75rem 0 0.75rem 1rem;

				&:hover {
					.icon {
						filter: brightness(2) !important;
						transition: 0.1s linear;
					}
				}
			}

			.icon {
				margin-right: 0.5rem;
			}

			.title {
				background: inherit;
				color: variables.$sid-font-color;

				&:hover {
					text-decoration: none;
				}
			}
		}

		&.active {
			.tree-folder-title {
				background: variables.$sid-bg;
				font-weight: bold;
			}
		}

		.tree-folder-items {
			background: variables.$sid-bg-alt;

			.item {
				font-size: 1rem;
				font-weight: 400;

				@include mixins.transition(all, 0.15s, ease-in-out);

				&.active {
					background: variables.$sid-active;

					.dropdown li a {
						color: variables.$main-font-color;

						&:hover {
							color: variables.$sid-font-color;
						}
					}

					a {
						color: variables.$sid-active-font;
					}
				}

				&:hover {
					background: variables.$sid-bg-dark;
				}

				a {
					text-decoration: none;
					color: variables.$sid-font-color;

					&.dropdown-toggle {
						.icon {
							margin-right: 0.25rem;
						}
					}
				}
			}
		}
	}
}

/*=== Buttons */
.stick,
.group {
	input, .btn {
		border-radius: 0;
	}

	.btn:first-child {
		border-radius: 5px 0 0 5px;
	}

	.btn:last-child, input:last-child, .dropdown:last-child > .btn {
		border-radius: 0 5px 5px 0;
	}

	.btn + .btn,
	.btn + input,
	.btn + .dropdown > .btn,
	input + .btn,
	input + input,
	input + .dropdown > .btn,
	.dropdown + .btn,
	.dropdown + input,
	.dropdown + .dropdown > .btn {
		border-left: 1px solid variables.$grey-medium-light;
	}

}

.aside {
	background: variables.$sid-bg;

	&.aside_feed {
		padding: 10px 0;
		text-align: center;
		background: variables.$sid-bg;

		.tree-folder-title:hover button.dropdown-toggle .icon {
			filter: none;
		}
	}

	&.aside_feed .tree {
		margin: 10px 0 50px;
	}

	a:hover {
		.icon {
			filter: brightness(2);
			transition: 0.1s linear;
		}
	}

}


/* Sidebar des pages de configuration */



/*=== Navigation */
.nav-list {
	font-size: 1rem;

	.item.nav-header,
	.item {
		min-height: 2.5em;
		line-height: 2.5em;
	}

	.item {
		background: variables.$sid-bg;
		color: variables.$white;
		min-height: 2.5em;
		line-height: 2.5em;

		&.nav-header {
			min-height: 2.5em;
			line-height: 2.5em;
		}

		a, .as-link {
			padding: 0 1rem;
			color: variables.$sid-font-color;

			@include mixins.transition(all, 0.15s, ease-in-out);
		}

		a:hover, .as-link:hover {
			background: variables.$sid-bg-dark;
			text-decoration: none;
		}

		&.active {
			background: variables.$main-first;
			color: variables.$white;

			a, .as-link {
				background: variables.$main-first;
				color: variables.$white;
				text-decoration: none;
			}
		}
	}

	.nav-header {
		padding: 0 1rem;
		font-weight: bold;
		color: variables.$grey-dark;
		text-transform: uppercase;
		letter-spacing: 1px;
	}

	.nav-form {
		padding: 3px;
		text-align: center;
	}
}

/*=== Aside main page (categories) */
.aside_feed .tree-folder-title > .title:not([data-unread="0"]) {
	width: calc(100% - 35px - 35px);
}

.aside.aside_feed .category .title:not([data-unread="0"])::after {
	margin: 0.75rem 0 0 0;
	background-color: variables.$sid-pills;
}

.aside.aside_feed .feed .item-title:not([data-unread="0"])::after {
	margin: 0.5em 0 0 0;
	background-color: variables.$sid-pills;
}

.feed.item.empty.active {
	background: variables.$grey-dark;
}

.feed.item.error.active {
	background: variables.$grey-dark;
}

.feed.item.empty,
.feed.item.empty > a {
	color: variables.$grey-dark;
}

.feed.item.error,
.feed.item.error > a {
	color: variables.$grey-dark;
}

.feed.item.empty.active,
.feed.item.error.active,
.feed.item.empty.active > a,
.feed.item.error.active > a {
	color: variables.$white;
}

.aside_feed .tree-folder-items .dropdown-menu::after {
	left: 2px;
}

.aside_feed .tree-folder-items .item .dropdown-target:target ~ .dropdown-toggle > .icon,
.aside_feed .tree-folder-items .item:hover .dropdown-toggle > .icon,
.aside_feed .tree-folder-items .item.active .dropdown-toggle > .icon {
	border-radius: 3px;
}

.aside_feed .stick #btn-add {
	border-left-color: variables.$sid-bg;
}
