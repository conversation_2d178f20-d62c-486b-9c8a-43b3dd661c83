@use "variables";

/*=== Configuration pages */
.post {
	font-size: 1rem;

	h1, h2 {
		color: variables.$main-font-color;
		font-size: 2rem;
		margin-top: 1.75rem;
		font-weight: 300;
		line-height: 1.2em;
	}

	h2 {
		font-size: 1.5rem;
	}

	a[href="./"] { // This is the "Back to your feeds" button.
		margin: 0;
		padding: 0.75rem 1.5rem;
		background: variables.$grey-lighter;
		display: inline-block;
		color: variables.$grey-dark;
		font-size: 1rem;
		border: 1px solid variables.$grey-medium-light;
		border-radius: 5px;
		min-width: 15px;
		line-height: 25px;
		vertical-align: middle;
		cursor: pointer;
		overflow: hidden;

		&:hover {
			background: variables.$main-first;
			color: white;
			border: 1px solid variables.$main-first;
			text-decoration: none;
		}
	}

}



#slider {
	border-left: none;
	box-shadow: 0px 6px 8px 0px rgba(0,0,0,0.35);
}

.theme-preview-list {
	.preview-container {
		.properties {
			padding: 1rem;
			background: rgba(0, 0, 0, 0.75);
			color: white;
			border: 0;

			.page-number {
				right: 1rem;
				top: 1rem;
			}
		}
	}
}
