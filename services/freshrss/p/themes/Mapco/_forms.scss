@use "mixins";

@use "variables";

/* btns */
.btn {
	margin: 0;
	padding: 0.5rem 1.5rem;
	background: variables.$grey-lighter;
	color: variables.$grey-dark;
	font-size: 1rem;
	border: none;
	border-radius: 5px;
	min-height: 25px;
	min-width: 15px;
	line-height: 25px;
	vertical-align: middle;

	@include mixins.transition(all, 0.15s, ease-in-out);

	&.btn-important {
		background: variables.$main-first;
		color: variables.$white;

		&:hover,
		&:active {
			background: variables.$main-first-alt;
		}

		img {
			filter: brightness(3);
		}
	}


	&.btn-attention {
		background: variables.$alert-bg;
		color: #fff;

		&:hover,
		&:active {
			background: variables.$alert-text;
		}
	}

	&:hover {
		text-decoration: none;
	}
}

/*=== Forms */
legend {
	margin: 2rem 0 1rem 0;
	padding: 0;
	display: inline-block;
	width: auto;
	font-size: 1rem;
	clear: both;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-weight: 700;
}

label {
	min-height: 25px;
	padding: 5px 0;
	cursor: pointer;
	color: variables.$grey-dark;
}

textarea {
	max-width: 100%;
	width: 360px;
	height: 100px;
}

input, select, textarea, button {
	padding: 5px 10px;
	background: variables.$white;
	color: variables.$grey-dark;
	font-family: "lato", "Helvetica", "Arial", sans-serif;
	font-size: 1rem;
	border: 1px solid variables.$grey-light;
	border-radius: 2px;
	min-height: 25px;
	line-height: 25px;
	vertical-align: middle;
}

select {
	padding-top: 9px;
	padding-bottom: 9px;
}

option {
	padding: 0 .5em;
}

input:focus, select:focus, textarea:focus {
	color: variables.$main-font-color;
	border-color: variables.$main-first;
}

input:invalid, select:invalid {
	color: variables.$alert-bg;
	border-color: variables.$alert-bg;
	box-shadow: none;
}

input:disabled, select:disabled {
	background: variables.$grey-light;
}

.form-group {
	padding: 5px;
	border-radius: 3px;

	&::after {
		display: block;
		clear: both;
	}

	.group-name {
		padding: 10px 0;
	}

	.group-controls {
		min-height: 25px;
		padding: 5px 0;
	}

	.group-controls .control {
		line-height: 2.0em;
	}

	&.form-actions {
		margin: 15px 0 25px;
		padding: 5px 0;
	}

	&.form-actions .btn {
		margin: 0 0.5rem 0 0;
	}
}



