@use "sass:color";

@use "mixins";

@use "variables";

/*=== Feed articles */
.flux {
	background: variables.$white;

	@include mixins.transition(all, 0.15s, ease-in-out);

	.flux_header {
		&:hover {
			background: variables.$grey-lighter;

			&:not(.current):hover .item .title {
				background: variables.$grey-lighter;
			}
		}
	}

	&.current {
		background: variables.$grey-lighter;
		border-left-color: variables.$main-first;
	}

	&.not_read:not(.current) {
		background: variables.$unread-bg;

		&:hover .item .title {
			background: variables.$unread-bg;
		}
	}

	&.not_read {
		.item .title {
			a {
				color: variables.$unread-font-color;
			}

		}

		.item.website {
			a {
				color: variables.$unread-font-color;
			}
		}

		.item .date {
			color: color.scale(variables.$unread-font-color, $alpha: -50%);
		}
	}

	&.favorite {
		border-left-color: variables.$fav-bg;

		@include mixins.transition(all, 0.15s, ease-in-out);
	}

	&.favorite:not(.current) {
		background: variables.$fav-light;

		&:hover .item .title {
			background: variables.$fav-light;
		}
	}

	.website {
		a {
			color: variables.$main-font-color;
			opacity: 0.75;
		}
	}

	.flux_header .date,
	.flux_content .bottom .date {
		color: color.scale(variables.$main-font-color, $alpha: -50%);
		font-size: 0.85rem;
	}

	.bottom {
		font-size: 1rem;
		text-align: center;
	}
}

.flux_header {
	font-size: 1rem;
	cursor: pointer;
	border-top: 1px solid variables.$grey-light;

	.title {
		font-size: 1rem;
	}
}

