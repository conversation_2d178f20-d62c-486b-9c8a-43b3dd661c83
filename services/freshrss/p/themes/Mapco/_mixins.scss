@use "sass:list";
/* stylelint-disable property-no-vendor-prefix */

/* FUNCTIONS */

//animation

@mixin transition($target, $duration, $ease) {
	transition: $target $duration $ease;
}

//animation

@mixin animation-delay($delay) {
	animation-delay: $delay;
}

//animation

@mixin animation($animate...) {
	$max: list.length($animate);
	$animations: '';

	@for $i from 1 through $max {
		$animations: #{$animations + list.nth($animate, $i)};

		@if $i < $max {
			$animations: #{$animations + ", "};
		}
	}
	animation: #{$animations};
}

//keyframes

@mixin keyframes($animationName) {

	@keyframes #{$animationName} {
		@content;
	}
}

@mixin border-radius($radius: 4px) {
	border-radius: $radius;
}
