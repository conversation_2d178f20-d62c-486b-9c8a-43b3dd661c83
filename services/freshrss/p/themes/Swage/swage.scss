@use "sass:color";

@charset "UTF-8";
//colors

$color_text:	#181621;
$color_light:	#fcfcfc;
$color_nav:		#0062be;
$color_aside:	#22303d;
$color_alert:	#fa8052;
$color_good:	#5eaabf;
$color_bad:		#b0425b;
$color_grey:	#e3e3e3;
$color_grey_transparent: #d9d9d9bb;
$color_stared:	#fff6da;
$color_unread:	#fff3ed;
$color_hover:	#fff;

:root {
	--color-text-light: #{$color_light};
	--color-text-light-darker: #{color.adjust(  $color_light,  $lightness: -40%)};
	--color-text-dark: #{$color_text};
	--color-text-nav: #{color.adjust(  $color_nav,  $lightness: -10%)};
	--color-text-aside: #{$color_aside};
	--color-text-alert: #{$color_alert};
	--color-text-good: #{$color_good};
	--color-text-bad: #{$color_bad};
	--color-text-bad-lighter: #{color.adjust( $color_bad, $lightness: 10%)};

	--color-text-shadow-light: #{$color_light};
	--color-box-shadow-light: #{color.adjust(  $color_light,  $lightness: -10%)};

	--color-border-light: #{$color_light};
	--color-border-light-darker: #{color.adjust(  $color_light,  $lightness: -10%)};
	--color-border-grey: #{$color_grey};
	--color-border-nav: #{color.adjust(  $color_nav,  $lightness: -10%)};
	--color-border-bad: #{$color_bad};

	--color-background-light: #{$color_light};
	--color-background-light-darker: #{color.adjust(  $color_light,  $lightness: -10%)};
	--color-background-light-darker-transparent: #{$color_grey_transparent};
	--color-background-dark: #{$color_text};
	--color-background-nav: #{$color_nav};
	--color-background-nav-darker: #{color.adjust(  $color_nav,  $lightness: -10%)};
	--color-background-aside: #{$color_aside};
	--color-background-alert: #{$color_alert};
	--color-background-alert-darker: #{color.adjust(  $color_alert,  $lightness: -10%)};
	--color-background-good: #{$color_good};
	--color-background-bad: #{color.adjust( $color_bad, $lightness: 10%)};
	--color-background-stared: #{$color_stared};
	--color-background-unread: #{$color_unread};
	--color-background-hover: #{$color_hover};

	--frss-scrollbar-handle: rgba(0, 0, 0, 0.1);
	--frss-scrollbar-handle-hover: rgba(0, 0, 0, 0.4);
	--frss-scrollbar-track: rgba(0, 0, 0, 0.05);
	--frss-scrollbar-track-hover: rgba(0, 0, 0, 0.1);
}


// @extend-elements
%input {
	min-height: 25px;
	margin-top: 4px;
	line-height: 2.25;
	vertical-align: middle;
	background-color: var(--color-background-light);
	border: 2px solid var(--color-border-grey);
	padding-left: 8px;
}

%invalid {
	padding-left: 5px;
	color: var(--color-text-bad);
	border-left-color: var(--color-border-bad);
	border-left-width: 5px;
	box-shadow: none;
}

%nav-list {
	min-height: 2.5em;
	line-height: 2.5;
}

%dropdown {
	padding: 0 22px;
	color: var(--color-text-light);
	font-size: 0.8rem;
	line-height: 2.5;
}

%after {
	display: block;
	clear: both;
}

%aside-width {
	width: 231px;
}

// /@extend-elements
html,
body {
	color: var(--color-text-dark);
	font-family: Helvetica, Arial, sans-serif;
}

a {
	color: var(--color-text-nav);
	outline: none;

	&#btn-subscription {
		width: 76%;
	}

	&#btn-add {
		width: 5%;
	}
}

img {
	&.icon:hover {
		background: none;
	}
}

sup {
	top: -0.3em;
}

legend {
	margin: 20px 0 5px;
	padding: 5px 20px;
	background-color: var(--color-background-aside);
	display: inline-block;
	width: auto;
	color: var(--color-text-light);
	font-size: 1.4em;
	clear: both;
}

label {
	min-height: 25px;
}

textarea {
	width: 360px;
	height: 100px;

	@extend %input;

	&:focus {
		border-color: var(--color-border-nav);
	}
}

input,
select {

	@extend %input;

	&:focus {
		border-color: var(--color-border-nav);
	}

	&:invalid {

		@extend %invalid;
	}

	&:disabled {
		background-color: var(--color-background-light);
	}
}

select {
	padding-top: 7px;
	padding-bottom: 8px;
}

option {
	padding: 0 .5em;
}

table {
	border-collapse: collapse;
}

td,
th {
	border: 1px solid var(--color-border-light-darker);
}

th {
	background-color: var(--color-background-light);
}

form {
	td,
	th {
		font-weight: normal;
		text-align: center;
	}
}

.category {
	.title.error::before {
		display: inline-block;
		padding-right: 7px;
		width: 16px;
		content: url(../Swage/icons/error.svg);
	}
}


.form-group {
	padding: 5px;
	border: 1px solid transparent;

	&:hover {
		background-color: var(--color-background-light);
		border: 1px solid var(--color-border-light);
	}

	&.form-actions {
		margin: 15px 0 25px;
		padding: 5px 0;
		background-color: var(--color-background-light-darker-transparent);
		border-top: 3px solid var(--color-border-light-darker);

		.btn {
			margin: 0 10px;
		}
	}

	.group-name {
		padding: 10px 0;
		text-align: right;
	}

	.group-controls {
		min-height: 25px;
		padding: 5px 0;

		.control {
			line-height: 2.0;
		}
	}
}

.form-group::after {

	@extend %after;
}

.stick {
	select {
		margin-top: 0;
	}

	&.configure-feeds {

		@extend %aside-width;
	}
}

.btn {
	margin: 0;
	padding: 0.25rem 0.5rem;
	background-color: var(--color-background-nav);
	color: var(--color-text-light);
	font-size: 0.9rem;
	border: none;
	min-height: 25px;
	min-width: 15px;
	vertical-align: middle;
	line-height: 1.5;
	text-decoration: none;

	&.active,
	&:active,
	&:hover {
		background-color: var(--color-background-nav-darker);
		text-decoration: none;
	}

	.icon {
		filter: brightness(3);
	}
}

.btn-important, .btn-attention {
	font-weight: normal;
	background-color: var(--color-background-alert);
	color: var(--color-text-light);

	&:hover,
	:active {
		background-color: var(--color-background-alert-darker) !important;
	}
}

.manage-list {
	.configure {
		.icon {
			filter: brightness(0.4);
			vertical-align: sub;
		}

		&:hover {
			filter: invert(56%) sepia(87%) saturate(1185%) hue-rotate(327deg) brightness(104%) contrast(96%);
		}
	}
}

.switch.active {
	background-color: var(--color-background-nav);

	&:hover {
		background-image: url('./icons/disabled-light.svg');
	}
}

.nav-list {
	font-size: 0.9rem;

	.item {

		@extend %nav-list;

		.nav-header {

			@extend %nav-list;
			padding: 0 1rem;
			font-weight: bold;
			background-color: var(--color-background-aside);
			color: var(--color-text-light);
			cursor: default;
		}

		a:hover,
		.as-link:hover {
			background-color: var(--color-background-nav-darker);
			color: var(--color-text-light);
		}

		&.active {
			background: var(--color-background-nav-darker);
			color: var(--color-text-light);

			a,
			.as-link {
				color: var(--color-text-light);
			}
		}

		> a,
		> .as-link {
			padding: 0 1.5rem;
		}

		.icon {
			filter: brightness(3);
		}
	}

	.nav-form {
		padding: 3px;
		text-align: center;
	}

	a:hover,
	.as-link:hover {
		text-decoration: none;
	}
}

.dropdown-menu {
	padding: 0.5rem 0 1rem 0;
	font-size: 0.8rem;
	text-align: left;
	border: none;
	background-color: var(--color-background-nav-darker);

	.dropdown-header {
		cursor: default;
		padding: 0.5rem 10px 0.5rem 10px;
		font-weight: bold;
		color: var(--color-text-light);

		a,
		.as-link {
			padding: 0 5px;
			position: absolute;
			right: 5px;

			&:hover {
				background-color: var(--color-background-nav);
			}
		}
	}

	.dropdown-section {
		.dropdown-section-title {
			cursor: default;
			padding: 0.25rem 0.5rem 0.125rem 0.25rem;
			font-weight: bold;
			color: var(--color-text-light);
		}

		.item {
			a,
			.as-link {
				padding: 0 22px;

				&:hover {
					background-color: var(--color-background-nav);
				}
			}
		}
	}

	> {
		.item {

			@extend %dropdown;
			padding: 0 0 0 0.5rem;

			a,
			> span,
			> .as-link,
			button {

				@extend %dropdown;
			}

			> a {
				min-width: initial;
				white-space: nowrap;
			}

			> a:hover,
			> button:hover {
				background-color: var(--color-background-nav);
				color: var(--color-text-light);
			}
		}

		.item[aria-checked="true"] > a::before {
			font-weight: bold;
			margin: 0 0 0 -14px;
		}
	}

	.help a {
		color: var(--color-text-light);
		text-decoration: underline;
		text-decoration-style: dotted;

		&:hover {
			text-decoration-style: solid;
		}
	}

	.input {
		select,
		input {
			margin: 0 auto 5px;
			padding: 2px 5px;
		}
	}
}

#dropdown-search-wrapper .dropdown-menu {
	padding-top: 1rem;
	padding-bottom: 0.25rem;

	.stick.search {
		width: 100%;

		input[type="search"] {
			width: 100%;
			border: 0;
		}
	}
}

.labels,
.tags,
.share {
	.dropdown-menu {
		right: auto;
	}
}

.item ~ .dropdown-header,
.item.separator {
	border-top-color: var(--color-border-light-darker);
	cursor: default;
}

.alert {
	margin: 0.25rem auto;
	background-color: var(--color-background-light);
	color: var(--color-text-light-darker);
	font-size: 0.9em;
	border: none;
	text-shadow: 0 0 1px var(--color-text-shadow-light);

	> a {
		color: inherit;
		text-decoration: underline;
	}
}

.alert-head {
	font-size: 1.15em;
}

.alert-warn,
.alert-success,
.alert-error {
	border: none;
}

.alert-warn {
	background-color: var(--color-background-light);
	color: var(--color-text-alert);
}

.alert-success {
	background-color: var(--color-background-light);
	color: var(--color-text-good);
}

.alert-error {
	background-color: var(--color-background-light);
	color: var(--color-text-bad);
}

.pagination {
	background: var(--color-background-light-darker);
	color: var(--color_text);

	.item a {
		color: var(--color_text);
	}
}

#load_more.loading,
#load_more.loading:hover {
	background: url(loader.gif) center center no-repeat var(--color-background-aside);
}


.content {
	padding: 20px 10px;

	hr {
		margin: 30px 10px;
		background: var(--color-background-light-darker);
		height: 1px;
		border: 0;
		box-shadow: 0 2px 5px var(--color-box-shadow-light);
	}

	pre {
		background-color: var(--color-background-dark);
		color: var(--color-text-light);

		code {
			background: transparent;
			color: var(--color-text-light);
			border: none;
		}
	}

	code {
		background-color: var(--color-background-light);
		color: var(--color-text-bad);
		border-color: var(--color-border-light);
	}

	blockquote {
		margin: 0;
		padding: 5px 20px;
		background-color: var(--color-background-light);
		display: block;
		color: var(--color-text-light-darker);
		border-top: 1px solid var(--color-border-light-darker);
		border-bottom: 1px solid var(--color-border-light-darker);

		p {
			margin: 0;
		}
	}

	> h1.title > a {
		color: var(--color_text);
	}
}

.box {
	border: 1px solid var(--color-border-light-darker);

	.box-title {
		margin: 0;
		background-color: var(--color-background-aside);
		color: var(--color-text-light);
		border-bottom: 1px solid var(--color-border-light-darker);

		a {
			color: var(--color-text-light);
		}

		.configure {
			margin-right: 4px;

			.icon {
				&:hover {
					filter: invert(56%) sepia(87%) saturate(1185%) hue-rotate(327deg) brightness(104%) contrast(96%);
				}
			}
		}
	}

	.box-content {
		max-height: 260px;

		.item {
			padding: 0 10px;
			font-size: 0.9rem;

			.configure {
				.icon {
					vertical-align: middle;
					filter: brightness(0.4);

					&:hover {
						filter: invert(56%) sepia(87%) saturate(1185%) hue-rotate(327deg) brightness(104%) contrast(96%);
					}
				}
			}
		}
	}

	&.category {
		.box-title .title {
			font-weight: normal;
			text-decoration: none;
			text-align: left;
		}

		&:not([data-unread="0"]) .box-title {
			background-color: var(--color-background-nav);

			&:active {
				background: var(--color-background-nav-darker);
			}

			.title {
				font-weight: bold;
				color: var(--color-text-light);
			}
		}

		.title:not([data-unread="0"])::after {
			background: none;
			border: 0;
			box-shadow: none;
			position: absolute;
			top: 5px;
			right: 10px;
			font-weight: bold;
			text-shadow: none;
		}
	}

	&.visible-semi {
		border-style: solid;
	}
}

.tree {
	margin: 10px 0;
}

.aside_feed .tree-folder-title {
	padding: 0.3rem 0.75rem;
	background-color: var(--color-background-aside);
	font-size: 1rem;
	position: relative;

	.title {
		background: inherit;
		color: var(--color-text-light);

		&:hover {
			text-decoration: none;
		}
	}
}

.tree-folder-items {
	background-color: var(--color-background-aside);

	> .item {
		font-size: 0.8rem;

		&.active {
			background-color: var(--color-background-nav-darker);
		}

		> a {
			text-decoration: none;
			color: var(--color-text-light);
		}
	}
}

.header {
	height: auto;

	> .item {
		padding: 0;
		vertical-align: middle;

		&.title {

			@extend %aside-width;
			position: absolute;
			text-align: center;

			a {
				padding: 0 1rem;

				&:hover {
					.logo {
						filter: grayscale(100%) brightness(100) opacity(90%);
					}
				}
			}

			.logo {
				display: inline-block;
				height: 26px;
				vertical-align: top;
				position: relative;
				top: 5px;
				filter: grayscale(100%) brightness(100);
			}
		}
	}

	.item.search {
		display: none;
	}

	.item.configure {
		position: fixed;
		right: 0;
		z-index: 95;
		width: 35px;
		text-align: center;
		line-height: 0.8;

		> .icon {
			filter: brightness(3);
			margin-right: 5px;
			margin-top: 3px;
		}

		.dropdown .dropdown-menu {
			max-height: calc(100vh - 45px);
			overflow: auto;

			.icon {
				filter: brightness(3);
			}
		}
	}
}

.aside {
	padding: 35px 0;
	background-color: var(--color-background-aside);

	@extend %aside-width;

	&.aside_feed {
		.tree {
			margin: 0 0 50px;
		}

		.tree-folder {
			.tree-folder-title,
			.item.feed {
				&:hover {
					background-color: var(--color-background-nav-darker);
				}
			}
		}

		.nav-form {
			input,
			select {
				width: 140px;
			}

			.dropdown {
				.dropdown-menu {
					right: -20px;
				}

				.dropdown-menu::after {
					right: 33px;
				}
			}
		}
	}
}

.aside_feed {
	.category .title:not([data-unread="0"])::after {
		margin: 0.5rem 0 0 0;
		background-color: var(--color-background-nav-darker);
		color: var(--color-text-light);
	}

	.feed .item-title:not([data-unread="0"])::after {
		background-color: var(--color-background-nav);
		color: var(--color-text-light);
	}

	.tree-folder-items .dropdown-menu::after {
		left: 2px;
	}

	.about {
		padding: 1rem;
		display: block;
		text-align: center;
		font-size: 0.8em;
		font-style: italic;
	}
}

.reader {
	.aside {
		.toggle_aside {
			background-color: var(--color-background-aside);

			.icon {
				filter: brightness(3);
			}

			&:hover {
				background-color: var(--color-background-nav);
			}
		}
	}
}

.post {
	font-size: 0.9em;

	input {
		&.long {
			height: 33px;
			margin-top: 0px;
		}
	}
}

.prompt {
	input {
		margin: 5px auto;
	}
}

#global {
	height: 100vh;
}

#new-article {
	background-color: var(--color-background-nav);
	font-size: 1em;
	text-align: center;
	position: fixed;
	bottom: 48px;
	z-index: 900;
	left: 0;
	line-height: 1.5;

	@extend %aside-width;

	> a {
		padding: 1rem;
		line-height: 1.5;
		font-weight: bold;
		color: var(--color-text-light);

		&:hover {
			text-decoration: none;
			background-color: var(--color-background-nav-darker);
		}
	}
}

.day {
	padding: 0 10px;
	font-weight: bold;
	line-height: 3;
	text-align: center;

	.name {
		display: none;
	}
}

.nav {
	a,
	.as-link {
		color: var(--color-text-light);
	}
}


.nav_menu {
	padding: 0;
	width: 100%;
	font-size: 0;
	background-color: var(--color-background-nav);
	text-align: left;
	position: sticky;
	top: 0;
	z-index: 90;

	.item.search {
		display: inline-block;
		position: absolute;
		right: 40px;

		input {
			border-width: 0;
			line-height: 1.95;
		}
	}

	#toggle-unread,
	#toggle-starred {
		.icon {
			filter: brightness(1);
		}
	}
}

.flux {
	padding-right: 10px;
	background-color: var(--color-background-light);

	&::after {

		@extend %after;
		margin: 0 auto;
		width: 90%;
		border-top: 1px solid var(--color-border-light-darker);
	}

	.flux_header:hover,
	.current {
		background-color: var(--color-background-hover);

		&:not(.current):hover .item .title {
			background-color: var(--color-background-hover);
		}
	}

	&.favorite:not(.current) {
		background-color: var(--color-background-stared);

		&:hover .item .title {
			background-color: var(--color-background-stared);
		}
	}

	&.not_read:not(.current) {
		background-color: var(--color-background-unread);

		&:hover .item .title {
			background-color: var(--color-background-unread);
		}
	}

	.flux_header .date,
	.flux_content .bottom .date {
		color: var(--color-text-light-darker);
	}

	.bottom {
		font-size: 0.8rem;
		text-align: center;
	}

	label {
		color: var(--color-text-light);
		cursor: pointer;
	}
}

.flux_header {
	font-size: 0.8rem;
	cursor: pointer;

	.title {
		font-size: 0.9rem;
	}
}

.notification {
	padding: 10px 0;
	background-color: var(--color-background-light-darker);
	height: auto;
	color: var(--color-text-light-darker);
	font-size: 1em;
	border: none;
	text-align: center;
	font-weight: bold;
	vertical-align: middle;
	position: fixed;
	bottom: 48px;
	left: 0;
	top: auto;

	@extend %aside-width;

	&.good,
	&.bad {
		color: var(--color-text-light);
	}

	&.good {
		background-color: var(--color-background-good);

		a.close:hover {
			background-color: var(--color-background-good);
		}
	}

	&.bad {
		background-color: var(--color-background-bad);

		a.close:hover {
			background-color: var(--color-background-bad);
		}
	}

	a.close {
		display: none;
	}
}

#bigMarkAsRead.big {
	text-align: center;
	text-decoration: none;
	background: var(--color-background-light-darker);

	&:hover {
		background-color: var(--color-background-aside);
		color: var(--color-text-light);
	}
}

#nav_entries {

	@extend %aside-width;
	background-color: var(--color-background-aside);
}

.stat {
	margin: 10px 0 20px;

	th,
	td,
	tr {
		border: none;
	}

	> table {
		td,
		th {
			border-bottom: 1px solid var(--color-border-light-darker);
		}
	}
}

#overlay {
	z-index: 100;

	.close .icon {
		filter: brightness(3);
	}
}

#panel {
	z-index: 100;

	.nav_menu {
		position: relative;
	}
}

.formLogin,
.register {
	#global {
		height: 0;
	}

	.header {
		background-color: var(--color-background-nav);
		height: 35px;
		position: relative;

		> .item {
			&.configure {
				padding: 8px;
				width: auto;
				position: absolute;
				right: 0;
				white-space: nowrap;
				bottom: 0;

				.icon {
					filter: brightness(3);
				}
			}
		}
	}
}

a.signin {
	color: var(--color-text-light);
	font-size: 70%;
}

.log-item {
	&.log-error {
		background-color: var(--color-background-bad);
		color: var(--color-text-light);
	}

	&.log-warning {
		background-color: var(--color-background-alert);
		color: var(--color-text-light);
	}

	&.log-debug {
		background: var(--color-background-dark);
	}
}

@media (max-width: 840px) {
	body:not(.formLogin, .register) {
		.header {
			.item .title {
				display: none;
			}
		}
	}

	.form-group .group-name {
		padding-bottom: 0;
		text-align: left;
	}

	.dropdown {
		position: relative;

		.dropdown-menu {
			width: auto;
		}
	}

	#new-article {
		margin-top: 2rem;
		width: 100%;
	}

	.header {
		display: table;

		.item {
			padding: 0;
		}

		.item.configure {
			padding: 0;
			position: fixed;
			right: 76px;

			> .icon {
				margin-top: 5px;
			}
		}
	}

	button.read_all.btn {
		display: none;
	}

	.flux .item.manage,
	.flux_header .item.website {
		width: 35px;
		text-align: center;
	}

	.flux:not(.current):hover .item .title {
		top: auto !important;
	}

	.aside {
		padding: 0;
		width: 0;
		transition: width 200ms linear;

		.toggle_aside {
			background-color: var(--color-background-aside);

			&:hover {
				background-color: var(--color-background-nav);
			}

			.icon {
				filter: brightness(3);
			}
		}

		&.aside_feed .configure-feeds {
			display: flex;
			margin-top: 0;
			margin-left: auto;
			margin-right: auto;
		}

		&:target {
			width: 78%;
			z-index: 1000;
		}
	}

	#slider {
		.toggle_aside {
			background-color: var(--color-background-aside);

			&:hover {
				background-color: var(--color-background-nav);
			}

			.icon {
				filter: brightness(3);
			}
		}
	}

	.nav_menu {
		height: 71px;

		.btn {
			margin: 0;
		}

		.stick,
		.group {
			margin: 0;

			.btn {
				margin: 0;
			}
		}

		.item.search {
			top: 3px;
			margin-left: 77px;
			width: calc(100% - 4 * 38px);
			position: relative;

			form {
				display: block;
			}

			input {
				width: 100%;
			}
		}
	}

	#overlay .close,
	.dropdown-menu .toggle_aside {
		background-color: var(--color-background-aside);
		display: block;
		height: 50px;
		line-height: 3.5;
		text-align: center;
		padding-right: 10px;

		&:hover {
			background-color: var(--color-background-nav);
		}
	}

	.dropdown-target:target ~ .dropdown-toggle::after,
	.dropdown-target:target ~ .dropdown-toggle.btn::after {
		display: none;
	}

	.share .dropdown-menu {
		right: 3%;
		left: auto;
	}

	.day .name {
		font-size: 1.1rem;
	}

	.notification {
		width: 100%;

		a.close {
			background: transparent;
			display: block;
			left: 0;

			&:hover {
				opacity: 0.5;
			}

			.icon {
				display: none;
			}
		}
	}

	#nav_entries {
		width: 100% !important;
	}

	.post {
		padding-left: 15px;
		padding-right: 15px;
	}

	div#stream {
		margin-top: 0px;
	}

	a.btn.toggle_aside {
		position: absolute;
		top: 0;
	}

	form#mark-read-menu,
	a#actualize,
	a#toggle-order,
	div#nav_menu_actions,
	div#nav_menu_views {
		position: absolute;
	}

	form#mark-read-menu {
		right: 38px;
		top: 0;
	}

	a#actualize,
	a#toggle-order {
		right: 0;
	}

	a#actualize {
		top: 0;
	}

	a#toggle-order,
	div#nav_menu_actions,
	div#nav_menu_views {
		top: 36px;
	}

	div#nav_menu_actions {
		left: 0px;
	}

	div#nav_menu_views {
		right: 50px;
	}
}

@media (max-width: 410px) {
	.nav_menu .stick {
		margin: 0;
	}
}

@media (max-width: 374px) {
	#nav_menu_views {
		display: none;
	}
}

button.as-link {
	outline: none;
}

.dropdown-target:target ~ .btn.dropdown-toggle {
	background-color: var(--color-background-nav-darker);
}

.tree-folder.active .tree-folder-title {
	background-color: var(--color-background-nav-darker);
	font-weight: bold;
}

.feed.item {
	&.empty {
		color: var(--color-text-alert);

		&.active {
			background-color: var(--color-background-alert);
			color: var(--color-text-light);

			> a {
				color: var(--color-text-light);
			}
		}

		> a {
			color: var(--color-text-alert);
		}
	}

	&.error {
		color: var(--color-text-bad-lighter);

		&.active {
			background-color: var(--color-background-bad);
			color: var(--color-text-light);

			> a {
				color: var(--color-text-light);
			}
		}

		> a {
			color: var(--color-text-bad-lighter);
		}
	}
}

#stream.reader .flux {
	background-color: var(--color-background-light);
	color: var(--color-text-aside);
	border: none;

	&::after {
		border: none;
	}

	.flux_content {
		border-color: var(--color-border-grey);
	}

	.author {
		color: var(--color-text-light-darker);
	}
}

#nav_menu_actions {
	ul.dropdown-menu {
		left: 0;
		right: auto;

		&::after {
			display: none;
		}
	}

	.dropdown.only-mobile {
		display: initial !important;
	}
}

#nav_menu_read_all {
	ul.dropdown-menu {
		right: 0;
		left: auto;

		&::after {
			display: none;
		}
	}
}

#slider {
	label {
		min-height: initial;
	}
}
