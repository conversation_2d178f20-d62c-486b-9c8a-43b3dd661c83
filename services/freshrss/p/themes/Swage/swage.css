:root {
	--color-text-light: #fcfcfc;
	--color-text-light-darker: #969696;
	--color-text-dark: #181621;
	--color-text-nav: rgb(0, 71.6947368421, 139);
	--color-text-aside: #22303d;
	--color-text-alert: #fa8052;
	--color-text-good: #5eaabf;
	--color-text-bad: #b0425b;
	--color-text-bad-lighter: rgb(195.8181818182, 97.1818181818, 119.5991735537);
	--color-text-shadow-light: #fcfcfc;
	--color-box-shadow-light: rgb(226.5, 226.5, 226.5);
	--color-border-light: #fcfcfc;
	--color-border-light-darker: rgb(226.5, 226.5, 226.5);
	--color-border-grey: #e3e3e3;
	--color-border-nav: rgb(0, 71.6947368421, 139);
	--color-border-bad: #b0425b;
	--color-background-light: #fcfcfc;
	--color-background-light-darker: rgb(226.5, 226.5, 226.5);
	--color-background-light-darker-transparent: rgba(217, 217, 217, 0.7333333333);
	--color-background-dark: #181621;
	--color-background-nav: #0062be;
	--color-background-nav-darker: rgb(0, 71.6947368421, 139);
	--color-background-aside: #22303d;
	--color-background-alert: #fa8052;
	--color-background-alert-darker: rgb(248.5674157303, 91.6123595506, 32.4325842697);
	--color-background-good: #5eaabf;
	--color-background-bad: rgb(195.8181818182, 97.1818181818, 119.5991735537);
	--color-background-stared: #fff6da;
	--color-background-unread: #fff3ed;
	--color-background-hover: #fff;
	--frss-scrollbar-handle: rgba(0, 0, 0, 0.1);
	--frss-scrollbar-handle-hover: rgba(0, 0, 0, 0.4);
	--frss-scrollbar-track: rgba(0, 0, 0, 0.05);
	--frss-scrollbar-track-hover: rgba(0, 0, 0, 0.1);
}

input,
select, textarea {
	min-height: 25px;
	margin-top: 4px;
	line-height: 2.25;
	vertical-align: middle;
	background-color: var(--color-background-light);
	border: 2px solid var(--color-border-grey);
	padding-left: 8px;
}

input:invalid,
select:invalid {
	padding-left: 5px;
	color: var(--color-text-bad);
	border-left-color: var(--color-border-bad);
	border-left-width: 5px;
	box-shadow: none;
}

.nav-list .item .nav-header, .nav-list .item {
	min-height: 2.5em;
	line-height: 2.5;
}

.dropdown-menu > .item a,
.dropdown-menu > .item > span,
.dropdown-menu > .item > .as-link,
.dropdown-menu > .item button, .dropdown-menu > .item {
	padding: 0 22px;
	color: var(--color-text-light);
	font-size: 0.8rem;
	line-height: 2.5;
}

.flux::after, .form-group::after {
	display: block;
	clear: both;
}

#nav_entries, .notification, #new-article, .aside, .header > .item.title, .stick.configure-feeds {
	width: 231px;
}

html,
body {
	color: var(--color-text-dark);
	font-family: Helvetica, Arial, sans-serif;
}

a {
	color: var(--color-text-nav);
	outline: none;
}
a#btn-subscription {
	width: 76%;
}
a#btn-add {
	width: 5%;
}

img.icon:hover {
	background: none;
}

sup {
	top: -0.3em;
}

legend {
	margin: 20px 0 5px;
	padding: 5px 20px;
	background-color: var(--color-background-aside);
	display: inline-block;
	width: auto;
	color: var(--color-text-light);
	font-size: 1.4em;
	clear: both;
}

label {
	min-height: 25px;
}

textarea {
	width: 360px;
	height: 100px;
}
textarea:focus {
	border-color: var(--color-border-nav);
}

input:focus,
select:focus {
	border-color: var(--color-border-nav);
}
input:disabled,
select:disabled {
	background-color: var(--color-background-light);
}

select {
	padding-top: 7px;
	padding-bottom: 8px;
}

option {
	padding: 0 0.5em;
}

table {
	border-collapse: collapse;
}

td,
th {
	border: 1px solid var(--color-border-light-darker);
}

th {
	background-color: var(--color-background-light);
}

form td,
form th {
	font-weight: normal;
	text-align: center;
}

.category .title.error::before {
	display: inline-block;
	padding-right: 7px;
	width: 16px;
	content: url(../Swage/icons/error.svg);
}

.form-group {
	padding: 5px;
	border: 1px solid transparent;
}
.form-group:hover {
	background-color: var(--color-background-light);
	border: 1px solid var(--color-border-light);
}
.form-group.form-actions {
	margin: 15px 0 25px;
	padding: 5px 0;
	background-color: var(--color-background-light-darker-transparent);
	border-top: 3px solid var(--color-border-light-darker);
}
.form-group.form-actions .btn {
	margin: 0 10px;
}
.form-group .group-name {
	padding: 10px 0;
	text-align: right;
}
.form-group .group-controls {
	min-height: 25px;
	padding: 5px 0;
}
.form-group .group-controls .control {
	line-height: 2;
}

.stick select {
	margin-top: 0;
}
.btn {
	margin: 0;
	padding: 0.25rem 0.5rem;
	background-color: var(--color-background-nav);
	color: var(--color-text-light);
	font-size: 0.9rem;
	border: none;
	min-height: 25px;
	min-width: 15px;
	vertical-align: middle;
	line-height: 1.5;
	text-decoration: none;
}
.btn.active, .btn:active, .btn:hover {
	background-color: var(--color-background-nav-darker);
	text-decoration: none;
}
.btn .icon {
	filter: brightness(3);
}

.btn-important, .btn-attention {
	font-weight: normal;
	background-color: var(--color-background-alert);
	color: var(--color-text-light);
}
.btn-important:hover,
.btn-important :active, .btn-attention:hover,
.btn-attention :active {
	background-color: var(--color-background-alert-darker) !important;
}

.manage-list .configure .icon {
	filter: brightness(0.4);
	vertical-align: sub;
}
.manage-list .configure:hover {
	filter: invert(56%) sepia(87%) saturate(1185%) hue-rotate(327deg) brightness(104%) contrast(96%);
}

.switch.active {
	background-color: var(--color-background-nav);
}
.switch.active:hover {
	background-image: url("./icons/disabled-light.svg");
}

.nav-list {
	font-size: 0.9rem;
}
.nav-list .item .nav-header {
	padding: 0 1rem;
	font-weight: bold;
	background-color: var(--color-background-aside);
	color: var(--color-text-light);
	cursor: default;
}
.nav-list .item a:hover,
.nav-list .item .as-link:hover {
	background-color: var(--color-background-nav-darker);
	color: var(--color-text-light);
}
.nav-list .item.active {
	background: var(--color-background-nav-darker);
	color: var(--color-text-light);
}
.nav-list .item.active a,
.nav-list .item.active .as-link {
	color: var(--color-text-light);
}
.nav-list .item > a,
.nav-list .item > .as-link {
	padding: 0 1.5rem;
}
.nav-list .item .icon {
	filter: brightness(3);
}
.nav-list .nav-form {
	padding: 3px;
	text-align: center;
}
.nav-list a:hover,
.nav-list .as-link:hover {
	text-decoration: none;
}

.dropdown-menu {
	padding: 0.5rem 0 1rem 0;
	font-size: 0.8rem;
	text-align: left;
	border: none;
	background-color: var(--color-background-nav-darker);
}
.dropdown-menu .dropdown-header {
	cursor: default;
	padding: 0.5rem 10px 0.5rem 10px;
	font-weight: bold;
	color: var(--color-text-light);
}
.dropdown-menu .dropdown-header a,
.dropdown-menu .dropdown-header .as-link {
	padding: 0 5px;
	position: absolute;
	right: 5px;
}
.dropdown-menu .dropdown-header a:hover,
.dropdown-menu .dropdown-header .as-link:hover {
	background-color: var(--color-background-nav);
}
.dropdown-menu .dropdown-section .dropdown-section-title {
	cursor: default;
	padding: 0.25rem 0.5rem 0.125rem 0.25rem;
	font-weight: bold;
	color: var(--color-text-light);
}
.dropdown-menu .dropdown-section .item a,
.dropdown-menu .dropdown-section .item .as-link {
	padding: 0 22px;
}
.dropdown-menu .dropdown-section .item a:hover,
.dropdown-menu .dropdown-section .item .as-link:hover {
	background-color: var(--color-background-nav);
}
.dropdown-menu > .item {
	padding: 0 0 0 0.5rem;
}
.dropdown-menu > .item > a {
	min-width: initial;
	white-space: nowrap;
}
.dropdown-menu > .item > a:hover,
.dropdown-menu > .item > button:hover {
	background-color: var(--color-background-nav);
	color: var(--color-text-light);
}
.dropdown-menu > .item[aria-checked=true] > a::before {
	font-weight: bold;
	margin: 0 0 0 -14px;
}
.dropdown-menu .help a {
	color: var(--color-text-light);
	text-decoration: underline;
	text-decoration-style: dotted;
}
.dropdown-menu .help a:hover {
	text-decoration-style: solid;
}
.dropdown-menu .input select,
.dropdown-menu .input input {
	margin: 0 auto 5px;
	padding: 2px 5px;
}

#dropdown-search-wrapper .dropdown-menu {
	padding-top: 1rem;
	padding-bottom: 0.25rem;
}
#dropdown-search-wrapper .dropdown-menu .stick.search {
	width: 100%;
}
#dropdown-search-wrapper .dropdown-menu .stick.search input[type=search] {
	width: 100%;
	border: 0;
}

.labels .dropdown-menu,
.tags .dropdown-menu,
.share .dropdown-menu {
	right: auto;
}

.item ~ .dropdown-header,
.item.separator {
	border-top-color: var(--color-border-light-darker);
	cursor: default;
}

.alert {
	margin: 0.25rem auto;
	background-color: var(--color-background-light);
	color: var(--color-text-light-darker);
	font-size: 0.9em;
	border: none;
	text-shadow: 0 0 1px var(--color-text-shadow-light);
}
.alert > a {
	color: inherit;
	text-decoration: underline;
}

.alert-head {
	font-size: 1.15em;
}

.alert-warn,
.alert-success,
.alert-error {
	border: none;
}

.alert-warn {
	background-color: var(--color-background-light);
	color: var(--color-text-alert);
}

.alert-success {
	background-color: var(--color-background-light);
	color: var(--color-text-good);
}

.alert-error {
	background-color: var(--color-background-light);
	color: var(--color-text-bad);
}

.pagination {
	background: var(--color-background-light-darker);
	color: var(--color_text);
}
.pagination .item a {
	color: var(--color_text);
}

#load_more.loading,
#load_more.loading:hover {
	background: url(loader.gif) center center no-repeat var(--color-background-aside);
}

.content {
	padding: 20px 10px;
}
.content hr {
	margin: 30px 10px;
	background: var(--color-background-light-darker);
	height: 1px;
	border: 0;
	box-shadow: 0 2px 5px var(--color-box-shadow-light);
}
.content pre {
	background-color: var(--color-background-dark);
	color: var(--color-text-light);
}
.content pre code {
	background: transparent;
	color: var(--color-text-light);
	border: none;
}
.content code {
	background-color: var(--color-background-light);
	color: var(--color-text-bad);
	border-color: var(--color-border-light);
}
.content blockquote {
	margin: 0;
	padding: 5px 20px;
	background-color: var(--color-background-light);
	display: block;
	color: var(--color-text-light-darker);
	border-top: 1px solid var(--color-border-light-darker);
	border-bottom: 1px solid var(--color-border-light-darker);
}
.content blockquote p {
	margin: 0;
}
.content > h1.title > a {
	color: var(--color_text);
}

.box {
	border: 1px solid var(--color-border-light-darker);
}
.box .box-title {
	margin: 0;
	background-color: var(--color-background-aside);
	color: var(--color-text-light);
	border-bottom: 1px solid var(--color-border-light-darker);
}
.box .box-title a {
	color: var(--color-text-light);
}
.box .box-title .configure {
	margin-right: 4px;
}
.box .box-title .configure .icon:hover {
	filter: invert(56%) sepia(87%) saturate(1185%) hue-rotate(327deg) brightness(104%) contrast(96%);
}
.box .box-content {
	max-height: 260px;
}
.box .box-content .item {
	padding: 0 10px;
	font-size: 0.9rem;
}
.box .box-content .item .configure .icon {
	vertical-align: middle;
	filter: brightness(0.4);
}
.box .box-content .item .configure .icon:hover {
	filter: invert(56%) sepia(87%) saturate(1185%) hue-rotate(327deg) brightness(104%) contrast(96%);
}
.box.category .box-title .title {
	font-weight: normal;
	text-decoration: none;
	text-align: left;
}
.box.category:not([data-unread="0"]) .box-title {
	background-color: var(--color-background-nav);
}
.box.category:not([data-unread="0"]) .box-title:active {
	background: var(--color-background-nav-darker);
}
.box.category:not([data-unread="0"]) .box-title .title {
	font-weight: bold;
	color: var(--color-text-light);
}
.box.category .title:not([data-unread="0"])::after {
	background: none;
	border: 0;
	box-shadow: none;
	position: absolute;
	top: 5px;
	right: 10px;
	font-weight: bold;
	text-shadow: none;
}
.box.visible-semi {
	border-style: solid;
}

.tree {
	margin: 10px 0;
}

.aside_feed .tree-folder-title {
	padding: 0.3rem 0.75rem;
	background-color: var(--color-background-aside);
	font-size: 1rem;
	position: relative;
}
.aside_feed .tree-folder-title .title {
	background: inherit;
	color: var(--color-text-light);
}
.aside_feed .tree-folder-title .title:hover {
	text-decoration: none;
}

.tree-folder-items {
	background-color: var(--color-background-aside);
}
.tree-folder-items > .item {
	font-size: 0.8rem;
}
.tree-folder-items > .item.active {
	background-color: var(--color-background-nav-darker);
}
.tree-folder-items > .item > a {
	text-decoration: none;
	color: var(--color-text-light);
}

.header {
	height: auto;
}
.header > .item {
	padding: 0;
	vertical-align: middle;
}
.header > .item.title {
	position: absolute;
	text-align: center;
}
.header > .item.title a {
	padding: 0 1rem;
}
.header > .item.title a:hover .logo {
	filter: grayscale(100%) brightness(100) opacity(90%);
}
.header > .item.title .logo {
	display: inline-block;
	height: 26px;
	vertical-align: top;
	position: relative;
	top: 5px;
	filter: grayscale(100%) brightness(100);
}
.header .item.search {
	display: none;
}
.header .item.configure {
	position: fixed;
	right: 0;
	z-index: 95;
	width: 35px;
	text-align: center;
	line-height: 0.8;
}
.header .item.configure > .icon {
	filter: brightness(3);
	margin-right: 5px;
	margin-top: 3px;
}
.header .item.configure .dropdown .dropdown-menu {
	max-height: calc(100vh - 45px);
	overflow: auto;
}
.header .item.configure .dropdown .dropdown-menu .icon {
	filter: brightness(3);
}

.aside {
	padding: 35px 0;
	background-color: var(--color-background-aside);
}
.aside.aside_feed .tree {
	margin: 0 0 50px;
}
.aside.aside_feed .tree-folder .tree-folder-title:hover,
.aside.aside_feed .tree-folder .item.feed:hover {
	background-color: var(--color-background-nav-darker);
}
.aside.aside_feed .nav-form input,
.aside.aside_feed .nav-form select {
	width: 140px;
}
.aside.aside_feed .nav-form .dropdown .dropdown-menu {
	right: -20px;
}
.aside.aside_feed .nav-form .dropdown .dropdown-menu::after {
	right: 33px;
}

.aside_feed .category .title:not([data-unread="0"])::after {
	margin: 0.5rem 0 0 0;
	background-color: var(--color-background-nav-darker);
	color: var(--color-text-light);
}
.aside_feed .feed .item-title:not([data-unread="0"])::after {
	background-color: var(--color-background-nav);
	color: var(--color-text-light);
}
.aside_feed .tree-folder-items .dropdown-menu::after {
	left: 2px;
}
.aside_feed .about {
	padding: 1rem;
	display: block;
	text-align: center;
	font-size: 0.8em;
	font-style: italic;
}

.reader .aside .toggle_aside {
	background-color: var(--color-background-aside);
}
.reader .aside .toggle_aside .icon {
	filter: brightness(3);
}
.reader .aside .toggle_aside:hover {
	background-color: var(--color-background-nav);
}

.post {
	font-size: 0.9em;
}
.post input.long {
	height: 33px;
	margin-top: 0px;
}

.prompt input {
	margin: 5px auto;
}

#global {
	height: 100vh;
}

#new-article {
	background-color: var(--color-background-nav);
	font-size: 1em;
	text-align: center;
	position: fixed;
	bottom: 48px;
	z-index: 900;
	left: 0;
	line-height: 1.5;
}
#new-article > a {
	padding: 1rem;
	line-height: 1.5;
	font-weight: bold;
	color: var(--color-text-light);
}
#new-article > a:hover {
	text-decoration: none;
	background-color: var(--color-background-nav-darker);
}

.day {
	padding: 0 10px;
	font-weight: bold;
	line-height: 3;
	text-align: center;
}
.day .name {
	display: none;
}

.nav a,
.nav .as-link {
	color: var(--color-text-light);
}

.nav_menu {
	padding: 0;
	width: 100%;
	font-size: 0;
	background-color: var(--color-background-nav);
	text-align: left;
	position: sticky;
	top: 0;
	z-index: 90;
}
.nav_menu .item.search {
	display: inline-block;
	position: absolute;
	right: 40px;
}
.nav_menu .item.search input {
	border-width: 0;
	line-height: 1.95;
}
.nav_menu #toggle-unread .icon,
.nav_menu #toggle-starred .icon {
	filter: brightness(1);
}

.flux {
	padding-right: 10px;
	background-color: var(--color-background-light);
}
.flux::after {
	margin: 0 auto;
	width: 90%;
	border-top: 1px solid var(--color-border-light-darker);
}
.flux .flux_header:hover,
.flux .current {
	background-color: var(--color-background-hover);
}
.flux .flux_header:hover:not(.current):hover .item .title,
.flux .current:not(.current):hover .item .title {
	background-color: var(--color-background-hover);
}
.flux.favorite:not(.current) {
	background-color: var(--color-background-stared);
}
.flux.favorite:not(.current):hover .item .title {
	background-color: var(--color-background-stared);
}
.flux.not_read:not(.current) {
	background-color: var(--color-background-unread);
}
.flux.not_read:not(.current):hover .item .title {
	background-color: var(--color-background-unread);
}
.flux .flux_header .date,
.flux .flux_content .bottom .date {
	color: var(--color-text-light-darker);
}
.flux .bottom {
	font-size: 0.8rem;
	text-align: center;
}
.flux label {
	color: var(--color-text-light);
	cursor: pointer;
}

.flux_header {
	font-size: 0.8rem;
	cursor: pointer;
}
.flux_header .title {
	font-size: 0.9rem;
}

.notification {
	padding: 10px 0;
	background-color: var(--color-background-light-darker);
	height: auto;
	color: var(--color-text-light-darker);
	font-size: 1em;
	border: none;
	text-align: center;
	font-weight: bold;
	vertical-align: middle;
	position: fixed;
	bottom: 48px;
	left: 0;
	top: auto;
}
.notification.good, .notification.bad {
	color: var(--color-text-light);
}
.notification.good {
	background-color: var(--color-background-good);
}
.notification.good a.close:hover {
	background-color: var(--color-background-good);
}
.notification.bad {
	background-color: var(--color-background-bad);
}
.notification.bad a.close:hover {
	background-color: var(--color-background-bad);
}
.notification a.close {
	display: none;
}

#bigMarkAsRead.big {
	text-align: center;
	text-decoration: none;
	background: var(--color-background-light-darker);
}
#bigMarkAsRead.big:hover {
	background-color: var(--color-background-aside);
	color: var(--color-text-light);
}

#nav_entries {
	background-color: var(--color-background-aside);
}

.stat {
	margin: 10px 0 20px;
}
.stat th,
.stat td,
.stat tr {
	border: none;
}
.stat > table td,
.stat > table th {
	border-bottom: 1px solid var(--color-border-light-darker);
}

#overlay {
	z-index: 100;
}
#overlay .close .icon {
	filter: brightness(3);
}

#panel {
	z-index: 100;
}
#panel .nav_menu {
	position: relative;
}

.formLogin #global,
.register #global {
	height: 0;
}
.formLogin .header,
.register .header {
	background-color: var(--color-background-nav);
	height: 35px;
	position: relative;
}
.formLogin .header > .item.configure,
.register .header > .item.configure {
	padding: 8px;
	width: auto;
	position: absolute;
	right: 0;
	white-space: nowrap;
	bottom: 0;
}
.formLogin .header > .item.configure .icon,
.register .header > .item.configure .icon {
	filter: brightness(3);
}

a.signin {
	color: var(--color-text-light);
	font-size: 70%;
}

.log-item.log-error {
	background-color: var(--color-background-bad);
	color: var(--color-text-light);
}
.log-item.log-warning {
	background-color: var(--color-background-alert);
	color: var(--color-text-light);
}
.log-item.log-debug {
	background: var(--color-background-dark);
}

@media (max-width: 840px) {
	body:not(.formLogin, .register) .header .item .title {
		display: none;
	}
	.form-group .group-name {
		padding-bottom: 0;
		text-align: left;
	}
	.dropdown {
		position: relative;
	}
	.dropdown .dropdown-menu {
		width: auto;
	}
	#new-article {
		margin-top: 2rem;
		width: 100%;
	}
	.header {
		display: table;
	}
	.header .item {
		padding: 0;
	}
	.header .item.configure {
		padding: 0;
		position: fixed;
		right: 76px;
	}
	.header .item.configure > .icon {
		margin-top: 5px;
	}
	button.read_all.btn {
		display: none;
	}
	.flux .item.manage,
	.flux_header .item.website {
		width: 35px;
		text-align: center;
	}
	.flux:not(.current):hover .item .title {
		top: auto !important;
	}
	.aside {
		padding: 0;
		width: 0;
		transition: width 200ms linear;
	}
	.aside .toggle_aside {
		background-color: var(--color-background-aside);
	}
	.aside .toggle_aside:hover {
		background-color: var(--color-background-nav);
	}
	.aside .toggle_aside .icon {
		filter: brightness(3);
	}
	.aside.aside_feed .configure-feeds {
		display: flex;
		margin-top: 0;
		margin-left: auto;
		margin-right: auto;
	}
	.aside:target {
		width: 78%;
		z-index: 1000;
	}
	#slider .toggle_aside {
		background-color: var(--color-background-aside);
	}
	#slider .toggle_aside:hover {
		background-color: var(--color-background-nav);
	}
	#slider .toggle_aside .icon {
		filter: brightness(3);
	}
	.nav_menu {
		height: 71px;
	}
	.nav_menu .btn {
		margin: 0;
	}
	.nav_menu .stick,
	.nav_menu .group {
		margin: 0;
	}
	.nav_menu .stick .btn,
	.nav_menu .group .btn {
		margin: 0;
	}
	.nav_menu .item.search {
		top: 3px;
		margin-left: 77px;
		width: calc(100% - 152px);
		position: relative;
	}
	.nav_menu .item.search form {
		display: block;
	}
	.nav_menu .item.search input {
		width: 100%;
	}
	#overlay .close,
	.dropdown-menu .toggle_aside {
		background-color: var(--color-background-aside);
		display: block;
		height: 50px;
		line-height: 3.5;
		text-align: center;
		padding-right: 10px;
	}
	#overlay .close:hover,
	.dropdown-menu .toggle_aside:hover {
		background-color: var(--color-background-nav);
	}
	.dropdown-target:target ~ .dropdown-toggle::after,
	.dropdown-target:target ~ .dropdown-toggle.btn::after {
		display: none;
	}
	.share .dropdown-menu {
		right: 3%;
		left: auto;
	}
	.day .name {
		font-size: 1.1rem;
	}
	.notification {
		width: 100%;
	}
	.notification a.close {
		background: transparent;
		display: block;
		left: 0;
	}
	.notification a.close:hover {
		opacity: 0.5;
	}
	.notification a.close .icon {
		display: none;
	}
	#nav_entries {
		width: 100% !important;
	}
	.post {
		padding-left: 15px;
		padding-right: 15px;
	}
	div#stream {
		margin-top: 0px;
	}
	a.btn.toggle_aside {
		position: absolute;
		top: 0;
	}
	form#mark-read-menu,
	a#actualize,
	a#toggle-order,
	div#nav_menu_actions,
	div#nav_menu_views {
		position: absolute;
	}
	form#mark-read-menu {
		right: 38px;
		top: 0;
	}
	a#actualize,
	a#toggle-order {
		right: 0;
	}
	a#actualize {
		top: 0;
	}
	a#toggle-order,
	div#nav_menu_actions,
	div#nav_menu_views {
		top: 36px;
	}
	div#nav_menu_actions {
		left: 0px;
	}
	div#nav_menu_views {
		right: 50px;
	}
}
@media (max-width: 410px) {
	.nav_menu .stick {
		margin: 0;
	}
}
@media (max-width: 374px) {
	#nav_menu_views {
		display: none;
	}
}
button.as-link {
	outline: none;
}

.dropdown-target:target ~ .btn.dropdown-toggle {
	background-color: var(--color-background-nav-darker);
}

.tree-folder.active .tree-folder-title {
	background-color: var(--color-background-nav-darker);
	font-weight: bold;
}

.feed.item.empty {
	color: var(--color-text-alert);
}
.feed.item.empty.active {
	background-color: var(--color-background-alert);
	color: var(--color-text-light);
}
.feed.item.empty.active > a {
	color: var(--color-text-light);
}
.feed.item.empty > a {
	color: var(--color-text-alert);
}
.feed.item.error {
	color: var(--color-text-bad-lighter);
}
.feed.item.error.active {
	background-color: var(--color-background-bad);
	color: var(--color-text-light);
}
.feed.item.error.active > a {
	color: var(--color-text-light);
}
.feed.item.error > a {
	color: var(--color-text-bad-lighter);
}

#stream.reader .flux {
	background-color: var(--color-background-light);
	color: var(--color-text-aside);
	border: none;
}
#stream.reader .flux::after {
	border: none;
}
#stream.reader .flux .flux_content {
	border-color: var(--color-border-grey);
}
#stream.reader .flux .author {
	color: var(--color-text-light-darker);
}

#nav_menu_actions ul.dropdown-menu {
	left: 0;
	right: auto;
}
#nav_menu_actions ul.dropdown-menu::after {
	display: none;
}
#nav_menu_actions .dropdown.only-mobile {
	display: initial !important;
}

#nav_menu_read_all ul.dropdown-menu {
	right: 0;
	left: auto;
}
#nav_menu_read_all ul.dropdown-menu::after {
	display: none;
}

#slider label {
	min-height: initial;
}

/*# sourceMappingURL=swage.css.map */
