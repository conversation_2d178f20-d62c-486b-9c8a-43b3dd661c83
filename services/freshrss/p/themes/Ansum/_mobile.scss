@use "mixins";

@use "variables";

/*=== MOBILE */
/*===========*/

@media (max-width: 840px) {
	.aside {

		@include mixins.transition(all, 0.2s, ease-in-out);

		&.aside_feed {
			padding: 0;
		}

		.tree .tree-folder .tree-folder-items .item a {
			padding: 0.5rem 1rem;
		}
	}

	.aside .toggle_aside,
	#overlay .close,
	.dropdown-menu .toggle_aside,
	#slider .toggle_aside {
		background-color: variables.$main-first;

		&:hover {
			background-color: variables.$main-first-alt;
		}

		.icon {
			filter: grayscale(100%) brightness(2.5);
		}
	}

	.header {
		.item {
			&.search {
				display: none;
			}

			&.configure {
				position: absolute;
				top: 0;
				right: 0;
			}
		}
	}

	#global {
		height: calc(100% - 8rem);
	}

	#panel {
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
	}

	.post {
		padding-left: 1rem;
		padding-right: 1rem;
	}

	.nav_menu {
		.btn {
			margin: 0;
			padding: 0.85rem 1.25rem;
		}

		.stick,
		.group {
			margin: 0.5rem 0.5rem;

			.btn {
				margin: 0;
				padding: 0.85rem 1.25rem;

				&.read_all {
					padding: 0.85rem 1.25rem;
				}
			}
		}

		.search {
			.input {
				max-width: 97%;
				width: 90px;

				&:focus {
					width: 400px;
				}
			}
		}
	}

	#stream {
		.flux {
			.flux_header {
				padding: 0.5rem 0;
			}
		}
	}

	.dropdown-target:target {
		~ .dropdown-toggle::after {
			background-color: variables.$grey-lighter;
			border-top: 1px solid variables.$grey-light;
			border-left: 1px solid variables.$grey-light;
			right: 21px;
			bottom: -14px;
		}

		~ a.dropdown-toggle {
			&:not(.btn) {
				&::after {
					bottom: -17px;
				}
			}
		}
	}

	.day {
		text-align: center;
		padding: 1rem 0;

		.name {
			padding: 0;
			display: block;
			width: 100%;
			line-height: 1.5rem;
			margin-bottom: 1rem;
		}

	}

	#nav_entries button {
		height: 4.5rem;
	}

	.notification {
		border-radius: 0;

		a.close {
			background: transparent;
			display: block;
			left: 0;
		}

		a.close:hover {
			opacity: 0.5;
		}

		a.close .icon {
			display: none;

		}
	}
}
