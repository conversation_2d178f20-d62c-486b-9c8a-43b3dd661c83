@use "variables";

/*=== GLOBAL VIEW */
/*================*/
#stream {
	.box.category {
		&:not([data-unread="0"]) .box-title .title {
			font-weight: bold;
		}

		.box-title {
			padding: 1.5rem;
			background: none;

			a.title {
				color: variables.$grey-dark;
				font-size: 1rem;
				font-weight: normal;
				text-decoration: none;
				text-align: left;
				text-transform: uppercase;
				letter-spacing: 1px;

				&:not([data-unread="0"])::after {
					margin: -0.5rem 1rem 0 0;
					padding: 0 0.75rem;
					background: variables.$grey-light;
					border-radius: 12px;
					position: absolute;
					top: 1.75rem;
					right: 0;
					line-height: 1.5rem;
					text-align: center;
				}

				&:hover {
					color: variables.$main-first;
				}
			}
		}

		.box-content {
			.item.feed {
				a {
					color: variables.$main-font-color;
					font-weight: 400;

					&:hover {
						color: variables.$main-first;
						text-decoration: none;
					}
				}
			}
		}
	}
}


// the panel that appears in overlay to display the flows
#overlay {
	background: rgba(0, 0, 0, 0.65);
}

#panel {
	top: 3rem;
	right: 3rem;
	bottom: 3rem;
	left: 3rem;
	border-radius: 3px;
}
