@use "sass:string";

@use "mixins";

@use "variables";

/*=== COMPONENTS */
/*===============*/
/*=== Forms */
// parti dans _forms.scss

/*=== Horizontal-list */
.horizontal-list {
	padding: 0.1rem 0;

	& > .item {
		&:first-child {
			padding-left: 0.5rem;
		}

	}
}

/*=== Dropdown */
.dropdown {
	.dropdown-target:target + .btn {
		background-color: variables.$grey-medium-light;
	}
}

.dropdown-menu {
	margin: 9px 0 0 0;
	padding: 0.5rem 0 1rem 0;
	background: variables.$grey-lighter;
	font-size: 1rem;
	border: none;
	border-radius: 3px;
	box-shadow: 0px 6px 8px 0px rgba(0,0,0,0.35);
	text-align: left;

	&::after {
		border: none;
		right: 17px;
	}

	.dropdown-header,
	.dropdown-section .dropdown-section-title {
		padding: 1rem 1.5rem;
		font-weight: bold;
		text-align: left;
		color: variables.$grey-dark;
		text-transform: uppercase;
		letter-spacing: 1px;
	}

	.item {

		@include mixins.transition(all, 0.075s, ease-in-out);

		> a,
		> span,
		> .as-link {
			padding: 0 2rem;
			color: variables.$main-font-color;
			font-size: inherit;
			line-height: 2.5em;

			span.icon {
				padding: 0 0.25rem !important;
			}
		}

		> a,
		> .as-link {
			&:not(.addItem):hover {
				background: variables.$main-first;
				color: variables.$white;

				.icon {
					filter: grayscale(100%) brightness(2.5);
				}
			}
		}

		&.dropdown-section {
			margin-top: 0.75rem;

			~ .dropdown-section {
				border-top-color: variables.$grey-light;
			}

			.item {
				a, .as-link {
					padding-left: 2rem;
				}
			}
		}

		&:not(.addItem) {
			> a:hover,
			button:hover {
				background: variables.$main-first;
				color: variables.$white;

				.icon {
					filter: brightness(3);
				}
			}
		}

		&[aria-checked="true"] {
			a::before {
				margin: 0 0 0 -14px;
				font-weight: bold;
			}
		}

		~ .dropdown-header {
			margin-top: 0.75rem;
			padding-top: 1.75rem;
			border-top-color: variables.$grey-light;
		}

		&.separator {
			margin-top: 0.75rem;
			border-top-color: variables.$grey-light;
		}
	}

	.input {
		select, input {
			margin: 0 auto 5px;
			padding: 2px 5px;
			border-radius: 3px;
		}
	}

}

.tree .tree-folder .tree-folder-items .dropdown-menu {
	// to reset the appearance of the dropdown in the case of a dark sidebar
	.item {
		padding: 0;

		a,
		button {
			color: variables.$main-font-color;

			&:hover {
				color: variables.$white;
			}
		}

		&:hover {
			background: variables.$main-first;
		}
	}
}




/*=== Alerts */
.alert {
	background: variables.$grey-lighter;
	color: variables.$grey-dark;
	font-size: 1rem;
	border: 1px solid variables.$grey-medium;
	border-radius: 3px;
	text-shadow: 0 0 1px variables.$grey-light;
}

.alert-head {
	font-size: 1.15em;
}

.alert > a {
	text-decoration: underline;
	color: inherit;
}

.alert-warn {
	background: variables.$warning-light;
	color: variables.$warning-text;
	border: 1px solid string.unquote(variables.$warning-text + '33'); // on ajoute l’opacité à la fin
}

.alert-success {
	background: variables.$success-light;
	color: variables.$success-text;
	border: 1px solid string.unquote(variables.$success-text + '33');
}

.alert-error {
	background: variables.$alert-light;
	color: variables.$alert-text;
	border: 1px solid string.unquote(variables.$alert-text + '33');
}

/*=== Pagination */
.pagination {
	background: variables.$grey-light;
	color: variables.$main-font-color;

	.item a {
		color: variables.$main-font-color;
	}
}

#load_more.loading,
#load_more.loading:hover {
	background: url("loader.gif") center center no-repeat #34495e;
}

/*=== Boxes */
.box {
	background: variables.$white;
	// border: 1px solid #ddd;
	border: none;
	border-radius: 3px;
	box-shadow: 0px 2px 2px 0px rgba(0,0,0,0.25);

	.box-title {
		background: variables.$grey-light;
		color: variables.$main-font-color;
		border-radius: 2px 2px 0 0;
	}

	.box-content {
		padding-left: 30px;

		.item {
			padding: 0.5rem 0;
			color: variables.$main-font-color;
			font-size: 1rem;
			border-bottom: 1px solid variables.$grey-light;
			line-height: 1.7em;
		}

		.item:last-child {
			border-bottom: none;
		}
	}

	.configure {
		margin-right: 0.5rem;
		padding: 0.25rem;
		border-radius: 3px;

		.icon {
			vertical-align: middle;
		}

		&:hover {
			border-radius: 2px;
			background-color: variables.$main-first;

			.icon {
				filter: brightness(3);
			}
		}
	}
}

/*=== "Load more" part */
#bigMarkAsRead.big {
	text-align: center;
	text-decoration: none;
	background: variables.$main-first-light;
	color: variables.$main-first;

	@include mixins.transition(all, 0.15s, ease-in-out);

	&:hover {
		background: variables.$main-first;
		color: #fff;

		.bigTick {
			filter: brightness(7);
		}
	}

	.bigTick {
		margin: 0.5rem 0;
		background: url(icons/tick-color.svg) center no-repeat;
		display: inline-block;
		width: 64px;
		height: 64px;
		text-indent: -9999px;
		white-space: nowrap;
	}
}
