// The main color of the theme
$main-first: #ca7227; // main color
$main-first-alt: #b7641d; // var for hovers
$main-first-light: #fdf6ef; // var light 1
$main-first-lighter: #fefaf7; // var light 2
$main-first-dark: #3c3835; // var for hovers
$main-first-darker: #221f1d; // var for hovers

// linked font colors
$main-font-color: #363330;
$light-font-color: #6d655f;
$white: #fff; // white (sometimes we would like to tint it a little)

// the whole palette of grays, very useful
// we will try to tint these grays according to the main color
$grey-darker: #524236;
$grey-dark: #766556;
$grey-medium-dark: #ba9;
$grey-medium: #d9ccbf;
$grey-medium-light: #e4d8cc;
$grey-light: #f5f0ec;
$grey-lighter: #fcfaf8;

$unread-font-color: #161a38;
$unread-bg: #f2f6f8;
$unread-bg-light: #fdfdfe;

// alert colors (red, yellow, green)
$alert-bg: #f5633e; // the base
$alert-light: #fde0d8; // +light
$alert-text: #73341f; // +dark

$warning-bg: #f4f762;
$warning-light: #fdfde0;
$warning-text: #73762f;

$success-bg: #10f5b2;
$success-light: #cffdef;
$success-text: #0c7556;

// favorites
$fav-bg: #ffc300;
$fav-light: #fff6da;

// the sidebar color, useful if you want a theme that would have a dark sidebar, e.g.
$sid-font-color: #363330; // the background color of the left bar and the header
$sid-bg: #fbf9f6; // the general background of the left bar, and of the header
$sid-bg-alt: #f7f2ea; // background inside groups
$sid-bg-dark: #efe3d3; // the hover
$sid-sep: #f0e7da; // the separators
$sid-active: $main-first; // the active color
// stylelint-disable-next-line color-hex-length
$sid-active-font: #ffffff; // the active color
$sid-pills: rgba(35,35,0, 0.15); // capsules
