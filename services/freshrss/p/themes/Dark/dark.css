@charset "UTF-8";

:root {
	--frss-background-color: #1c1c1c;
	--frss-background-color-transparent: #111a;
	--frss-font-color-light: #aaa;


	--frss-scrollbar-handle: rgba(0, 0, 0, 0.1);
	--frss-scrollbar-track: rgba(0, 0, 0, 0.05);
	--frss-scrollbar-handle-hover: rgba(0, 0, 0, 0.3);
	--frss-scrollbar-track-hover: rgba(0, 0, 0, 0.05);

	/** Origine **/
	--background-color-light: #1c1c1c;
	--background-color-light-shadowed: inherit;
	--background-color-light-shadowed-transparent: #262626aa;
	--background-color-grey: #1c1c1c;
	--background-color-hover: #26303f;
	--background-color-flux-hover: #111;

	--unread-article-background-color: inherit;
	--unread-article-background-color-hover: #111;
	--favorite-article-background-color: inherit;
	--favorite-article-background-color-hover: #111;

	--font-color-link-hover: #888;
	--font-color: #888;
	--font-color-link: #6986b2;
	--font-color-grey: #999;

	--empty-feed-color: #c95;
	--error-feed-color: #a44;
	--font-color-light: #fff;

	--alert-warn-font-color: #c95;
	--alert-warn-border-color: #c95;
	--alert-warn-background-color: inherit;
	--alert-success-font-color: #484;
	--alert-success-border-color: #484;
	--alert-success-background-color: inherit;
	--alert-error-font-color: #a44;
	--alert-error-boder-color: #a44;
	--alert-error-background-color: inherit;

	--box-shadow-color: #000a;

	--border-color: #000;

	--attention-background-color-active: #801;
	--dark-attention-background-color: #801;
	--dark-attention-gradient-color: #c04;


	--form-element-font-color-focus: #6986b2;
	--form-element-border-color-focus: #2f2f2f;

	--form-element-border-color-invalid: #f00;
	--form-element-invalid-box-shadow-color-inset: #f00;

	--notification-good-border-color: #484;
	--notification-good-font-color: #484;
	--notification-bad-border-color: #a44;
	--notification-bad-font-color: #a44;

	--contrast-border-color-active: #0062be;
	--unread-article-border-color: #ff5300;
	--favorite-article-border-color: #ffc300;

	/** Dark **/
	--dark-border-color: #2f2f2f;

	--dark-border-color0: #000;
	--dark-border-color2: #222;
	--dark-border-color3: #333;
	--dark-border-color4: #444;
	--dark-border-color6: #666;
	--dark-border-color8: #888;

	--dark-background-color-blue: #26303f;
	--dark-background-color1: #111;
	--dark-background-color16: #161616;
	--dark-background-color2: #222;
	--dark-background-color3: #333;
	--dark-favicon-background-color: #fff;

	--dark-background-color-button-hover-gradient1: #4a5d7a;
	--dark-background-color-button-hover-gradient2: #26303f;

	--dark-font-color4: #444;
	--dark-font-color6: #666;
	--dark-font-color8: #888;
	--dark-font-color9: #999;
	--dark-font-colorA: #aaa;

	--dark-notification-border-color: #c95;
	--dark-notification-good-border-color: #484;
	--dark-notification-bad-border-color: #a44;
	--dark-notification-font-color: #c95;

	--dark-notification-good-close-background-color-hover: #484;
	--dark-notification-bad-close-background-color-hover: #a44;

	--dark-form-element-box-shadow-inset: #1d1d1d;

}

/*=== Images */
img.favicon {
	background: var(--dark-favicon-background-color);
	border-radius: 2px;
}

body img,
body video,
p.help .icon,
.icon {
	filter: brightness(.6) contrast(1.2);
}

.spinner {
	filter: invert(1) brightness(.6) contrast(1.2);
}

.bookmark:hover > .spinner {
	filter: invert(1) brightness(1.1);
}

a:hover .icon.spinner {
	filter: invert(1) brightness(1.5);
}

/*=== Forms */
legend {
	border-bottom: 1px solid var(--dark-border-color);
}

input, select, textarea {
	background: var(--dark-background-color3);
	color: var(--dark-font-color9);
	border-color: var(--border-color);
	box-shadow: 0 2px 2px var(--dark-form-element-box-shadow-inset) inset;
}

input:disabled, select:disabled {
	background-color: var(--dark-background-color2);
	color: var(--dark-font-colorA);
	border-style: solid;
	border-color: var(--dark-border-color0);
}

input:focus,
select:focus,
textarea:focus,
input[type="password"]:focus + .toggle-password {
	border: 1px solid var(--form-element-border-color-focus);
	box-shadow: 0 2px 2px var(--dark-form-element-box-shadow-inset) inset;
}

/*=== Tables */
th, td {
	border: 1px solid var(--dark-border-color3);
}

th {
	background-color: var(--dark-background-color2);
}

/*=== COMPONENTS */
/*===============*/
/*=== Forms */
.form-group.form-actions {
	background: var(--dark-background-color2);
	border-top: 1px solid var(--dark-border-color);
}

/*=== Buttons */
button.as-link[disabled] {
	color: var(--dark-font-color4) !important;
}

.stick .btn-important:first-child {
	border-right: 1px solid var(--dark-border-color0);
}

.stick input:focus+input {
	border-left: 1px solid var(--dark-border-color0);
}

.stick input+input:focus {
	border-left: 1px solid var(--dark-border-color3);
}

.btn {
	background-image: none;
	background-color: var(--dark-background-color1);
	color: var(--dark-font-color8);
	border: 1px solid var(--dark-border-color0);
}

.btn:hover {
	background: linear-gradient(to top, var(--dark-background-color-button-hover-gradient1) 0%, var(--dark-background-color-button-hover-gradient2) 100%);
}

.btn:hover .icon {
	filter: brightness(1);
}

.btn.active,
.btn:active,
.dropdown-target:target ~ .btn.dropdown-toggle {
	background-color: var(--dark-background-color3);
	box-shadow: none;
}

.btn.active .icon,
.dropdown-target:target ~ .btn.dropdown-toggle .icon {
	filter: brightness(1);
}

.btn:active {
	background-color: var(--dark-background-color-blue);
}

.btn-important {
	background: var(--dark-background-color-blue);
}

.btn-important:hover {
	background: linear-gradient(to top, var(--dark-background-color-button-hover-gradient1) 0%, var(--dark-background-color-button-hover-gradient2) 100%);
	color: inherit;
}

.btn-important:active {
	background-color: var(--dark-background-color-blue);
}

.btn-important .icon {
	filter: brightness(1);
}

.btn-attention {
	background-image: none;
	background-color: var(--dark-attention-background-color)
}

.btn-attention:hover {
	background: linear-gradient(to top, var(--dark-attention-gradient-color) 0%, var(--dark-attention-background-color) 100%);
}

/*=== switches */
.switch {
	background-color: var(--dark-background-color3);
}

.switch.active {
	background-color: var(--dark-background-color-blue);
}

.switch::before {
	background-color: var(--dark-background-color2);
}

.switch:not([disabled]):hover::before {
	background-color: var(--dark-background-color1);
}

/*=== Navigation */
.nav-list .item.active {
	background-color: var(--dark-background-color3);
}

.nav-list .nav-section .item.active:hover a,
.nav-list .nav-section .item.active:hover .as-link,
.nav-list .item.active {
	color: var(--font-color-link-hover);
	background-color: var(--background-color-hover);
}

.nav-list .item.active a,
.nav-list .item.active .as-link {
	color: var(--font-color-link-hover);
}

.nav-list .nav-header {
	background-color: var(--dark-background-color1);
}

/*=== Dropdown */
.dropdown-menu {
	background-color: var(--background-color-light);
	border: 1px solid var(--dark-border-color8);
	box-shadow: 3px 3px 3px var(--box-shadow-color);
}

.dropdown-menu::after {
	border-color: var(--dark-border-color8);
}

.dropdown-header,
.dropdown-section .dropdown-section-title {
	color: var(--dark-font-color8);
}

.dropdown-menu .item > a:focus,
.dropdown-menu .item > a:hover,
.dropdown-menu .item > button:focus:not([disabled]),
.dropdown-menu .item > button:hover:not([disabled]),
.dropdown-menu .item > label:focus:not(.noHover),
.dropdown-menu .item > label:hover:not(.noHover) {
	background-color: var(--dark-background-color-blue);
	color: var(--dark-font-color8);
}

.item ~ .dropdown-header,
.dropdown-section ~ .dropdown-section,
.item.separator {
	border-top-color: var(--dark-border-color3);
}

/*=== Alerts */
.alert {
	background-color: var(--dark-background-color1);
	color: var(--dark-font-colorA);
	border: 1px solid var(--dark-border-color8);
}

/*=== Pagination */
.pagination {
	color: var(--dark-font-color8);
}

.pagination .item a {
	color: var(--dark-font-color6);
}

.pagination:first-child .item {
	border-top: 1px solid var(--dark-border-color3);
	border-bottom: 1px solid var(--dark-border-color3);
}

/*=== Boxes */
.box {
	border: 1px solid var(--dark-border-color0);
}

.box .icon {
	filter: brightness(100%);
}

.box .box-title {
	background-color: var(--dark-background-color1);
	border-bottom: 1px solid var(--dark-border-color0);
}

/*=== Tree */
.tree-folder-title .title {
	color: var(--dark-font-color8);
}

.tree-folder.active .tree-folder-title {
	background: var(--dark-background-color1);
}

.tree-folder-items {
	background: var(--dark-background-color16);
	border-top: 1px solid var(--dark-border-color2);
	border-bottom: 1px solid var(--dark-border-color2);
}

.tree-folder-items > .item.active {
	background: var(--dark-background-color-blue);
}

.tree-folder-items > .item.active .icon {
	filter: brightness(1.5);
}

.tree-folder-items > .item.active > a {
	color: var(--dark-font-color8);
}

.tree-folder-items .item:hover {
	background-color: var(--dark-background-color-blue);
}

.tree-folder .tree-folder-title:hover .dropdown-toggle .icon,
.tree-folder.active .tree-folder-title .dropdown-toggle .icon,
.tree-folder .tree-folder-title:hover > .icon,
.tree-folder.active .tree-folder-title > .icon,
.tree-folder-items .item:hover .icon {
	filter: brightness(1.5);
}

/*=== STRUCTURE */
/*===============*/
/*=== Header */
.header > .item {
	border-bottom: 1px solid var(--dark-border-color3);
}

.header > .item.title .logo {
	filter: grayscale(60%) brightness(1.1);
}

.header > .item.title a:hover .logo {
	filter: grayscale(60%) brightness(1.5);
}

.aside {
	border-right: 1px solid var(--dark-border-color3);
}

/*=== Aside main page (categories) */
.aside.aside_feed .category .title:not([data-unread="0"])::after {
	background-color: var(--dark-background-color16);
}

.aside.aside_feed .category.active .title:not([data-unread="0"])::after {
	background-color: var(--dark-background-color3);
}

.aside.aside_feed .feed .item-title:not([data-unread="0"])::after {
	background-color: var(--dark-background-color2);
	color: var(--dark-font-color8);
}

.aside.aside_feed .feed.active .item-title:not([data-unread="0"])::after {
	border-color: var(--dark-border-color8);
	color: var(--dark-font-color8);
}

/*=== New article notification */
#new-article {
	background-color: var(--dark-background-color-blue);
}

#new-article > a {
	color: var(--dark-font-colorA);
}

#new-article > a:hover {
	background-color: var(--dark-background-color3);
}

/*=== Day indication */
.day {
	border-top: 1px solid var(--dark-border-color3);
	border-bottom: 1px solid var(--dark-border-color3);
}

/*=== Index menu */
.nav_menu {
	border-bottom: 1px solid var(--dark-border-color);
}

/*=== Feed articles */
.flux {
	border-left: 2px solid var(--dark-border-color);
}

.flux_header {
	border-top: none;
}

.flux .flux_header:hover {
	background-color: var(--background-color-flux-hover);
}

.flux.current {
	background-color: var(--dark-background-color1);
}

.flux .flux_header .item .title {
	color: var(--dark-font-color8);
}

.flux .flux_header .date,
.flux .flux_content .bottom .date {
	color: var(--dark-font-color6);
}

.flux:not(.current):hover .item .title {
	background-color: var(--dark-background-color1);
}

/*=== Content of feed articles */
.content > h1.title > a {
	color: var(--dark-font-color8);
}

.content hr {
	background-color: var(--dark-background-color3);
}

.content pre {
	background-color: var(--dark-background-color2);
	color: var(--dark-font-colorA);
	border: 1px solid var(--dark-border-color0);
}

.content code {
	background-color: var(--dark-background-color1);
	border-color: var(--dark-border-color3);
}

.content pre code {
	color: var(--dark-font-colorA);
}

.content blockquote {
	background: var(--dark-background-color2);
	color: var(--dark-font-color9);
	border-top: 1px solid var(--dark-border-color4);
	border-bottom: 1px solid var(--dark-border-color4);
}

/*=== Notification and actualize notification */
.notification {
	background-color: var(--dark-background-color1);
	color: var(--dark-notification-font-color);
	border: 1px solid var(--dark-notification-border-color);
}

.notification.good {
	background-color: var(--dark-background-color1);
	border-color: var(--dark-notification-good-border-color);
}

.notification.bad {
	background-color: var(--dark-background-color1);
	border-color: var(--dark-notification-bad-border-color);
}

.notification .close:hover {
	background-color: var(--dark-background-color2);
}

.notification.good .close:hover {
	background-color: var(--dark-notification-good-close-background-color-hover);
}

.notification.bad .close:hover {
	background-color: var(--dark-notification-bad-close-background-color-hover);
}

.notification .close .icon {
	filter: brightness(0.6);
}

.notification .close:hover .icon {
	filter: brightness(3);
}

/*=== "Load more" part */
#bigMarkAsRead:hover {
	background-color: var(--dark-background-color1);
	color: var(--dark-font-colorA);
	background-image: linear-gradient(to top, var(--dark-background-color-button-hover-gradient1) 0%, var(--dark-background-color-button-hover-gradient2) 5%);
	box-shadow: none;
}

/*=== Navigation menu (for articles) */
#nav_entries {
	background-color: var(--dark-background-color1);
	border-top: 1px solid var(--dark-border-color3);
}

/*=== READER VIEW */
/*================*/
#stream.reader .flux {
	background-color: var(--dark-background-color2);
}

#stream.reader .flux .flux_content {
	background-color: var(--dark-background-color1);
	border: none;
}

#stream.reader .flux .author {
	color: var(--dark-font-color6);
}

/*=== GLOBAL VIEW */
/*================*/
.box.category .box-title .title {
	color: var(--dark-font-color8);
}

#stream.global .box.category .box-title:hover {
	background-color: var(--dark-background-color-blue);
}

.box.category:not([data-unread="0"]) .box-title {
	background-color: var(--dark-background-color3);
}

.box.category:not([data-unread="0"]) .box-title .title {
	color: var(--dark-font-colorA);
}

#stream.global .feed .item-title:not([data-unread="0"])::after {
	background-color: var(--dark-background-color16);
}

/*=== Panel */
#panel {
	border: 1px solid var(--dark-border-color6);
}

/*=== Slider */
#slider {
	border-left: 1px solid var(--dark-border-color6);
}

.theme-preview-list {
	border-color: var(--dark-border-color0);
}

.theme-preview-list .properties {
	border-color: var(--dark-border-color0);
	color: var(--dark-font-colorA);
}

kbd {
	background-color: var(--dark-background-color3);
	color: var(--dark-font-color9);
	border-color: var(--dark-border-color3);
}

/*=== STATISTICS */
/*===============*/
.stat > table td,
.stat > table th {
	border-bottom: 1px solid var(--dark-border-color3);
}

/*=== MOBILE */
/*===========*/

@media (max-width: 840px) {
	.aside .toggle_aside,
	#overlay .close,
	.dropdown-menu .toggle_aside {
		background-color: var(--dark-background-color1);
		border-bottom: 1px solid var(--dark-border-color3);
	}

	.dropdown-target:target ~ .dropdown-toggle::after {
		border-top: 1px solid var(--dark-border-color8);
		border-left: 1px solid var(--dark-border-color8);
	}
}
