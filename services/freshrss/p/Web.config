﻿<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<system.web>
		<httpRuntime executionTimeout="300" maxRequestLength="8192"/>
	</system.web>
	<system.webServer>
		<defaultDocument>
			<files>
				<clear />
				<add value="index.php" />
				<add value="index.html" />
			</files>
		</defaultDocument>
		<urlCompression doStaticCompression="true" doDynamicCompression="true"/>
		<staticContent>
			<clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="31.00:00:00" />
		</staticContent>
		<caching>
			<profiles>
				<add extension=".css" policy="CacheForTimePeriod" duration="31.00:00:00" location="Any" kernelCachePolicy="DontCache" />
				<add extension=".gif" policy="CacheForTimePeriod" duration="31.00:00:00" location="Any" kernelCachePolicy="DontCache" />
				<add extension=".html" policy="CacheForTimePeriod" duration="31.00:00:00" location="Any" kernelCachePolicy="DontCache" />
				<add extension=".jpg" policy="CacheForTimePeriod" duration="31.00:00:00" location="Any" kernelCachePolicy="DontCache" />
				<add extension=".js" policy="CacheForTimePeriod" duration="31.00:00:00" location="Any" kernelCachePolicy="DontCache" />
				<add extension=".png" policy="CacheForTimePeriod" duration="31.00:00:00" location="Any" kernelCachePolicy="DontCache" />
				<add extension=".svg" policy="CacheForTimePeriod" duration="31.00:00:00" location="Any" kernelCachePolicy="DontCache" />
				<add extension=".woff" policy="CacheForTimePeriod" duration="31.00:00:00" location="Any" kernelCachePolicy="DontCache" />
			</profiles>
		</caching>
		<!--<httpProtocol>
			<customHeaders>
				<add name="Cache-Control" value="public" />
			</customHeaders>
		</httpProtocol>-->
	</system.webServer>
</configuration>
