# If you need custom .htaccess rules, instead of modifying this file,
# create one in `./i/.htaccess` (in particular for access control such as HTTP Basic Auth),
# or in `../.htaccess` for other more general rules.
# See also `../.htaccess.dist` if you cannot set Apache `DocumentRoot` to here (`FreshRSS/p/`) as recommended

<IfModule mod_dir.c>
	DirectoryIndex	index.php index.html
</IfModule>

FileETag	None
AddDefaultCharset	UTF-8

<IfModule mod_mime.c>
	AddType application/javascript .js
	AddType application/json .map

	AddCharset	UTF-8	.html
	AddCharset	UTF-8	.js
</IfModule>

<IfModule mod_filter.c>
	<IfModule mod_deflate.c>
		AddOutputFilterByType DEFLATE application/javascript application/json application/xhtml+xml image/svg+xml text/css text/html
	</IfModule>
</IfModule>

<IfModule mod_expires.c>
	ExpiresActive	on
	ExpiresDefault	"access plus 1 month"
	ExpiresByType	application/javascript	"access plus 1 month"
	ExpiresByType	application/xhtml+xml	"access plus 1 month"
	ExpiresByType	image/x-icon	"access plus 1 month"
	ExpiresByType	text/html	"access plus 1 month"
	<FilesMatch "\.php$">
		ExpiresActive	Off
	</FilesMatch>
</IfModule>

<IfModule mod_headers.c>
	<FilesMatch "\.(css|gif|html|ico|js|png|svg|woff|woff2)$">
		Header	merge Cache-Control "public"
		<If "%{QUERY_STRING} =~ /^[0-9]+$/">
			# For requests like `frss.css?1746304092`
			Header merge Cache-Control "immutable"
		</If>
	</FilesMatch>
	Header edit Set-Cookie ^(.*)$ "$1; SameSite=Lax"
</IfModule>

# Provide the true IP address of the connection (e.g. last proxy), even when using mod_remoteip
<IfModule mod_rewrite.c>
	RewriteEngine on
	RewriteRule .* - [E=CONN_REMOTE_ADDR:%{CONN_REMOTE_ADDR}]
</IfModule>
<IfModule !mod_rewrite.c>
	<IfModule mod_setenvif.c>
		SetEnvIfExpr "%{CONN_REMOTE_ADDR} =~ /(.*)/" CONN_REMOTE_ADDR=$1
	</IfModule>
</IfModule>
