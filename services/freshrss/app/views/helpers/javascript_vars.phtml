<?php
declare(strict_types=1);
/** @var FreshRSS_View $this */
$mark = FreshRSS_Context::userConf()->mark_when;
$s = FreshRSS_Context::userConf()->shortcuts;
$extData = Minz_ExtensionManager::callHook('js_vars', []);
echo json_encode([
	'context' => [
		'anonymous' => !FreshRSS_Auth::hasAccess(),
		'auto_remove_article' => !!FreshRSS_Context::isAutoRemoveAvailable(),
		'hide_posts' => !(FreshRSS_Context::userConf()->display_posts || Minz_Request::actionName() === 'reader'),
		'display_order' => Minz_Request::paramString('order') ?: FreshRSS_Context::userConf()->sort_order,
		'sort' => FreshRSS_Context::$sort,
		'display_categories' => FreshRSS_Context::userConf()->display_categories,
		'auto_mark_article' => !!$mark['article'],
		'auto_mark_site' => !!$mark['site'],
		'auto_mark_scroll' => !!$mark['scroll'],
		'auto_mark_focus' => !!$mark['focus'],
		'auto_load_more' => FreshRSS_Context::userConf()->auto_load_more && FreshRSS_Context::$sort !== 'rand',
		'auto_actualize_feeds' => Minz_Session::paramBoolean('actualize_feeds'),
		'nb_parallel_refresh' => max(1, FreshRSS_Context::systemConf()->nb_parallel_refresh),
		'does_lazyload' => !!FreshRSS_Context::userConf()->lazyload ,
		'sides_close_article' => !!FreshRSS_Context::userConf()->sides_close_article,
		'sticky_post' => !!FreshRSS_Context::isStickyPostEnabled(),
		'html5_notif_timeout' => FreshRSS_Context::userConf()->html5_notif_timeout,
		'auth_type' => FreshRSS_Context::systemConf()->auth_type,
		'current_view' => Minz_Request::actionName(),
		'csrf' => FreshRSS_Auth::csrfToken(),
		'mtime' => [
			'extra.js' => @filemtime(PUBLIC_PATH . '/scripts/extra.js'),
			'feed.js' => @filemtime(PUBLIC_PATH . '/scripts/feed.js'),
		],
		'max_favicon_upload_size' => FreshRSS_Context::systemConf()->limits['max_favicon_upload_size'],
		'version' => FRESHRSS_VERSION,
	],
	'shortcuts' => [
		'actualize' => @$s['actualize'],
		'mark_read' => @$s['mark_read'],
		'mark_favorite' => @$s['mark_favorite'],
		'go_website' => @$s['go_website'],
		'prev_entry' => @$s['prev_entry'],
		'next_entry' => @$s['next_entry'],
		'next_unread_entry' => @$s['next_unread_entry'],
		'skip_prev_entry' => @$s['skip_prev_entry'],
		'skip_next_entry' => @$s['skip_next_entry'],
		'first_entry' => @$s['first_entry'],
		'last_entry' => @$s['last_entry'],
		'collapse_entry' => @$s['collapse_entry'],
		'load_more' => @$s['load_more'],
		'mylabels' => @$s['mylabels'],
		'auto_share' => @$s['auto_share'],
		'focus_search' => @$s['focus_search'],
		'user_filter' => @$s['user_filter'],
		'help' => @$s['help'],
		'close_dropdown' => @$s['close_dropdown'],
		'normal_view' => @$s['normal_view'],
		'global_view' => @$s['global_view'],
		'reading_view' => @$s['reading_view'],
		'rss_view' => @$s['rss_view'],
		'toggle_media' => @$s['toggle_media'],
	],
	'urls' => [
		'index' => _url('index', 'index'),
		'login' => Minz_Url::display(['c' => 'auth', 'a' => 'login'], 'php'),
		'logout' => Minz_Url::display(['c' => 'auth', 'a' => 'logout'], 'php'),
		'help' => FRESHRSS_WIKI,
		'shortcuts' => Minz_Url::display(['c' => 'configure', 'a' => 'shortcut'], 'php'),
	],
	'i18n' => [
		'confirmation_default' => _t('gen.js.confirm_action'),
		'notif_title_articles' => _t('gen.js.feedback.title_new_articles'),
		'notif_body_new_articles' => _t('gen.js.feedback.body_new_articles'),
		'notif_body_unread_articles' => _t('gen.js.feedback.body_unread_articles'),
		'notif_request_failed' => _t('gen.js.feedback.request_failed'),
		'category_empty' => _t('gen.js.category_empty'),
		'labels_empty' => _t('gen.js.labels_empty'),
		'favicon_size_exceeded' => _t('feedback.sub.feed.favicon.too_large', format_bytes(FreshRSS_Context::systemConf()->limits['max_favicon_upload_size'])),
		'language' => FreshRSS_Context::userConf()->language,
	],
	'icons' => [
		'read' => rawurlencode(_i('read')),
		'unread' => rawurlencode(_i('unread')),
		'spinner' => '../themes/icons/spinner.svg',
	],
	'extensions' => $extData,
], JSON_UNESCAPED_UNICODE | JSON_HEX_TAG | JSON_HEX_AMP);
