<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	if ($this->ext_details === null) {
		throw new FreshRSS_Context_Exception('Extension not initialised!');
	}

	$name_encoded = urlencode($this->ext_details->getName());
	$ext_enabled = $this->ext_details->isEnabled();
	if ($ext_enabled) {
		$button_class = ' active';
		$name_class = '';
		$action = 'disable';
		$title = _t('gen.action.disable');
	} else {
		$button_class = '';
		$name_class = ' disabled';
		$action = 'enable';
		$title = _t('gen.action.enable');
	}
	if ($this->ext_details->getType() === 'user' || FreshRSS_Auth::hasAccess('admin')) {?>
	<button class="switch<?= $button_class ?>" form="form-extension" formaction="<?= _url('extension', $action, 'e', $name_encoded) ?>" title="<?= _t('gen.action.enable') ?>"></button>
	<a class="open-slider configure" title="<?= _t('gen.action.manage') ?>" href="<?= _url('extension', 'configure', 'e', $name_encoded) ?>"><?= _i('configure') ?></a>
	<span class="ext_name<?= $name_class ?>"><?= $this->ext_details->getName() ?></span>
<?php } else { ?>
	<button class="switch<?= $button_class ?>" title="<?= _t('admin.extensions.system.no_rights') ?>" disabled="disabled"></button>
	<span class="ext_name<?= $name_class ?>"><?= $this->ext_details->getName() ?></span>
<?php } ?>
