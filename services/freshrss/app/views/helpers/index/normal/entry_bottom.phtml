<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	if ($this->entry === null) {
		return;
	}
	$bottomline_read = FreshRSS_Context::userConf()->bottomline_read;
	$bottomline_favorite = FreshRSS_Context::userConf()->bottomline_favorite;
	$bottomline_sharing = FreshRSS_Context::userConf()->bottomline_sharing && (count(FreshRSS_Context::userConf()->sharing) > 0);
	$bottomline_myLabels = FreshRSS_Context::userConf()->bottomline_myLabels;
	$bottomline_tags = FreshRSS_Context::userConf()->bottomline_tags;
	$bottomline_date = FreshRSS_Context::userConf()->bottomline_date;
	$bottomline_link = FreshRSS_Context::userConf()->bottomline_link;
?><ul class="horizontal-list bottom"><?php
	if (FreshRSS_Auth::hasAccess()) {
		if ($bottomline_read) {
			?><li class="item manage"><?php
				$arUrl = ['c' => 'entry', 'a' => 'read', 'params' => ['id' => $this->entry->id()]];
				if ($this->entry->isRead()) {
					$arUrl['params']['is_read'] = '0';
				}
				?><a class="item-element read" href="<?= Minz_Url::display($arUrl) ?>" title="<?= _t('conf.shortcut.mark_read') ?>"><?php
					echo _i($this->entry->isRead() ? 'read' : 'unread'); ?></a><?php
			?></li><?php
		}
		if ($bottomline_favorite) {
			?><li class="item manage"><?php
				$arUrl = ['c' => 'entry', 'a' => 'bookmark', 'params' => ['id' => $this->entry->id()]];
				if ($this->entry->isFavorite()) {
					$arUrl['params']['is_favorite'] = '0';
				}
				?><a class="item-element bookmark" href="<?= Minz_Url::display($arUrl) ?>" title="<?= _t('conf.shortcut.mark_favorite') ?>"><?php
					echo _i($this->entry->isFavorite() ? 'starred' : 'non-starred'); ?></a><?php
			?></li><?php
		}
	}
	if ($bottomline_myLabels) {
	?><li class="item labels">
		<div class="item-element dropdown dynamictags">
			<div id="dropdown-labels-<?= $this->entry->id() ?>" class="dropdown-target"></div>
			<a class="dropdown-toggle" href="#dropdown-labels-<?= $this->entry->id() ?>">
				<?= _i('label') ?><span class="dropdown-label"><?= _t('index.menu.mylabels') ?></span>
			</a>
		</div>
	</li><?php
	}
	$tags = $bottomline_tags ? $this->entry->tags() : null;
	if (!empty($tags)) {
	?><li class="item tags">
		<div class="item-element dropdown">
			<div id="dropdown-tags-<?= $this->entry->id() ?>" class="dropdown-target"></div>
			<a class="dropdown-toggle" href="#dropdown-tags-<?= $this->entry->id() ?>">
				<?= _i('tag') ?><span class="dropdown-label"><?= _t('index.tag.related') ?></span>
			</a>
			<ul class="dropdown-menu">
				<li class="dropdown-header"><?= _t('index.tag.related') ?></li>
				<?php
				foreach ($tags as $tag) {
					?><li class="item"><a href="<?= _url('index', 'index', 'search', '#' .
						FreshRSS_tag_Controller::escapeForSearch($tag)) ?>"><?= $tag ?></a></li><?php
				} ?>
			</ul>
			<a class="dropdown-close" href="#close">❌</a>
		</div>
	</li><?php
	}
	?><li class="item share"><?php
		if ($bottomline_sharing) {
		?><div class="item-element dropdown">
			<div id="dropdown-share-<?= $this->entry->id() ?>" class="dropdown-target"></div>
			<a class="dropdown-toggle" href="#dropdown-share-<?= $this->entry->id() ?>">
				<?= _i('share') ?><span class="dropdown-label"><?= _t('index.share') ?></span>
			</a>
			<?php /* entry_share_menu */ ?>
		</div>
		<?php } ?>
	</li><?php
	if ($bottomline_date) {
		?><li class="item date"><time datetime="<?= $this->entry->machineReadableDate() ?>" class="item-element"><?= $this->entry->date() ?></time>&nbsp;</li><?php
	}
	if ($bottomline_link) {
		?><li class="item link"><a target="_blank" class="item-element" rel="noreferrer" href="<?= $this->entry->link() ?>" title="<?= _t('conf.shortcut.see_on_website') ?>"><?= _i('link') ?></a></li><?php
	} ?>
</ul>
