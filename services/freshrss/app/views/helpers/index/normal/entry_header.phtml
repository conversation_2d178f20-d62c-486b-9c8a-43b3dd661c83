<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$topline_read = FreshRSS_Context::userConf()->topline_read;
	$topline_favorite = FreshRSS_Context::userConf()->topline_favorite;
	$topline_myLabels = FreshRSS_Context::userConf()->topline_myLabels;
	$topline_sharing = FreshRSS_Context::userConf()->topline_sharing;
	$topline_website = FreshRSS_Context::userConf()->topline_website;
	$topline_thumbnail = FreshRSS_Context::userConf()->topline_thumbnail;
	$topline_summary = FreshRSS_Context::userConf()->topline_summary;
	$topline_display_authors = FreshRSS_Context::userConf()->topline_display_authors;
	$topline_date = FreshRSS_Context::userConf()->topline_date;
	$topline_link = FreshRSS_Context::userConf()->topline_link;
	$lazyload = FreshRSS_Context::userConf()->lazyload;
	if ($this->feed === null || $this->entry === null) {
		return;
	}
?><ul class="horizontal-list flux_header website<?= $topline_website ?>" data-website-name="<?= $this->feed->name() ?>" data-article-authors="<?= implode(' · ', $this->entry->authors()) ?>"><?php
	if (FreshRSS_Auth::hasAccess()) {
		if ($topline_read) {
			?><li class="item manage"><?php
				$arUrl = ['c' => 'entry', 'a' => 'read', 'params' => ['id' => $this->entry->id()]];
				if ($this->entry->isRead()) {
					$arUrl['params']['is_read'] = '0';
				}
				?><a class="item-element read" href="<?= Minz_Url::display($arUrl) ?>" title="<?= _t('conf.shortcut.mark_read') ?>"><?php
					echo _i($this->entry->isRead() ? 'read' : 'unread'); ?></a><?php
			?></li><?php
		}
		if ($topline_favorite) {
			?><li class="item manage"><?php
				$arUrl = ['c' => 'entry', 'a' => 'bookmark', 'params' => ['id' => $this->entry->id()]];
				if ($this->entry->isFavorite()) {
					$arUrl['params']['is_favorite'] = '0';
				}
				?><a class="item-element bookmark" href="<?= Minz_Url::display($arUrl) ?>" title="<?= _t('conf.shortcut.mark_favorite') ?>"><?php
					echo _i($this->entry->isFavorite() ? 'starred' : 'non-starred'); ?></a><?php
			?></li><?php
		}
	}

	if ($topline_website !== 'none'):
	?><li class="item website <?= $topline_website ?>">
		<a href="<?= _url('index', 'index', 'get', 'f_' . $this->feed->id()) ?>" class="item-element" title="<?= _t('gen.action.filter') ?>: <?= $this->feed->name() ?>">
			<?php if (FreshRSS_Context::userConf()->show_favicons && 'name' !== $topline_website): ?><img class="favicon" src="<?= $this->feed->favicon() ?>" alt="✇" loading="lazy" /><?php endif; ?><?php if ('icon' !== $topline_website): ?><span class="websiteName"><?= $this->feed->name() ?></span><?php endif; ?>
		</a>
		</li><?php
	endif; ?>

	<?php
	if ($topline_thumbnail !== 'none'):
	?><li class="item thumbnail <?= $topline_thumbnail ?> <?= $topline_summary ? '' : 'small' ?>"><?php
		$thumbnail = $this->entry->thumbnail();
		if ($thumbnail != null):
			?><img src="<?= $thumbnail['url'] ?>" class="item-element "<?= $lazyload ? ' loading="lazy"' : '' ?> alt="" /><?php
		endif;
	?></li><?php
	endif; ?>
	<li class="item titleAuthorSummaryDate">
		<a target="_blank" rel="noreferrer" href="<?= $this->entry->link() ?>" class="item-element title<?= (($topline_thumbnail !== 'none') || $topline_summary) ? ' multiline' : '' ?>" dir="auto"><?= $this->entry->title() ?><?php
		if ($topline_display_authors):
			?> <span class="author"><?= implode(' · ', $this->entry->authors()) ?></span><?php
		endif;
		?></a>
	<?php
	if ($topline_summary):
		?><div class="summary"><?= trim(mb_substr(strip_tags($this->entry->content(false)), 0, 500, 'UTF-8')) ?></div><?php
	endif; ?>
	<?php if ($topline_date) { ?><span class="item-element date"><time datetime="<?= $this->entry->machineReadableDate() ?>"><?= $this->entry->date() ?></time>&nbsp;</span><?php } ?>
	</li>
	<?php if ($topline_myLabels) { ?>
		<li class="item labels">
			<div class="item-element dropdown dynamictags">
				<div id="dropdown-labels2-<?= $this->entry->id() ?>" class="dropdown-target"></div>
				<a class="dropdown-toggle" href="#dropdown-labels2-<?= $this->entry->id() ?>" title="<?= _t('index.menu.mylabels') ?>">
					<?= _i('label') ?>
				</a>
				<?php /* labels_article_template */ ?>
			</div>
		</li>
	<?php } ?>
	<?php if ($topline_sharing) { ?>
		<li class="item share">
			<div class="item-element dropdown">
				<div id="dropdown-share2-<?= $this->entry->id() ?>" class="dropdown-target"></div>
				<a class="dropdown-toggle" href="#dropdown-share2-<?= $this->entry->id() ?>" title="<?= _t('index.share') ?>">
					<?= _i('share') ?>
				</a>
				<?php /* share_article_template */ ?>
			</div>
		</li>
	<?php } ?>
	<?php if ($topline_link) { ?><li class="item link"><a target="_blank" rel="noreferrer" href="<?= $this->entry->link() ?>" class="item-element" title="<?=
		_t('conf.shortcut.see_on_website') ?>"><?= _i('link') ?></a></li><?php } ?>
</ul>
