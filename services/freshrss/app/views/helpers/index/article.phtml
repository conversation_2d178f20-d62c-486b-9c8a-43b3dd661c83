<?php
	// used only in Reading view and html.phtml
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$entry = $this->entry;
	$feed = $this->feed;
	if ($feed === null || $entry === null) {
		return;
	}
?>
<article class="flux_content content_<?= FreshRSS_Context::userConf()->content_width ?>" dir="auto">
	<div class="content">
		<header>
			<?php
				$favoriteUrl = ['c' => 'entry', 'a' => 'bookmark', 'params' => ['id' => $entry->id()]];
				if ($entry->isFavorite()) {
					$favoriteUrl['params']['is_favorite'] = '0';
				}
				$readUrl = ['c' => 'entry', 'a' => 'read', 'params' => ['id' => $entry->id()]];
				if ($entry->isRead()) {
					$readUrl['params']['is_read'] = '0';
				}
			?>
			<div class="article-header-topline horizontal-list">
				<?php if (FreshRSS_Auth::hasAccess()) { ?>
					<?php if (FreshRSS_Context::userConf()->topline_read && FreshRSS_Context::userConf()->show_article_icons == 't') { ?>
						<div class="item manage">
							<a class="read" href="<?= Minz_Url::display($readUrl) ?>" title="<?= _t('conf.shortcut.mark_read') ?>"><?= _i($entry->isRead() ? 'read' : 'unread') ?></a>
						</div>
					<?php } ?>
					<?php if (FreshRSS_Context::userConf()->topline_favorite && FreshRSS_Context::userConf()->show_article_icons == 't') { ?>
						<div class="item manage">
							<a class="bookmark" href="<?= Minz_Url::display($favoriteUrl) ?>" title="<?= _t('conf.shortcut.mark_favorite') ?>"><?= _i($entry->isFavorite() ? 'starred' : 'non-starred') ?></a>
						</div>
					<?php } ?>
				<?php } ?>
				<div class="item">
					<?php if (FreshRSS_Context::userConf()->show_feed_name === 't') { ?>
						<a class="website" href="<?= _url('index', 'reader', 'get', 'f_' . $feed->id()) ?>" title="<?= _t('gen.action.filter') ?>">
							<?php if (FreshRSS_Context::userConf()->show_favicons): ?>
								<img class="favicon" src="<?= $feed->favicon() ?>" alt="✇" loading="lazy" /><?php
							endif; ?><span><?= $feed->name() ?></span></a>
					<?php } ?>
				</div>
				<?php
				if (FreshRSS_Context::userConf()->topline_link && FreshRSS_Context::userConf()->show_article_icons == 't') { ?>
					<div class="item link">
						<a target="_blank" rel="noreferrer" href="<?= $entry->link() ?>" class="item-element" title="<?= _t('conf.shortcut.see_on_website') ?>"><?= _i('link') ?></a>
					</div>
				<?php } ?>
			</div>

			<?php
				if (in_array(FreshRSS_Context::userConf()->show_tags, ['b', 'h'], true)) {
					$this->renderHelper('index/tags');
				}
			?>
			<h1 class="title"><a target="_blank" rel="noreferrer" class="go_website" href="<?= $entry->link() ?>"><?= $entry->title() ?></a></h1>
			<?php if (in_array(FreshRSS_Context::userConf()->show_author_date, ['h','b'], true)) { ?>
				<div class="subtitle horizontal-list">
					<?php if (FreshRSS_Auth::hasAccess()) { ?>
						<?php if (FreshRSS_Context::userConf()->topline_read && FreshRSS_Context::userConf()->show_article_icons == 'a') { ?>
							<div class="item manage">
								<a class="read" href="<?= Minz_Url::display($readUrl) ?>" title="<?= _t('conf.shortcut.mark_read') ?>"><?= _i($entry->isRead() ? 'read' : 'unread') ?></a>
							</div>
						<?php } ?>
						<?php if (FreshRSS_Context::userConf()->topline_favorite && FreshRSS_Context::userConf()->show_article_icons == 'a') { ?>
							<div class="item manage">
								<a class="bookmark" href="<?= Minz_Url::display($favoriteUrl) ?>" title="<?= _t('conf.shortcut.mark_favorite') ?>"><?= _i($entry->isFavorite() ? 'starred' : 'non-starred') ?></a>
							</div>
						<?php } ?>
					<?php } ?>
					<div class="item">
						<?php if (FreshRSS_Context::userConf()->show_feed_name === 'a') { ?>
							<span class="website"><a href="<?= $this->internal_rendering ? $feed->website() : _url('index', 'reader', 'get', 'f_' . $feed->id()) ?>" title="<?= _t('gen.action.filter') ?>">
								<?php if (FreshRSS_Context::userConf()->show_favicons): ?>
									<img class="favicon" src="<?= $feed->favicon() ?>" alt="✇" loading="lazy" /><?php
								endif; ?><span><?= $feed->name() ?></span></a></span>
						<?php }
							if (!empty($entry->authors())) {
								$this->renderHelper('index/authors');
								if ($this->feed === null || $this->entry === null) {
									throw new Exception('Unexpected side effect!');	// Should never occur. Only for PHPStan
								}
							}
						?>
					</div>
					<div class="item date">
						<time datetime="<?= $entry->machineReadableDate() ?>"><?= $entry->date() ?></time>
					</div>
					<?php
					if (FreshRSS_Context::userConf()->topline_link && FreshRSS_Context::userConf()->show_article_icons == 'a') { ?>
						<div class="item link">
							<a target="_blank" rel="noreferrer" href="<?= $entry->link() ?>" class="item-element" title="<?= _t('conf.shortcut.see_on_website') ?>"><?= _i('link') ?></a>
						</div>
					<?php } ?>
				</div>
			<?php } ?>
		</header>

		<div class="text">
			<?= $entry->content(true) ?>
		</div>
		<?php
		$display_authors_date = in_array(FreshRSS_Context::userConf()->show_author_date, ['b', 'f'], true);
		$display_tags = in_array(FreshRSS_Context::userConf()->show_tags, ['b', 'f'], true);

		if ($display_authors_date || $display_tags) {
			?>
			<footer>
				<?php if ($display_authors_date) { ?>
					<div class="subtitle">
						<?php if (FreshRSS_Context::userConf()->show_feed_name === 'a') { ?>
							<div class="website"><a href="<?= _url('index', 'reader', 'get', 'f_' . $feed->id()) ?>" title="<?= _t('gen.action.filter') ?>">
								<?php if (FreshRSS_Context::userConf()->show_favicons): ?>
									<img class="favicon" src="<?= $feed->favicon() ?>" alt="✇" loading="lazy" /><?php
								endif; ?><span><?= $feed->name() ?></span></a></div>
						<?php }
							$this->renderHelper('index/authors');
						?>
						<div class="date">
							<time datetime="<?= $entry->machineReadableDate() ?>"><?= $entry->date() ?></time>
						</div>
					</div>
					<?php
				}

				if ($display_tags) {
					$this->renderHelper('index/tags');
				}
				?>
			</footer>
			<?php
		} ?>
	</div>

	<footer>
		<ul class="horizontal-list bottom">
			<?php if (FreshRSS_Auth::hasAccess()) { ?>
				<?php if (FreshRSS_Context::userConf()->bottomline_read) { ?>
					<li class="item manage">
						<a class="read" href="<?= Minz_Url::display($readUrl) ?>" title="<?= _t('conf.shortcut.mark_read') ?>"><?= _i($entry->isRead() ? 'read' : 'unread') ?></a>
					</li>
				<?php } ?>
				<?php if (FreshRSS_Context::userConf()->bottomline_favorite) { ?>
					<li class="item manage">
						<a class="bookmark" href="<?= Minz_Url::display($favoriteUrl) ?>" title="<?= _t('conf.shortcut.mark_favorite') ?>"><?= _i($entry->isFavorite() ? 'starred' : 'non-starred') ?></a>
					</li>
				<?php } ?>
			<?php } ?>
			<li class="item share"></li>
			<?php
			if (FreshRSS_Context::userConf()->bottomline_date) {
				?>
				<li class="item date">
					<time datetime="<?= $entry->machineReadableDate() ?>" class="item-element"><?= $entry->date() ?></time>&nbsp;
				</li>
				<?php
			}
			if (FreshRSS_Context::userConf()->bottomline_link) { ?>
				<li class="item link">
					<a target="_blank" rel="noreferrer" href="<?= $entry->link() ?>" class="item-element" title="<?= _t('conf.shortcut.see_on_website') ?>"><?= _i('link') ?></a>
				</li>
			<?php } ?>
		</ul>
	</footer>
</article>
