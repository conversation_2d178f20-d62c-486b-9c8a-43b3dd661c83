<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	if ($this->entry === null) {
		return;
	}
?>
<div class="author"><?= _t('gen.short.by_author') ?>
<?php
	$first = true;
	foreach ($this->entry->authors() as $author) {
		$href = Minz_Url::display(Minz_Request::modifiedCurrentRequest(['search' => 'author:"' . htmlspecialchars_decode($author, ENT_QUOTES) . '"']));
		?><?= $first ? '' : ' · ' ?><a href="<?= $href ?>" title="<?= _t('gen.action.filter') ?>"><?= $author ?></a><?php
		$first = false;
	}
?>
</div>
