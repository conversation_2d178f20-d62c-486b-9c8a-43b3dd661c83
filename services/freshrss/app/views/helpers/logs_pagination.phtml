<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$c = Minz_Request::controllerName();
	$a = Minz_Request::actionName();
	$params = array_filter($_GET, 'is_string', ARRAY_FILTER_USE_KEY);
?>
<?php if ($this->nbPage > 1) { ?>
<nav class="nav-pagination nav-list">
	<ul class="pagination">
		<?php
			/** @var string $getteur from Minz_Paginator::render() */
			$params[$getteur] = 1;
		?>
		<li class="item pager-first">
			<a href="<?= Minz_Url::display(['c' => $c, 'a' => $a, 'params' => $params]) ?>">« <?= _t('conf.logs.pagination.first') ?></a>
		</li>

		<?php $params[$getteur] = $this->currentPage - 1; ?>

		<li class="item pager-previous">
			<?php if ($this->currentPage > 1) { ?>
			<a href="<?= Minz_Url::display(['c' => $c, 'a' => $a, 'params' => $params]) ?>">‹ <?= _t('conf.logs.pagination.previous') ?></a>
			<?php } ?>
		</li>

		<?php if ($this->currentPage - 2 > 1) { ?>
		<li class="item">…</a></li>
		<?php } ?>

		<?php
		for ($i = $this->currentPage - 2; $i <= $this->currentPage + 2; $i++) {
			if ($i > 0 && $i <= $this->nbPage) {
				$params[$getteur] = $i;
				if ($i != $this->currentPage) {
					$class = '';
					$aria = 'false';
				} else {
					$class = ' active';
					$aria = 'true';
				} ?>
				<li class="item<?= $class ?>"><a href="<?= Minz_Url::display(['c' => $c, 'a' => $a, 'params' => $params]) ?>" aria-current="<?= $aria ?>"><?= $i ?></a></li>
			<?php
			}
		} ?>

		<?php if ($this->nbPage > $this->currentPage + 2) { ?>
		<li class="item">…</a></li>
		<?php } ?>

		<?php $params[$getteur] = $this->currentPage + 1; ?>

		<li class="item pager-next">
			<?php if ($this->currentPage < $this->nbPage) { ?>
			<a href="<?= Minz_Url::display(['c' => $c, 'a' => $a, 'params' => $params]) ?>"><?= _t('conf.logs.pagination.next') ?> ›</a>
			<?php } ?>
		</li>

		<?php $params[$getteur] = $this->nbPage; ?>
		<li class="item pager-last">
			<a href="<?= Minz_Url::display(['c' => $c, 'a' => $a, 'params' => $params]) ?>"><?= _t('conf.logs.pagination.last') ?> »</a>
		</li>
	</ul>
</nav>
<?php } ?>
