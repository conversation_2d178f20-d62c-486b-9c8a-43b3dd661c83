<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$url_next = Minz_Request::currentRequest();
	if (FreshRSS_Context::$continuation_id !== '0') {
		$url_next['params']['cid'] = FreshRSS_Context::$continuation_id;
	}
	if (FreshRSS_Context::$sort !== 'id') {
		$url_next['params']['idMax'] = FreshRSS_Context::$id_max;
	}
	$url_next['params']['state'] = (string)FreshRSS_Context::$state;
	$url_next['params']['ajax'] = '1';

	$url_mark_read = [
		'c' => 'entry',
		'a' => 'read',
		'params' => [
			'get' => FreshRSS_Context::currentGet(),
			'nextGet' => FreshRSS_Context::$next_get,
			'idMax' => FreshRSS_Context::$id_max,
			'search' => htmlspecialchars_decode(FreshRSS_Context::$search->getRawInput(), ENT_QUOTES),
			'state' => FreshRSS_Context::$state,
		],
	];

	$hasAccess = FreshRSS_Auth::hasAccess();
	if ($hasAccess) {
?>
<form id="stream-footer" action="" method="post">
<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
<?php } else { ?>
<div id="stream-footer">
<?php }?>
	<div class="stream-footer-inner">
	<?php if (FreshRSS_Context::$continuation_id !== '0') { ?>
		<button id="load_more" type="submit" class="btn" formaction="<?= Minz_Url::display($url_next) ?>"><?= _t('gen.stream.load_more') ?></button>
	<?php } elseif ($hasAccess) { ?>
		<?= _t('gen.stream.nothing_to_load') ?>
		<?php if (FreshRSS_Context::userConf()->mark_read_button !== 'none') {
			$css = '';
			switch (FreshRSS_Context::userConf()->mark_read_button) {
				case 'big':
					$css = 'as-link big';
					break;
				case 'small':
					$css = 'btn small';
					break;
			}
			?>
			<br />
			<button id="bigMarkAsRead"
				class="<?= $css ?> <?= FreshRSS_Context::userConf()->reading_confirm ? 'confirm" disabled="disabled' : '' ?>"
				form="stream-footer"
				formaction="<?= Minz_Url::display($url_mark_read) ?>"
				type="submit">
				<span class="bigTick">✓</span><br />
				<span class="markAllRead"><?= _t('gen.stream.mark_all_read') ?></span><br />
				<?php if (FreshRSS_Context::userConf()->onread_jump_next) { ?>
					<span class="jumpNext"><?= _t('conf.reading.jump_next') ?></span>
				<?php } ?>
			</button>
		<?php
		} } else { ?>
		<?= _t('gen.stream.nothing_to_load') ?><br />
	<?php } ?>
	</div>
<?php if ($hasAccess) { ?>
</form>
<?php } else {?>
</div>
<?php }?>
