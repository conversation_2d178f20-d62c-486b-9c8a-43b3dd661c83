<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
?>
<main class="prompt">
	<h1><?= _t('gen.auth.registration') ?></h1>

	<form method="post" action="<?= _url('user', 'create') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />

		<div class="form-group">
			<label for="new_user_language"><?= _t('admin.user.language') ?></label>
			<select name="new_user_language" id="new_user_language">
			<?php $languages = Minz_Translate::availableLanguages(); ?>
			<?php foreach ($languages as $lang) { ?>
			<option value="<?= $lang ?>"<?= $this->preferred_language === $lang ? ' selected="selected"' : '' ?>><?= _t('gen.lang.' . $lang) ?></option>
			<?php } ?>
			</select>
		</div>

		<div class="form-group">
			<label for="new_user_timezone"><?= _t('conf.display.timezone') ?></label>
			<select name="new_user_timezone" id="new_user_timezone">
			<?php $timezones = array_merge([''], DateTimeZone::listIdentifiers()); ?>
			<?php foreach ($timezones as $timezone): ?>
			<option value="<?= $timezone ?>"<?= $timezone === '' ? ' selected="selected"' : '' ?>>
				<?= $timezone == '' ? _t('gen.short.by_default') . ' (' . FreshRSS_Context::defaultTimeZone() . ')' : $timezone ?>
			</option>
			<?php endforeach; ?>
			</select>
		</div>

		<div class="form-group">
			<label for="new_user_name"><?= _t('gen.auth.username') ?></label>
			<input id="new_user_name" name="new_user_name" type="text" size="16" required="required" autocomplete="off"
				pattern="<?= FreshRSS_user_Controller::USERNAME_PATTERN ?>" autocapitalize="off" />
			<p class="help"><?= _i('help') ?> <?= _t('gen.auth.username.format') ?></p>
		</div>

		<?php if ($this->show_email_field) { ?>
			<div class="form-group">
				<label for="new_user_email">
					<?= _t('gen.auth.email') ?>
				</label>
				<input id="new_user_email" name="new_user_email" type="email" required="required" />
			</div>
		<?php } ?>

		<div class="form-group">
			<label for="new_user_passwordPlain"><?= _t('gen.auth.password') ?></label>
			<div class="stick">
				<input type="password" id="new_user_passwordPlain" name="new_user_passwordPlain" required="required" autocomplete="new-password" pattern=".{7,}" />
				<button type="button" class="btn toggle-password" data-toggle="new_user_passwordPlain"><?= _i('key') ?></button>
			</div>
			<noscript><b><?= _t('gen.js.should_be_activated') ?></b></noscript>
			<p class="help"><?= _i('help') ?> <?= _t('gen.auth.password.format') ?></p>

		</div>

		<?php if ($this->show_tos_checkbox) { ?>
			<div class="form-group">
				<label class="checkbox" for="accept-tos">
					<input type="checkbox" name="accept_tos" id="accept-tos" value="1" required="required" />
					<?= _t('gen.auth.accept_tos', _url('index', 'tos')) ?>
				</label>
			</div>
		<?php } ?>

		<div class="form-group form-group-actions">
			<?php
				$redirect_url = urlencode(Minz_Url::display(
					['c' => 'index', 'a' => 'index'],
					'php', true
				));
			?>
			<input type="hidden" name="r" value="<?= $redirect_url ?>" />

			<button type="submit" class="btn btn-important"><?= _t('gen.action.create') ?></button>

			<a href="<?= _url('index', 'index') ?>"><?= _t('gen.action.cancel') ?></a>
		</div>
	</form>
</main>

<footer class="main-footer">
	<a href="<?= _url('index', 'about') ?>"><?= _t('gen.freshrss.about') ?></a>
	<?php if (file_exists(TOS_FILENAME)) { ?>
		| <a href="<?= _url('index', 'tos') ?>"><?= _t('index.tos.title')?></a>
	<?php } ?>
</footer>
