<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_configure');
?>
<main class="post">
	<h1><?= _t('gen.menu.authentication') ?></h1>
	<form method="post" action="<?= _url('auth', 'index') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />

		<div class="form-group">
			<label class="group-name" for="auth_type"><?= _t('admin.auth.type') ?></label>
			<div class="group-controls">
				<select id="auth_type" name="auth_type" required="required" data-leave-validation="<?= FreshRSS_Context::systemConf()->auth_type ?>">
					<?php if (!in_array(FreshRSS_Context::systemConf()->auth_type, ['form', 'http_auth', 'none'], true)) { ?>
						<option selected="selected"></option>
					<?php } ?>
					<option value="form"<?= FreshRSS_Context::systemConf()->auth_type === 'form' ? ' selected="selected"' : '',
						cryptAvailable() ? '' : ' disabled="disabled"' ?>><?= _t('admin.auth.form') ?></option>
					<option value="http_auth"<?= FreshRSS_Context::systemConf()->auth_type === 'http_auth' ? ' selected="selected"' : '' ?>>
						<?= _t('admin.auth.http') ?> (REMOTE_USER = '<?= httpAuthUser() ?>')</option>
					<option value="none"<?= FreshRSS_Context::systemConf()->auth_type === 'none' ? ' selected="selected"' : '' ?>><?= _t('admin.auth.none') ?></option>
				</select>
			</div>
		</div>

		<div class="form-group">
			<div class="group-controls">
				<label class="checkbox" for="anon_access">
					<input type="checkbox" name="anon_access" id="anon_access" value="1"<?=
						FreshRSS_Context::systemConf()->allow_anonymous ? ' checked="checked"' : '',
						FreshRSS_Auth::accessNeedsAction() ? '' : ' disabled="disabled"' ?> data-leave-validation="<?= FreshRSS_Context::systemConf()->allow_anonymous ?>"/>
					<?= _t('admin.auth.allow_anonymous', FreshRSS_Context::systemConf()->default_user) ?>
				</label>
			</div>
		</div>

		<div class="form-group">
			<div class="group-controls">
				<label class="checkbox" for="anon_refresh">
					<input type="checkbox" name="anon_refresh" id="anon_refresh" value="1"<?=
						FreshRSS_Context::systemConf()->allow_anonymous_refresh ? ' checked="checked"' : '',
						FreshRSS_Auth::accessNeedsAction() ? '' : ' disabled="disabled"' ?> data-leave-validation="<?= FreshRSS_Context::systemConf()->allow_anonymous_refresh ?>"/>
					<?= _t('admin.auth.allow_anonymous_refresh') ?>
				</label>
			</div>
		</div>

		<div class="form-group">
			<div class="group-controls">
				<label class="checkbox" for="unsafe_autologin">
					<input type="checkbox" name="unsafe_autologin" id="unsafe_autologin" value="1"<?=
						FreshRSS_Context::systemConf()->unsafe_autologin_enabled ? ' checked="checked"' : '',
						FreshRSS_Auth::accessNeedsAction() ? '' : ' disabled="disabled"' ?> data-leave-validation="<?= FreshRSS_Context::systemConf()->unsafe_autologin_enabled ?>"/>
					<?= _t('admin.auth.unsafe_autologin') ?>
					<kbd><?= Minz_Url::display(['c' => 'auth', 'a' => 'login', 'params' => ['u' => 'alice', 'p' => '1234']], 'html', true) ?></kbd>
				</label>
			</div>
		</div>

		<div class="form-group">
			<div class="group-controls">
				<label class="checkbox" for="api_enabled">
					<input type="checkbox" name="api_enabled" id="api_enabled" value="1"<?=
						FreshRSS_Context::systemConf()->api_enabled ? ' checked="checked"' : '',
						FreshRSS_Auth::accessNeedsLogin() ? '' : ' disabled="disabled"' ?> data-leave-validation="<?= FreshRSS_Context::systemConf()->api_enabled ?>"/>
					<?= _t('admin.auth.api_enabled') ?>
				</label>
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.submit') ?></button>
				<button type="reset" class="btn"><?= _t('gen.action.cancel') ?></button>
			</div>
		</div>
	</form>
</main>
