<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_subscription');
?>
<main class="post ">
	<h1><?= _t('sub.menu.import_export') ?></h1>

	<h2><?= _t('sub.category.dynamic_opml') ?></h2>
	<div class="form-group form-actions">
		<div class="group-controls">
			<ul>
				<li><a href="<?= _url('subscription', 'add') ?>"><?= _t('sub.title.add_dynamic_opml') ?> <?= _i('opml-dyn') ?></a></li>
			</ul>
		</div>
	</div>

	<h2><?= _t('sub.import_export.import') ?></h2>
	<form method="post" action="<?= _url('importExport', 'import') ?>" enctype="multipart/form-data">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<div class="form-group">
			<label class="group-name" for="file">
				<?= extension_loaded('zip') ? _t('sub.import_export.file_to_import') : _t('sub.import_export.file_to_import_no_zip') ?>
			</label>
			<div class="group-controls">
				<input type="file" name="file" id="file" />
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.import') ?></button>
			</div>
		</div>
	</form>

	<h2><?= _t('sub.import_export.export') ?></h2>
	<?php if (count($this->feeds) > 0) { ?>
	<form method="post" action="<?= _url('importExport', 'export') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<div class="form-group">
			<div class="group-controls">
				<label class="checkbox" for="export_opml">
					<input type="checkbox" name="export_opml" id="export_opml" value="1" checked="checked" />
					<?= _t('sub.import_export.export_opml') ?>
				</label>

				<label class="checkbox" for="export_labelled">
					<input type="checkbox" name="export_labelled" id="export_labelled" value="1" <?= extension_loaded('zip') ? 'checked="checked"' : '' ?> />
					<?= _t('sub.import_export.export_labelled') ?>
				</label>

				<label class="checkbox" for="export_starred">
					<input type="checkbox" name="export_starred" id="export_starred" value="1" <?= extension_loaded('zip') ? 'checked="checked"' : '' ?> />
					<?= _t('sub.import_export.export_starred') ?>
				</label>

				<?php
					$select_args = '';
					if (extension_loaded('zip')) {
						$select_args = ' size="' . min(10, count($this->feeds)) . '" multiple="multiple"';
					}
				?>
				<select name="export_feeds[]"<?= $select_args ?>>
					<?= extension_loaded('zip') ? '' : '<option></option>' ?>
					<?php foreach ($this->feeds as $feed) { ?>
					<option value="<?= $feed->id() ?>"><?= $feed->name() ?></option>
					<?php } ?>
				</select>
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.export') ?></button>
			</div>
		</div>
	</form>
	<?php } ?>

	<h2><?= _t('sub.import_export.export.sqlite') ?></h2>
	<?php if (count($this->sqliteArchives ?? []) === 0): ?>
	<p class="alert alert-warn">
		<?= _t('gen.short.not_applicable') ?>
	</p>
	<?php else: ?>
	<form method="post" action="<?= _url('importExport', 'sqlite') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<div class="form-group">
			<div class="group-controls">
				<select name="sqlite">
					<?php foreach ($this->sqliteArchives ?? [] as $sqliteArchive): ?>
					<option value="<?= htmlspecialchars($sqliteArchive['name'], ENT_COMPAT, 'UTF-8') ?>">
						<?= htmlspecialchars($sqliteArchive['name'], ENT_NOQUOTES, 'UTF-8') ?>
						<small>(<?= format_bytes($sqliteArchive['size']) ?> · <?= date('c', $sqliteArchive['mtime']) ?>)</small>
					</option>
					<?php endforeach; ?>
				</select>
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.download') ?></button>
			</div>
		</div>
	</form>
	<?php endif; ?>
</main>
