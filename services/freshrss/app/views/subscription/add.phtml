<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_subscription');
?>
<main class="post drop-section">
	<h1><?= _t('sub.menu.add') ?></h1>
	<h2><?= _t('sub.title.add_category') ?></h2>
	<form action="<?= _url('category', 'create') ?>" method="post">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<div class="form-group">
			<label class="group-name" for="new-category"><?= _t('sub.category') ?></label>
			<div class="group-controls">
				<input id="new-category" name="new-category" type="text" required="required" autocomplete="off" />
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.add') ?></button>
			</div>
		</div>
	</form>

	<h2><?= _t('sub.title.add_feed') ?></h2>
	<form id="add_rss" method="post" action="<?= _url('feed', 'add') ?>" autocomplete="off">

		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />

		<div class="form-group">
			<label class="group-name" for="url_rss"><?= _t('sub.feed.url') ?></label>
			<div class="group-controls">
				<div class="stick">
					<input id="url_rss" name="url_rss" type="url" required="required" autocomplete="off" class="long" autofocus="autofocus" />
					<a class="btn open-url" target="_blank" rel="noreferrer" href="" data-input="url_rss" title="<?= _t('gen.action.open_url') ?>"><?= _i('link') ?></a>
				</div>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name" for="category"><?= _t('sub.category') ?></label>
			<div class="group-controls">
				<select name="category" id="category">
				<?php
					foreach ($this->categories as $cat) {
						if ($cat->kind() == FreshRSS_Category::KIND_DYNAMIC_OPML) {
							continue;
						}
				?>
				<option value="<?= $cat->id() ?>"<?= $cat->id() === (Minz_Request::paramInt('cat_id') ?: 1) ? ' selected="selected"' : '' ?>>
					<?= $cat->name() ?>
				</option>
				<?php } ?>
				</select>
			</div>
		</div>

		<details class="form-advanced">
			<summary class="form-advanced-title">
				<?= _t('sub.feed.kind') ?>
			</summary>

			<div class="form-group">
				<label class="group-name" for="feed_kind"><?= _t('sub.feed.kind') ?></label>
				<div class="group-controls">
					<select name="feed_kind" id="feed_kind" class="select-show">
						<option value="<?= FreshRSS_Feed::KIND_RSS ?>" selected="selected"><?= _t('sub.feed.kind.rss') ?></option>
						<option value="<?= FreshRSS_Feed::KIND_HTML_XPATH ?>" data-show="html_xpath"><?= _t('sub.feed.kind.html_xpath') ?></option>
						<option value="<?= FreshRSS_Feed::KIND_XML_XPATH ?>" data-show="html_xpath"><?= _t('sub.feed.kind.xml_xpath') ?></option>
						<option value="<?= FreshRSS_Feed::KIND_JSONFEED ?>"><?= _t('sub.feed.kind.jsonfeed') ?></option>
						<option value="<?= FreshRSS_Feed::KIND_JSON_DOTNOTATION ?>" data-show="json_dotnotation"><?= _t('sub.feed.kind.json_dotnotation') ?></option>
						<option value="<?= FreshRSS_Feed::KIND_HTML_XPATH_JSON_DOTNOTATION ?>" data-show="json_dotnotation xPathToJsonGroup"><?= _t('sub.feed.kind.html_json') ?></option>
					</select>
				</div>
			</div>

			<fieldset id="html_xpath">
				<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.html_xpath.help') ?></p>
				<div class="form-group">
					<label class="group-name" for="xPathFeedTitle"><small><?= _t('sub.feed.kind.html_xpath.xpath') ?></small><br />
						<?= _t('sub.feed.kind.html_xpath.feed_title') ?></label>
					<div class="group-controls">
						<textarea class="valid-xpath" name="xPathFeedTitle" id="xPathFeedTitle" rows="2" cols="64" spellcheck="false">//title</textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.html_xpath.feed_title.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="xPathItem"><small><?= _t('sub.feed.kind.html_xpath.xpath') ?></small><br />
						<?= _t('sub.feed.kind.html_xpath.item') ?></label>
					<div class="group-controls">
						<textarea class="valid-xpath" name="xPathItem" id="xPathItem" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.html_xpath.item.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="xPathItemTitle"><small><?= _t('sub.feed.kind.html_xpath.relative') ?></small><br />
						<?= _t('sub.feed.kind.html_xpath.item_title') ?></label>
					<div class="group-controls">
						<textarea class="valid-xpath" name="xPathItemTitle" id="xPathItemTitle" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.html_xpath.item_title.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="xPathItemContent"><small><?= _t('sub.feed.kind.html_xpath.relative') ?></small><br />
						<?= _t('sub.feed.kind.html_xpath.item_content') ?></label>
					<div class="group-controls">
						<textarea class="valid-xpath" name="xPathItemContent" id="xPathItemContent" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.html_xpath.item_content.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="xPathItemUri"><small><?= _t('sub.feed.kind.html_xpath.relative') ?></small><br />
						<?= _t('sub.feed.kind.html_xpath.item_uri') ?></label>
					<div class="group-controls">
						<textarea class="valid-xpath" name="xPathItemUri" id="xPathItemUri" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.html_xpath.item_uri.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="xPathItemThumbnail"><small><?= _t('sub.feed.kind.html_xpath.relative') ?></small><br />
						<?= _t('sub.feed.kind.html_xpath.item_thumbnail') ?></label>
					<div class="group-controls">
						<textarea class="valid-xpath" name="xPathItemThumbnail" id="xPathItemThumbnail" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.html_xpath.item_thumbnail.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="xPathItemAuthor"><small><?= _t('sub.feed.kind.html_xpath.relative') ?></small><br />
						<?= _t('sub.feed.kind.html_xpath.item_author') ?></label>
					<div class="group-controls">
						<textarea class="valid-xpath" name="xPathItemAuthor" id="xPathItemAuthor" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.html_xpath.item_author.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="xPathItemTimestamp"><small><?= _t('sub.feed.kind.html_xpath.relative') ?></small><br />
						<?= _t('sub.feed.kind.html_xpath.item_timestamp') ?></label>
					<div class="group-controls">
						<textarea class="valid-xpath" name="xPathItemTimestamp" id="xPathItemTimestamp" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.html_xpath.item_timestamp.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="xPathItemTimeFormat">
						<?= _t('sub.feed.kind.html_xpath.item_timeFormat') ?></label>
					<div class="group-controls">
						<textarea name="xPathItemTimeFormat" id="xPathItemTimeFormat" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.html_xpath.item_timeFormat.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="xPathItemCategories"><small><?= _t('sub.feed.kind.html_xpath.relative') ?></small><br />
						<?= _t('sub.feed.kind.html_xpath.item_categories') ?></label>
					<div class="group-controls">
						<textarea class="valid-xpath" name="xPathItemCategories" id="xPathItemCategories" rows="2" cols="64" spellcheck="false"></textarea>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="xPathItemUid"><small><?= _t('sub.feed.kind.html_xpath.relative') ?></small><br />
						<?= _t('sub.feed.kind.html_xpath.item_uid') ?></label>
					<div class="group-controls">
						<textarea class="valid-xpath" name="xPathItemUid" id="xPathItemUid" rows="2" cols="64" spellcheck="false"></textarea>
					</div>
				</div>
			</fieldset>
			<fieldset id="json_dotnotation">
				<div class="form-group" id="xPathToJsonGroup">
					<label class="group-name" for="xPathToJson"><?= _t('sub.feed.kind.html_json.xpath') ?></label>
					<div class="group-controls">
						<textarea class="valid-xpath" name="xPathToJson" id="xPathToJson" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.html_json.xpath.help') ?></p>
					</div>
				</div>

				<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.json_dotnotation.help') ?></p>
				<div class="form-group">
					<label class="group-name" for="jsonFeedTitle"><small><?= _t('sub.feed.kind.json_dotnotation.json') ?></small><br />
						<?= _t('sub.feed.kind.json_dotnotation.feed_title') ?></label>
					<div class="group-controls">
						<textarea class="valid-json" name="jsonFeedTitle" id="jsonFeedTitle" rows="2" cols="64" spellcheck="false">title</textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.json_dotnotation.feed_title.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="jsonItem"><small><?= _t('sub.feed.kind.json_dotnotation.json') ?></small><br />
						<?= _t('sub.feed.kind.json_dotnotation.item') ?></label>
					<div class="group-controls">
						<textarea class="valid-json" name="jsonItem" id="jsonItem" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.json_dotnotation.item.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="jsonItemTitle"><small><?= _t('sub.feed.kind.json_dotnotation.relative') ?></small><br />
						<?= _t('sub.feed.kind.json_dotnotation.item_title') ?></label>
					<div class="group-controls">
						<textarea class="valid-json" name="jsonItemTitle" id="jsonItemTitle" rows="2" cols="64" spellcheck="false"></textarea>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="jsonItemContent"><small><?= _t('sub.feed.kind.json_dotnotation.relative') ?></small><br />
						<?= _t('sub.feed.kind.json_dotnotation.item_content') ?></label>
					<div class="group-controls">
						<textarea class="valid-json" name="jsonItemContent" id="jsonItemContent" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.json_dotnotation.item_content.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="jsonItemUri"><small><?= _t('sub.feed.kind.json_dotnotation.relative') ?></small><br />
						<?= _t('sub.feed.kind.json_dotnotation.item_uri') ?></label>
					<div class="group-controls">
						<textarea class="valid-json" name="jsonItemUri" id="jsonItemUri" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.json_dotnotation.item_uri.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="jsonItemThumbnail"><small><?= _t('sub.feed.kind.json_dotnotation.relative') ?></small><br />
						<?= _t('sub.feed.kind.json_dotnotation.item_thumbnail') ?></label>
					<div class="group-controls">
						<textarea class="valid-json" name="jsonItemThumbnail" id="jsonItemThumbnail" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.json_dotnotation.item_thumbnail.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="jsonItemAuthor"><small><?= _t('sub.feed.kind.json_dotnotation.relative') ?></small><br />
						<?= _t('sub.feed.kind.json_dotnotation.item_author') ?></label>
					<div class="group-controls">
						<textarea class="valid-json" name="jsonItemAuthor" id="jsonItemAuthor" rows="2" cols="64" spellcheck="false"></textarea>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="jsonItemTimestamp"><small><?= _t('sub.feed.kind.json_dotnotation.relative') ?></small><br />
						<?= _t('sub.feed.kind.json_dotnotation.item_timestamp') ?></label>
					<div class="group-controls">
						<textarea class="valid-json" name="jsonItemTimestamp" id="jsonItemTimestamp" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.json_dotnotation.item_timestamp.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="jsonItemTimeFormat">
						<?= _t('sub.feed.kind.json_dotnotation.item_timeFormat') ?></label>
					<div class="group-controls">
						<textarea name="jsonItemTimeFormat" id="jsonItemTimeFormat" rows="2" cols="64" spellcheck="false"></textarea>
						<p class="help"><?= _i('help') ?> <?= _t('sub.feed.kind.json_dotnotation.item_timeFormat.help') ?></p>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="jsonItemCategories"><small><?= _t('sub.feed.kind.json_dotnotation.relative') ?></small><br />
						<?= _t('sub.feed.kind.json_dotnotation.item_categories') ?></label>
					<div class="group-controls">
						<textarea class="valid-json" name="jsonItemCategories" id="jsonItemCategories" rows="2" cols="64" spellcheck="false"></textarea>
					</div>
				</div>
				<div class="form-group">
					<label class="group-name" for="jsonItemUid"><small><?= _t('sub.feed.kind.json_dotnotation.relative') ?></small><br />
						<?= _t('sub.feed.kind.json_dotnotation.item_uid') ?></label>
					<div class="group-controls">
						<textarea class="valid-json" name="jsonItemUid" id="jsonItemUid" rows="2" cols="64" spellcheck="false"></textarea>
					</div>
				</div>
			</fieldset>
		</details>

		<details class="form-advanced">
			<summary class="form-advanced-title">
				<?= _t('sub.feed.advanced') ?>
			</summary>

			<div class="form-group">
				<label class="group-name" for="http_user"><?= _t('sub.feed.auth.username') ?></label>
				<div class="group-controls">
					<input id="http_user" name="http_user" type="text" autocomplete="off"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="http_pass"><?= _t('sub.feed.auth.password') ?></label>
				<div class="group-controls">
					<div class="stick">
						<input id="http_pass" name="http_pass" type="password" value="" autocomplete="new-password" />
						<button type="button" class="btn toggle-password" data-toggle="http_pass"><?= _i('key') ?></button>
					</div>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="curl_params_cookie"><?= _t('sub.feed.css_cookie') ?></label>
				<div class="group-controls">
					<input type="text" name="curl_params_cookie" id="curl_params_cookie" value="" placeholder="<?= _t('gen.short.blank_to_disable') ?>" />
					<p class="help"><?= _i('help') ?> <?= _t('sub.feed.css_cookie_help') ?></p>
					<label for="curl_params_cookiefile">
						<input type="checkbox" name="curl_params_cookiefile" id="curl_params_cookiefile" value="1" />
						<?= _t('sub.feed.accept_cookies') ?>
					</label>
					<p class="help"><?= _i('help') ?> <?= _t('sub.feed.accept_cookies_help') ?></p>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="curl_params_redirects"><?= _t('sub.feed.max_http_redir') ?></label>
				<div class="group-controls">
					<input type="number" name="curl_params_redirects" id="curl_params_redirects" min="-1" value="" placeholder="<?= _t('gen.short.blank_to_disable') ?>" />
					<p class="help"><?= _i('help') ?> <?= _t('sub.feed.max_http_redir_help') ?></p>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="curl_params_useragent"><?= _t('sub.feed.useragent') ?></label>
				<div class="group-controls">
					<input type="text" name="curl_params_useragent" id="curl_params_useragent" value="" placeholder="<?= _t('gen.short.by_default') ?>" />
					<p class="help"><?= _i('help') ?> <?= _t('sub.feed.useragent_help') ?></p>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="proxy_type"><?= _t('sub.feed.proxy') ?></label>
				<div class="group-controls">
					<select class="number" name="proxy_type" id="proxy_type"><?php
						defined('CURLPROXY_HTTPS') or define('CURLPROXY_HTTPS', 2);	// Compatibility cURL 7.51
						foreach (['' => '', -1 => 'NONE', CURLPROXY_HTTP => 'HTTP', CURLPROXY_HTTPS => 'HTTPS',
							CURLPROXY_SOCKS4 => 'SOCKS4', CURLPROXY_SOCKS4A => 'SOCKS4A', CURLPROXY_SOCKS5 => 'SOCKS5',
							CURLPROXY_SOCKS5_HOSTNAME => 'SOCKS5H'] as $k => $v) {
							echo '<option value="' . $k . '">' . $v . '</option>';
						}
					?>
					</select>
					<input type="text" name="curl_params" id="curl_params" value="" placeholder="<?= _t('gen.short.by_default') ?>" />
					<p class="help"><?= _i('help') ?> <?= _t('sub.feed.proxy_help') ?></p>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="curl_method"><?= _t('sub.feed.method') ?></label>
				<div class="group-controls">
					<select class="number" name="curl_method" id="curl_method"><?php
					foreach (['GET' => 'GET', 'POST' => 'POST'] as $k => $v) {
						echo '<option value="' . $k . '">' . $v . '</option>';
					}
					?>
					</select>
					<div class="stick">
						<input type="text" name="curl_fields" id="curl_fields" value="" placeholder="<?= _t('sub.feed.method_postparams') ?>" />
					</div>
					<p class="help"><?= _i('help') ?> <?= _t('sub.feed.method_help') ?></p>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="timeout"><?= _t('sub.feed.timeout') ?></label>
				<div class="group-controls">
					<input type="number" name="timeout" id="timeout" min="3" max="900" value="" placeholder="<?= _t('gen.short.by_default') ?>" />
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="ssl_verify"><?= _t('sub.feed.ssl_verify') ?></label>
				<div class="group-controls">
					<select name="ssl_verify" id="ssl_verify">
						<option value=""><?= _t('gen.short.by_default') ?></option>
						<option value="0"><?= _t('gen.short.no') ?></option>
						<option value="1"><?= _t('gen.short.yes') ?></option>
					</select>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="http_headers"><?= _t('sub.feed.http_headers') ?></label>
				<div class="group-controls">
					<textarea class="valid-json" id="http_headers" name="http_headers" rows="3" cols="64" spellcheck="false"></textarea>
					<p class="help"><?= _i('help') ?> <?= _t('sub.feed.http_headers_help') ?></p>
				</div>
			</div>
		</details>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.add') ?></button>
			</div>
		</div>
	</form>

	<h2>
		<?= _t('sub.title.add_dynamic_opml') ?>
		<?= _i('opml-dyn') ?>
	</h2>
	<form action="<?= _url('category', 'create') ?>" method="post">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<div class="form-group">
			<label class="group-name" for="new-category"><?= _t('sub.title.add_opml_category') ?></label>
			<div class="group-controls">
				<input id="new-category" name="new-category" type="text" required="required" autocomplete="off" />
			</div>
		</div>

		<div class="form-group">
			<label class="group-name" for="opml_url"><?= _t('sub.category.opml_url') ?></label>
			<div class="group-controls">
				<div class="stick">
					<input id="opml_url" name="opml_url" type="url" required="required" autocomplete="off" class="long" />
					<a class="btn open-url" target="_blank" rel="noreferrer" href="" data-input="opml_url" title="<?= _t('gen.action.open_url') ?>"><?= _i('link') ?></a>
				</div>
				<p class="help"><?= _i('help') ?> <?= _t('sub.category.dynamic_opml.help') ?></p>
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.add') ?></button>
			</div>
		</div>
	</form>
</main>
