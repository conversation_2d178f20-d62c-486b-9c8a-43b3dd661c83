<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_subscription');
?>
<main class="post">
	<h1><?= _t('sub.menu.label_management') ?></h1>

	<h2><?= _t('sub.title.add_label') ?></h2>
	<form id="add_tag" method="post" action="<?= _url('tag', 'add') ?>" autocomplete="off">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<div class="form-group">
			<label class="group-name" for="new_label_name"><?= _t('sub.tag.name') ?></label>
			<div class="group-controls">
				<input id="new_label_name" name="name" type="text" autocomplete="off" required="required" />
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.add') ?></button>
			</div>
		</div>
	</form>

	<?php if (count($this->tags) > 0): ?>
	<h2><?= _t('gen.action.manage') ?></h2>
	<ul id="tagsList" <?= (count($this->tags) > 11) ? 'class="listInColumns"' : '' ?>>
	<?php foreach ($this->tags as $tag): ?>
		<li>
			<a href="<?= _url('tag', 'update', 'id', $tag->id()) ?>" class="configure open-slider" title="<?= _t('gen.action.manage') ?>">
				<?= _i('configure') ?>
			</a>
			<?= $tag->name() ?>
		</li>
	<?php endforeach; ?>
	</ul>
	<?php endif; ?>
</main>

<?php $class = isset($this->tag) ? ' active' : ''; ?>
<aside id="slider" class="<?= $class ?>">
	<a class="toggle_aside" href="#close"><img class="icon" src="../themes/icons/close.svg" loading="lazy" alt="❌"></a>
	<div id="slider-content">
		<?php
			if (isset($this->tag)) {
				$this->renderHelper('tag/update');
			}
		?>
	</div>
</aside>
<a href="#" id="close-slider">
	<?= _i('close') ?>
</a>
