<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
?>
<main class="post">
	<p>
		<?= _t('user.email.validation.need_to', FreshRSS_Context::systemConf()->title) ?>
	</p>

	<p>
		<?= _t('user.email.validation.email_sent_to', FreshRSS_Context::userConf()->mail_login) ?>
	</p>

	<form action="<?= _url('user', 'sendValidationEmail') ?>" method="post">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<button type="submit" class="btn">
			<?= _t('user.email.validation.resend_email') ?>
		</button>
	</form>

	<p>
		<small>
			<?= _t('user.email.validation.change_email', _url('user', 'profile')) ?>
		</small>
	</p>
</main>
