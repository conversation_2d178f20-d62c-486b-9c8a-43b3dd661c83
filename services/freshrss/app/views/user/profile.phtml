<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	if (!$this->disable_aside) {
		$this->partial('aside_configure');
	}
?>

<main class="post">
	<form method="post" action="<?= _url('user', 'profile') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<h1><?= _t('conf.profile') ?></h1>

		<div class="form-group">
			<label class="group-name" for="current_user"><?= _t('conf.user.current') ?></label>
			<div class="group-controls">
				<input id="current_user" type="text" disabled="disabled" value="<?= Minz_User::name() ?>" />
			</div>
		</div>

		<?php if (FreshRSS_Auth::hasAccess('admin')) { ?>
			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="is_admin">
						<input type="checkbox" id="is_admin" disabled="disabled" checked="checked" />
						<?= _t('conf.user.is_admin') ?>
					</label>
				</div>
			</div>
		<?php } ?>

		<div class="form-group">
			<label class="group-name" for="email"><?= _t('conf.profile.email') ?></label>
			<div class="group-controls">
				<input id="email" name="email" type="email" value="<?= FreshRSS_Context::userConf()->mail_login ?>" />
			</div>
		</div>

		<div class="form-group">
			<label class="group-name" for="newPasswordPlain"><?= _t('conf.profile.password_form') ?></label>
			<div class="group-controls">
				<div class="stick">
					<input type="password" id="newPasswordPlain" name="newPasswordPlain" autocomplete="new-password"
						pattern=".{7,}" <?= cryptAvailable() ? '' : 'disabled="disabled" ' ?>/>
					<button type="button" class="btn toggle-password" data-toggle="newPasswordPlain"><?= _i('key') ?></button>
				</div>
				<p class="help"><?= _i('help') ?> <?= _t('conf.profile.password_format') ?></p>
				<noscript><b><?= _t('gen.js.should_be_activated') ?></b></noscript>
			</div>
		</div>

		<?php if (FreshRSS_Auth::accessNeedsAction()) { ?>
		<div class="form-group">
			<label class="group-name" for="token"><?= _t('admin.auth.token') ?></label>
			<?php $token = FreshRSS_Context::userConf()->token; ?>
			<div class="group-controls">
				<input type="text" id="token" name="token" value="<?= $token ?>" placeholder="<?= _t('gen.short.blank_to_disable') ?>" data-leave-validation="<?= $token ?>"/>
				<p class="help"><?= _i('help') ?> <?= _t('admin.auth.token_help') ?></p>
				<kbd><?= Minz_Url::display(['a' => 'rss', 'params' => ['user' => Minz_User::name() ?? '',
					'token' => $token, 'hours' => FreshRSS_Context::userConf()->since_hours_posts_per_rss]], 'html', true) ?></kbd>
				<p class="help"><?= _i('help') ?> <?= _t('conf.query.help') ?></a></p>
			</div>
		</div>
		<?php } ?>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.submit') ?></button>
				<button type="reset" class="btn"><?= _t('gen.action.cancel') ?></button>
			</div>
		</div>
	</form>


	<h2><?= _t('conf.profile.api') ?></h2>
	<?php if (FreshRSS_Context::systemConf()->api_enabled) { ?>
		<form method="post" action="<?= _url('api', 'updatePassword') ?>">
			<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />

			<div class="form-group">
				<label class="group-name" for="apiPasswordPlain"><?= _t('conf.profile.password_api') ?></label>
				<div class="group-controls">
					<div class="stick">
						<input type="password" id="apiPasswordPlain" name="apiPasswordPlain" autocomplete="new-password"
							<?php if (FreshRSS_Context::userConf()->apiPasswordHash != '') {?>
								placeholder="<?= _t('conf.profile.api.api_set') ?>"
							<?php } else {?>
								placeholder="<?= _t('conf.profile.api.api_not_set') ?>"
							<?php } ?>
							pattern=".{7,}" <?= cryptAvailable() ? '' : 'disabled="disabled" ' ?>/>
						<button type="button" class="btn toggle-password" data-toggle="apiPasswordPlain"><?= _i('key') ?></button>
					</div>
					<p class="help"><?= _i('help') ?> <?= _t('conf.profile.api.check_link', Minz_Url::display('/api/', 'html', true)) ?></p>
					<p class="help"><?= _i('help') ?> <?= _t('conf.profile.api.documentation_link') ?></p>
				</div>
			</div>

			<div class="form-group form-actions">
				<div class="group-controls">
					<button type="submit" class="btn btn-important"><?= _t('gen.action.submit') ?></button>
				</div>
			</div>
		</form>
	<?php } else { ?>
		<div class="form-group">
			<label class="group-name"></label>
			<div class="group-controls">
				<?= _t('conf.profile.api.disabled') ?>
				<p class="help"><?= _i('help') ?> <?= _t('conf.profile.api.help') ?></p>
			</div>
		</div>
	<?php } ?>

	<?php if (!FreshRSS_Auth::hasAccess('admin')) { ?>
	<h2><?= _t('conf.profile.delete') ?></h2>

	<form id="crypto-form" method="post" action="<?= _url('user', 'delete') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />

		<p class="alert alert-warn"><span class="alert-head"><?= _t('gen.short.attention') ?></span> <?= _t('conf.profile.delete.warn') ?></p>

		<div class="form-group">
			<label class="group-name" for="passwordPlain"><?= _t('gen.auth.password') ?></label>
			<div class="group-controls">
				<div class="stick">
					<input type="password" id="passwordPlain" required="required" />
					<button type="button" class="btn toggle-password" data-toggle="passwordPlain"><?= _i('key') ?></button>
				</div>
					<input type="hidden" id="challenge" name="challenge" /><br />
					<noscript><strong><?= _t('gen.js.should_be_activated') ?></strong></noscript>
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<?php
					$redirect_url = urlencode(Minz_Url::display(
						['c' => 'user', 'a' => 'profile'],
						'php', true
					));
				?>
				<input type="hidden" name="r" value="<?= $redirect_url ?>" />
				<input type="hidden" name="username" id="username" value="<?= Minz_User::name() ?>" />
				<button type="submit" class="btn btn-attention confirm"><?= _t('gen.action.remove') ?></button>
			</div>
		</div>
	</form>
	<?php } ?>
</main>
