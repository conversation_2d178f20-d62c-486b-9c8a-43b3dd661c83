<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_configure');
?>
<main class="post">
	<h1><?= _t('gen.menu.user_management') ?></h1>
	<h2><?= _t('admin.user.create') ?></h2>
	<form method="post" action="<?= _url('user', 'create') ?>" autocomplete="off">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<input type="hidden" name="originController" value="<?= Minz_Request::controllerName() ?>" />
		<input type="hidden" name="originAction" value="<?= Minz_Request::actionName() ?>" />

		<div class="form-group">
			<label class="group-name" for="new_user_language"><?= _t('admin.user.language') ?></label>
			<div class="group-controls">
				<select name="new_user_language" id="new_user_language">
				<?php $languages = Minz_Translate::availableLanguages(); ?>
				<?php foreach ($languages as $lang) { ?>
				<option value="<?= $lang ?>"<?= FreshRSS_Context::userConf()->language === $lang ?
					' selected="selected"' : '' ?>><?= _t('gen.lang.' . $lang) ?></option>
				<?php } ?>
				</select>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name" for="new_user_timezone"><?= _t('conf.display.timezone') ?></label>
			<div class="group-controls">
				<select name="new_user_timezone" id="new_user_timezone">
				<?php $timezones = array_merge([''], DateTimeZone::listIdentifiers()); ?>
				<?php foreach ($timezones as $timezone): ?>
				<option value="<?= $timezone ?>"<?= $timezone === '' ? ' selected="selected"' : '' ?>>
					<?= $timezone == '' ? _t('gen.short.by_default') . ' (' . FreshRSS_Context::defaultTimeZone() . ')' : $timezone ?>
				</option>
				<?php endforeach; ?>
				</select>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name" for="new_user_name"><?= _t('admin.user.username') ?></label>
			<div class="group-controls">
				<input id="new_user_name" name="new_user_name" type="text" size="16" required="required" autocomplete="off"
					pattern="<?= FreshRSS_user_Controller::USERNAME_PATTERN ?>" placeholder="demo" />
			</div>
		</div>

		<div class="form-group">
			<div class="group-controls">
				<label for="new_user_is_admin" class="checkbox">
					<input type="checkbox" name="new_user_is_admin" id="new_user_is_admin">
					<?= _t('admin.user.is_admin') ?>
				</label>
			</div>
		</div>

		<?php if ($this->show_email_field) { ?>
			<div class="form-group">
				<label class="group-name" for="new_user_email">
					<?= _t('gen.auth.email') ?>
				</label>
				<div class="group-controls">
					<input id="new_user_email" name="new_user_email" type="email" required="required" />
				</div>
			</div>
		<?php } ?>

		<div class="form-group">
			<label class="group-name" for="new_user_passwordPlain"><?= _t('admin.user.password_form') ?></label>
			<div class="group-controls">
				<div class="stick">
					<input type="password" id="new_user_passwordPlain" name="new_user_passwordPlain" autocomplete="new-password" pattern=".{7,}" />
					<button type="button" class="btn toggle-password" data-toggle="new_user_passwordPlain"><?= _i('key') ?></button>
				</div>
				<p class="help"><?= _i('help') ?> <?= _t('admin.user.password_format') ?></p>
				<noscript><b><?= _t('gen.js.should_be_activated') ?></b></noscript>
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.create') ?></button>
				<button type="reset" class="btn"><?= _t('gen.action.cancel') ?></button>
			</div>
		</div>
	</form>

	<h2><?= _t('admin.user.list'); ?></h2>
	<div class="table-wrapper scrollbar-thin">
		<table id="user-list">
			<thead>
				<tr>
					<th></th>
					<th><?= _t('admin.user.username') ?></th>
					<th><?= _t('admin.user.enabled') ?></th>
					<th><?= _t('admin.user.is_admin') ?></th>
					<th><?= _t('admin.user.email') ?></th>
					<th><?= _t('admin.user.language') ?></th>
					<th><?= _t('admin.user.feed_count') ?></th>
					<th><?= _t('admin.user.article_count') ?></th>
					<th><?= _t('admin.user.database_size') ?></th>
					<th><?= _t('admin.user.last_user_activity') ?></th>
				</tr>
			</thead>
			<tbody>
				<?php foreach ($this->users as $username => $values): ?>
					<tr <?= $values['is_default'] ? 'class="default-user"' : '' ?>>
						<td><a href="<?= _url('user', 'details', 'username', $username) ?>" class="configure open-slider" ><?= _i('configure') ?></a></td>
						<td><?= $username ?></td>
						<td><?= $values['enabled'] ? '✔' : ' ' ?></td>
						<td><?= $values['is_admin'] ? '✔' : ' ' ?></td>
						<td><?= $values['mail_login'] ?></td>
						<td><?= _t("gen.lang.{$values['language']}") ?></td>
						<td><?= format_number($values['feed_count']) ?></td>
						<td><?= format_number($values['article_count']) ?></td>
						<td><?= format_bytes($values['database_size']) ?></td>
						<td><?= $values['last_user_activity'] ?></td>
					</tr>
				<?php endforeach ?>
			</tbody>
		</table>
	</div>
</main>

<?php $class = ($this->query != null) ? ' active' : ''; ?>
<aside id="slider" class="<?= $class ?>">
<a class="toggle_aside" href="#close"><img class="icon" src="../themes/icons/close.svg" loading="lazy" alt="❌"></a>
	<div id="slider-content">
		<?php
			if ($this->query != null) {
				$this->renderHelper('configure/query');
			}
		?>
	</div>
</aside>
<a href="#" id="close-slider">
	<?= _i('close') ?>
</a>
