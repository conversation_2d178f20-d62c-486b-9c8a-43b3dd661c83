<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	if (!Minz_Request::paramBoolean('ajax')) {
		$this->partial('aside_configure');
	}
	$isDefault = $this->details['is_default'];
	$isAdmin = $this->details['is_admin'];
	$enabled = $this->details['enabled'];
?>
<div class="post">
	<h2><?= $this->username ?><?php if ($isAdmin) echo ' ― ', _t('admin.user.admin'); ?></h2>
	<form method="post" action="<?= _url('user', 'manage', 'username', $this->username); ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken(); ?>" />

		<div class="form-group">
			<label class="group-name"><?= _t('admin.user.email') ?></label>
			<div class="group-controls">
				<?= $this->details['mail_login'] ?>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name"><?= _t('admin.user.language') ?></label>
			<div class="group-controls">
				<?= _t("gen.lang.{$this->details['language']}") ?>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name"><?= _t('admin.user.feed_count') ?></label>
			<div class="group-controls">
				<?= format_number($this->details['feed_count'] ?: 0) ?>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name"><?= _t('admin.user.article_count') ?></label>
			<div class="group-controls">
				<?= format_number($this->details['article_count'] ?: 0) ?>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name"><?= _t('admin.user.database_size') ?></label>
			<div class="group-controls">
				<?= format_bytes($this->details['database_size']) ?>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name"><?= _t('admin.user.last_user_activity') ?></label>
			<div class="group-controls">
				<?= $this->details['last_user_activity'] ?>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name" for="newPasswordPlain"><?= _t('admin.user.password_form') ?></label>
			<div class="group-controls">
				<div class="stick">
					<input type="password" id="newPasswordPlain" name="newPasswordPlain" autocomplete="new-password"
						pattern=".{7,}" <?= cryptAvailable() ? '' : 'disabled="disabled" ' ?>/>
					<button type="button" class="btn toggle-password" data-toggle="newPasswordPlain"><?= _i('key') ?></button>
				</div>
				<p class="help"><?= _i('help'); ?> <?= _t('admin.user.password_format') ?></p>
			</div>
		</div>

		<div class="form-group form-actions">
			<noscript><b><?= _t('gen.js.should_be_activated'); ?></b></noscript>
			<div class="group-controls">
				<button type="submit" class="btn btn-important" name="action" value="update"><?= _t('gen.action.update') ?></button>
				<button type="submit" class="btn btn-attention confirm" name="action" value="purge"><?= _t('gen.action.purge') ?></button>
				<button type="submit" class="btn btn-attention confirm" name="action" value="delete"><?= _t('gen.action.remove') ?></button>
				<?php if ($isAdmin && !$isDefault): ?>
					<button type="submit" class="btn btn-attention confirm" name="action" value="demote"><?= _t('gen.action.demote') ?></button>
				<?php elseif (!$isAdmin): ?>
					<button type="submit" class="btn btn-attention confirm" name="action" value="promote"><?= _t('gen.action.promote') ?></button>
				<?php endif; ?>
				<?php if ($enabled && !$isDefault): ?>
					<button type="submit" class="btn btn-attention" name="action" value="disable"><?= _t('gen.action.disable') ?></button>
				<?php elseif (!$enabled): ?>
					<button type="submit" class="btn btn-attention" name="action" value="enable"><?= _t('gen.action.enable') ?></button>
				<?php endif; ?>
			<div>
		</div>
	</form>
</div>
