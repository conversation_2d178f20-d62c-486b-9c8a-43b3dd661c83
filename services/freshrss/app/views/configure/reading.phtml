<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_configure');
?>
<main class="post">
	<h1><?= _t('conf.reading') ?></h1>

	<form method="post" action="<?= _url('configure', 'reading') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<fieldset>
			<legend><?= _t('conf.reading.headline.view') ?></legend>
			<div class="form-group">
				<label class="group-name" for="view_mode"><?= _t('conf.reading.view.default') ?></label>
				<div class="group-controls">
					<select name="view_mode" id="view_mode" data-leave-validation="<?= FreshRSS_Context::userConf()->view_mode ?>">
						<?php
						/** @var FreshRSS_View $this */
						/** @var list<FreshRSS_ViewMode>|null $viewModes */
						$viewModes = isset($this->viewModes) && is_array($this->viewModes) ? $this->viewModes : null;
						if ($viewModes === null) {
							$viewModes = FreshRSS_ViewMode::getAllModes();
						}
						foreach ($viewModes as $mode):
						?>
							<option value="<?= htmlspecialchars($mode->id(), ENT_COMPAT, 'UTF-8') ?>"<?= FreshRSS_Context::userConf()->view_mode === $mode->id() ? ' selected="selected"' : '' ?>>
								<?= htmlspecialchars($mode->name(), ENT_NOQUOTES, 'UTF-8') ?>
							</option>
						<?php endforeach; ?>
					</select>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="default_view"><?= _t('conf.reading.show') ?></label>
				<div class="group-controls">
					<select name="default_view" id="default_view" data-leave-validation="<?= FreshRSS_Context::userConf()->default_view ?>">
						<option value="unread"<?= FreshRSS_Context::userConf()->default_view === 'unread' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.show.unread') ?></option>
						<option value="adaptive"<?= FreshRSS_Context::userConf()->default_view === 'adaptive' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.show.adaptive') ?></option>
						<option value="unread_or_favorite"<?= FreshRSS_Context::userConf()->default_view === 'unread_or_favorite' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.show.unread_or_favorite') ?></option>
						<option value="all"<?= FreshRSS_Context::userConf()->default_view === 'all' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.show.all_articles') ?></option>
					</select>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="posts_per_page"><?= _t('conf.reading.articles_per_page') ?></label>
				<div class="group-controls">
					<input type="number" id="posts_per_page" name="posts_per_page" value="<?=
						FreshRSS_Context::userConf()->posts_per_page ?>" min="5" max="500"
						data-leave-validation="<?= FreshRSS_Context::userConf()->posts_per_page ?>"/>
					<p class="help"><?= _i('help') ?> <?= _t('conf.reading.number_divided_when_reader') ?></p>
				</div>
			</div>

			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="auto_load_more">
						<input type="checkbox" name="auto_load_more" id="auto_load_more" value="1"<?=
							FreshRSS_Context::userConf()->auto_load_more ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->auto_load_more ?>"/>
						<?= _t('conf.reading.auto_load_more') ?>
						<noscript> — <strong><?= _t('gen.js.should_be_activated') ?></strong></noscript>
					</label>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="mark_read_button"><?= _t('conf.mark_read_button') ?></label>
				<div class="group-controls">
					<select name="mark_read_button" id="mark_read_button" data-leave-validation="<?= FreshRSS_Context::userConf()->mark_read_button ?>">
						<option value="big"<?= FreshRSS_Context::userConf()->mark_read_button === 'big' ? ' selected="selected"' : '' ?>><?= _t('conf.mark_read_button.big') ?></option>
						<option value="small"<?= FreshRSS_Context::userConf()->mark_read_button === 'small' ? ' selected="selected"' : '' ?>><?= _t('conf.mark_read_button.small') ?></option>
						<option value="none"<?= FreshRSS_Context::userConf()->mark_read_button === 'none' ? ' selected="selected"' : '' ?>><?= _t('conf.mark_read_button.none') ?></option>
					</select>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="sort_order"><?= _t('conf.reading.sort') ?></label>
				<div class="group-controls">
					<select name="sort_order" id="sort_order" data-leave-validation="<?= FreshRSS_Context::userConf()->sort_order ?>">
						<option value="DESC"<?= FreshRSS_Context::userConf()->sort_order === 'DESC' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.sort.newer_first') ?></option>
						<option value="ASC"<?= FreshRSS_Context::userConf()->sort_order === 'ASC' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.sort.older_first') ?></option>
					</select>
				</div>
			</div>

		</fieldset>

		<fieldset>
			<legend><?= _t('conf.reading.headline.categories') ?></legend>
			<div class="form-group">
				<label class="group-name" for="display_categories"><?= _t('conf.reading.display_categories_unfolded') ?></label>
				<div class="group-controls">
					<select name="display_categories" id="display_categories" data-leave-validation="<?= FreshRSS_Context::userConf()->display_categories ?>">
						<option value="active"<?= FreshRSS_Context::userConf()->display_categories === 'active' ? ' selected="selected"' : '' ?>><?=
							_t('conf.reading.show.active_category') ?></option>
						<option value="remember"<?= FreshRSS_Context::userConf()->display_categories === 'remember' ? ' selected="selected"' : '' ?>><?=
							_t('conf.reading.show.remember_categories') ?></option>
						<option value="all"<?= FreshRSS_Context::userConf()->display_categories === 'all' ? ' selected="selected"' : '' ?>><?=
							_t('conf.reading.show.all_categories') ?></option>
						<option value="none"<?= FreshRSS_Context::userConf()->display_categories === 'none' ? ' selected="selected"' : '' ?>><?=
							_t('conf.reading.show.no_category') ?></option>
					</select>
				</div>
			</div>

			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="show_fav_unread">
						<input type="checkbox" name="show_fav_unread" id="show_fav_unread" value="1"<?=
							FreshRSS_Context::userConf()->show_fav_unread ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->show_fav_unread ?>"/>
						<?= _t('conf.reading.always_show_favorites') ?>
						<p class="help"><?= _i('help') ?> <?= _t('conf.reading.show_fav_unread_help') ?></p>
					</label>
				</div>
			</div>

			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="hide_read_feeds">
						<input type="checkbox" name="hide_read_feeds" id="hide_read_feeds" value="1"<?=
							FreshRSS_Context::userConf()->hide_read_feeds ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->hide_read_feeds ?>"/>
						<?= _t('conf.reading.hide_read_feeds') ?>
					</label>
				</div>
			</div>
		</fieldset>

		<fieldset>
			<legend><?= _t('conf.reading.headline.articles_header_footer') ?></legend>
			<div class="form-group">
				<label class="group-name" for="show_feed_name"><?= _t('conf.reading.article.feed_title') ?></label>
				<div class="group-controls">
					<select name="show_feed_name" id="show_feed_name" data-leave-validation="<?= FreshRSS_Context::userConf()->show_feed_name ?>">
						<option value="0"<?= FreshRSS_Context::userConf()->show_feed_name === '0' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.article.feed_name.none') ?></option>
						<option value="t"<?= FreshRSS_Context::userConf()->show_feed_name === 't' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.article.feed_name.above_title') ?></option>
						<option value="a"<?= FreshRSS_Context::userConf()->show_feed_name === 'a' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.article.feed_name.with_authors') ?></option>
					</select>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="show_author_date"><?= _t('conf.reading.article.authors_date') ?></label>
				<div class="group-controls">
					<select name="show_author_date" id="show_author_date" data-leave-validation="<?= FreshRSS_Context::userConf()->show_author_date ?>">
						<option value="0" <?= FreshRSS_Context::userConf()->show_author_date === '0' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.article.authors_date.none') ?></option>
						<option value="h" <?= FreshRSS_Context::userConf()->show_author_date === 'h' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.article.authors_date.header') ?></option>
						<option value="f" <?= FreshRSS_Context::userConf()->show_author_date === 'f' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.article.authors_date.footer') ?></option>
						<option value="b" <?= FreshRSS_Context::userConf()->show_author_date === 'b' ? ' selected="selected"' : '' ?>><?= _t('conf.reading.article.authors_date.both') ?></option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="group-name" for="show_article_icons"><?= _t('conf.reading.article.icons') ?></label>
				<div class="group-controls">
					<select name="show_article_icons" id="show_article_icons" data-leave-validation="<?= FreshRSS_Context::userConf()->show_article_icons ?>">
						<option value="t" <?= FreshRSS_Context::userConf()->show_article_icons === 't' ? ' selected="selected"' : '' ?> data-input-visible="true"><?= _t('conf.reading.article.icons.above_title') ?></option>
						<option value="a" <?= FreshRSS_Context::userConf()->show_article_icons === 'a' ? ' selected="selected"' : '' ?> data-input-visible="true"><?= _t('conf.reading.article.icons.with_authors') ?></option>
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="group-name" for="show_tags"><?= _t('conf.reading.article.tags') ?></label>
				<div class="group-controls">
					<select class="select-input-changer" name="show_tags" id="show_tags" data-name="show_tags_max" data-leave-validation="<?= FreshRSS_Context::userConf()->show_tags ?>">
						<option value="0" <?= FreshRSS_Context::userConf()->show_tags === '0' ? ' selected="selected"' : '' ?> data-input-visible="false"><?= _t('conf.reading.article.tags.none') ?></option>
						<option value="h" <?= FreshRSS_Context::userConf()->show_tags === 'h' ? ' selected="selected"' : '' ?> data-input-visible="true"><?= _t('conf.reading.article.tags.header') ?></option>
						<option value="f" <?= FreshRSS_Context::userConf()->show_tags === 'f' ? ' selected="selected"' : '' ?> data-input-visible="true"><?= _t('conf.reading.article.tags.footer') ?></option>
						<option value="b" <?= FreshRSS_Context::userConf()->show_tags === 'b' ? ' selected="selected"' : '' ?> data-input-visible="true"><?= _t('conf.reading.article.tags.both') ?></option>
					</select>
					<noscript> — <strong><?= _t('gen.js.should_be_activated') ?></strong></noscript>
				</div>
			</div>
			<div class="form-group" id="show_tags_max-block">
				<label class="group-name" for="show_tags_max"><?= _t('conf.reading.article.tags_max') ?></label>
				<div class="group-controls">
					<input type="number" id="show_tags_max" name="show_tags_max" value="<?= FreshRSS_Context::userConf()->show_tags_max ?>" min="0" data-leave-validation="<?= FreshRSS_Context::userConf()->show_tags_max ?>" data-number="2" />
					<p class="help"><?= _i('help') ?> <?= _t('conf.reading.article.tags_max.help') ?></p>
				</div>
			</div>
		</fieldset>

		<fieldset>
			<legend><?= _t('conf.reading.headline.articles') ?></legend>
			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="display_posts">
						<input type="checkbox" name="display_posts" id="display_posts" value="1"<?=
							FreshRSS_Context::userConf()->display_posts ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->display_posts ?>"/>
						<?= _t('conf.reading.display_articles_unfolded') ?>
						<noscript> — <strong><?= _t('gen.js.should_be_activated') ?></strong></noscript>
					</label>
				</div>
			</div>

			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="sticky_post">
						<input type="checkbox" name="sticky_post" id="sticky_post" value="1"<?=
							FreshRSS_Context::userConf()->sticky_post ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->sticky_post ?>"/>
						<?= _t('conf.reading.sticky_post') ?>
						<noscript> — <strong><?= _t('gen.js.should_be_activated') ?></strong></noscript>
					</label>
				</div>
			</div>

			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="sides_close_article">
						<input type="checkbox" name="sides_close_article" id="sides_close_article" value="1"<?=
							FreshRSS_Context::userConf()->sides_close_article ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->sides_close_article ?>"/>
						<?= _t('conf.reading.sides_close_article') ?>
						<noscript> — <strong><?= _t('gen.js.should_be_activated') ?></strong></noscript>
					</label>
				</div>
			</div>
		</fieldset>

		<fieldset>
			<legend><?= _t('conf.reading.headline.mark_as_read') ?></legend>
			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="auto_remove_article">
						<input type="checkbox" name="auto_remove_article" id="auto_remove_article" value="1"<?=
							FreshRSS_Context::userConf()->auto_remove_article ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->auto_remove_article ?>"/>
						<?= _t('conf.reading.auto_remove_article') ?>
						<noscript> — <strong><?= _t('gen.js.should_be_activated') ?></strong></noscript>
					</label>
				</div>
			</div>

			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="reading_confirm">
						<input type="checkbox" name="reading_confirm" id="reading_confirm" value="1"<?=
							FreshRSS_Context::userConf()->reading_confirm ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->reading_confirm ?>"/>
						<?= _t('conf.reading.confirm_enabled') ?>
						<noscript> — <strong><?= _t('gen.js.should_be_activated') ?></strong></noscript>
					</label>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="onread_jump_next"><?= _t('conf.reading.after_onread') ?></label>
				<div class="group-controls">
					<label class="checkbox" for="onread_jump_next">
						<input type="checkbox" name="onread_jump_next" id="onread_jump_next" value="1"<?=
							FreshRSS_Context::userConf()->onread_jump_next ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->onread_jump_next ?>"/>
						<?= _t('conf.reading.jump_next') ?>
					</label>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="check_open_article"><?= _t('conf.reading.read.when') ?></label>
				<div class="group-controls">
					<label class="checkbox" for="check_open_article">
						<input type="checkbox" name="mark_open_article" id="check_open_article" value="1"<?=
							FreshRSS_Context::userConf()->mark_when['article'] ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->mark_when['article'] ?>"/>
						<?= _t('conf.reading.read.article_viewed') ?>
					</label>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="check_open_site"><?= _t('conf.reading.read.when') ?></label>
				<div class="group-controls">
					<label class="checkbox" for="check_open_site">
						<input type="checkbox" name="mark_open_site" id="check_open_site" value="1"<?=
							FreshRSS_Context::userConf()->mark_when['site'] ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->mark_when['site'] ?>"/>
						<?= _t('conf.reading.read.article_open_on_website') ?>
					</label>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="check_scroll"><?= _t('conf.reading.read.when') ?></label>
				<div class="group-controls">
					<label class="checkbox" for="check_scroll">
						<input type="checkbox" name="mark_scroll" id="check_scroll" value="1"<?=
							FreshRSS_Context::userConf()->mark_when['scroll'] ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->mark_when['scroll'] ?>"/>
						<?= _t('conf.reading.read.scroll') ?>
					</label>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="check_focus"><?= _t('conf.reading.read.when') ?></label>
				<div class="group-controls">
					<label class="checkbox" for="check_focus">
						<input type="checkbox" name="mark_focus" id="check_focus" value="1"<?=
							FreshRSS_Context::userConf()->mark_when['focus'] ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->mark_when['focus'] ?>" />
						<?= _t('conf.reading.read.focus') ?>
					</label>
				</div>
			</div>
		</fieldset>

		<fieldset>
			<legend><?= _t('sub.feed.filteractions') ?></legend>
			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="mark_updated_article_unread">
						<input type="checkbox" name="mark_updated_article_unread" id="mark_updated_article_unread" value="1"<?=
							FreshRSS_Context::userConf()->mark_updated_article_unread ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->mark_updated_article_unread ?>"/>
						<?= _t('conf.reading.mark_updated_article_unread') ?>
					</label>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="enable_read_when_same_title_in_feed"><?= _t('conf.reading.read.when') ?></label>
				<div class="group-controls">
					<label class="checkbox" for="enable_read_when_same_title_in_feed">
						<input type="checkbox" name="enable_read_when_same_title_in_feed" id="enable_read_when_same_title_in_feed" value="1"<?=
							empty(FreshRSS_Context::userConf()->mark_when['same_title_in_feed']) ? '' : ' checked="checked"' ?>
							data-leave-validation="<?= empty(FreshRSS_Context::userConf()->mark_when['same_title_in_feed']) ? 0 : 1 ?>"/>
						<?= _t('conf.reading.read.when_same_title_in_feed') ?>
						<?php $read_when_same_title_in_feed = empty(FreshRSS_Context::userConf()->mark_when['same_title_in_feed']) ? 25 : FreshRSS_Context::userConf()->mark_when['same_title_in_feed']; ?>
						<input type="number" id="read_when_same_title_in_feed" name="read_when_same_title_in_feed" min="0"
							value="<?= $read_when_same_title_in_feed ?>" data-leave-validation="<?= $read_when_same_title_in_feed ?>" />
					</label>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="mark_upon_reception"><?= _t('conf.reading.read.when') ?></label>
				<div class="group-controls">
					<label class="checkbox" for="mark_upon_reception">
						<input type="checkbox" name="mark_upon_reception" id="mark_upon_reception" value="1"<?=
							FreshRSS_Context::userConf()->mark_when['reception'] ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->mark_when['reception'] ?>"/>
						<?= _t('conf.reading.read.upon_reception') ?>
					</label>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="read_upon_gone"><?= _t('conf.reading.read.when') ?></label>
				<div class="group-controls">
					<label class="checkbox" for="read_upon_gone">
						<input type="checkbox" name="read_upon_gone" id="read_upon_gone" value="1"<?=
							FreshRSS_Context::userConf()->mark_when['gone'] ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->mark_when['gone'] ?>"/>
						<?= _t('conf.reading.read.upon_gone') ?>
					</label>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="filteractions_read"><?= _t('conf.reading.read.when') ?></label>
				<div class="group-controls">
					<textarea name="filteractions_read" id="filteractions_read" class="w100"><?php
						foreach (FreshRSS_Context::userConf()->filtersAction('read') as $filterRead) {
							echo $filterRead->getRawInput(), PHP_EOL;
						}
					?></textarea>
					<p class="help"><?= _i('help') ?> <?= _t('sub.feed.filteractions.help') ?></p>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="filteractions_star"><?= _t('conf.reading.star.when') ?></label>
				<div class="group-controls">
					<textarea name="filteractions_star" id="filteractions_star" class="w100"><?php
						foreach (FreshRSS_Context::userConf()->filtersAction('star') as $filterStar) {
							echo $filterStar->getRawInput(), PHP_EOL;
						}
					?></textarea>
					<p class="help"><?= _i('help') ?> <?= _t('sub.feed.filteractions.help') ?></p>
				</div>
			</div>

			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="keep_max_n_unread">
						<input type="checkbox" name="enable_keep_max_n_unread" id="enable_keep_max_n_unread" value="1"<?=
							empty(FreshRSS_Context::userConf()->mark_when['max_n_unread']) ? '' : ' checked="checked"' ?>
							data-leave-validation="<?= empty(FreshRSS_Context::userConf()->mark_when['max_n_unread']) ? 0 : 1 ?>"/>
						<?= _t('conf.reading.read.keep_max_n_unread') ?>
						<?php $keep_max_n_unread = empty(FreshRSS_Context::userConf()->mark_when['max_n_unread']) ? 1000 : FreshRSS_Context::userConf()->mark_when['max_n_unread']; ?>
						<input type="number" id="keep_max_n_unread" name="keep_max_n_unread" min="0" value="<?= $keep_max_n_unread ?>" data-leave-validation="<?= $keep_max_n_unread ?>" />
					</label>
				</div>
			</div>
		</fieldset>

		<fieldset>
			<legend><?= _t('conf.reading.headline.misc') ?></legend>
			<div class="form-group">
				<div class="group-controls">
					<label class="checkbox" for="lazyload">
						<input type="checkbox" name="lazyload" id="lazyload" value="1"<?=
							FreshRSS_Context::userConf()->lazyload ? ' checked="checked"' : '' ?>
							data-leave-validation="<?= FreshRSS_Context::userConf()->lazyload ?>"/>
						<?= _t('conf.reading.img_with_lazyload') ?>
						<noscript> — <strong><?= _t('gen.js.should_be_activated') ?></strong></noscript>
					</label>
				</div>
			</div>
		</fieldset>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.submit') ?></button>
				<button type="reset" class="btn"><?= _t('gen.action.cancel') ?></button>
			</div>
		</div>

	</form>
</main>

<script src="../scripts/config.js?<?= @filemtime(PUBLIC_PATH . '/scripts/config.js') ?>"></script>
