<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_configure');
?>
<main class="post">
	<h1><?= _t('conf.shortcut') ?></h1>

	<datalist id="keys">
		<?php foreach ($this->list_keys as $key) { ?>
		<option value="<?= $key ?>">
		<?php } ?>
	</datalist>

	<?php
		$s = array_map(static fn(string $string) => htmlspecialchars($string, ENT_COMPAT, 'UTF-8'), FreshRSS_Context::userConf()->shortcuts);
	?>

	<?php if ([] !== $nonStandard = getNonStandardShortcuts($s)): ?>
		<p class="alert alert-error">
			<?= _t('conf.shortcut.non_standard', implode('</kbd>, <kbd>', $nonStandard)) ?>
		</p>
	<?php endif; ?>

	<form method="post" action="<?= _url('configure', 'shortcut') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />

		<noscript><p class="alert alert-error"><?= _t('conf.shortcut.javascript') ?></p></noscript>

		<fieldset>
			<legend><?= _t('conf.shortcut.views') ?></legend>

			<div class="form-group">
				<label class="group-name" for="normal_view_shortcut"><?= _t('conf.shortcut.normal_view') ?></label>
				<div class="group-controls">
					<input type="text" id="normal_view_shortcut" name="shortcuts[normal_view]" list="keys" value="<?= $s['normal_view'] ?>"
						data-leave-validation="<?= $s['normal_view'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="global_view_shortcut"><?= _t('conf.shortcut.global_view') ?></label>
				<div class="group-controls">
					<input type="text" id="global_view_shortcut" name="shortcuts[global_view]" list="keys" value="<?= $s['global_view'] ?>"
						data-leave-validation="<?= $s['global_view'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="reading_view_shortcut"><?= _t('conf.shortcut.reading_view') ?></label>
				<div class="group-controls">
					<input type="text" id="reading_view_shortcut" name="shortcuts[reading_view]" list="keys" value="<?= $s['reading_view'] ?>"
						data-leave-validation="<?= $s['reading_view'] ?>"/>
				</div>
			</div>
		</fieldset>

		<fieldset>
			<legend><?= _t('conf.shortcut.navigation') ?></legend>

			<p class="alert alert-warn"><?= _t('conf.shortcut.navigation_help') ?></p>

			<div class="form-group">
				<label class="group-name" for="next_entry"><?= _t('conf.shortcut.next_article') ?></label>
				<div class="group-controls">
					<input type="text" id="next_entry" name="shortcuts[next_entry]" list="keys" value="<?= $s['next_entry'] ?>"
						data-leave-validation="<?= $s['next_entry'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="next_unread_entry"><?= _t('conf.shortcut.next_unread_article') ?></label>
				<div class="group-controls">
					<input type="text" id="next_unread_entry" name="shortcuts[next_unread_entry]" list="keys" value="<?= $s['next_unread_entry'] ?>"
						data-leave-validation="<?= $s['next_unread_entry'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="prev_entry"><?= _t('conf.shortcut.previous_article') ?></label>
				<div class="group-controls">
					<input type="text" id="prev_entry" name="shortcuts[prev_entry]" list="keys" value="<?= $s['prev_entry'] ?>"
						data-leave-validation="<?= $s['prev_entry'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="first_entry"><?= _t('conf.shortcut.first_article') ?></label>
				<div class="group-controls">
					<input type="text" id="first_entry" name="shortcuts[first_entry]" list="keys" value="<?= $s['first_entry'] ?>"
						data-leave-validation="<?= $s['first_entry'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="last_entry"><?= _t('conf.shortcut.last_article') ?></label>
				<div class="group-controls">
					<input type="text" id="last_entry" name="shortcuts[last_entry]" list="keys" value="<?= $s['last_entry'] ?>"
						data-leave-validation="<?= $s['last_entry'] ?>"/>
				</div>
			</div>

			<p class="alert alert-warn"><?= _t('conf.shortcut.navigation_no_mod_help') ?></p>

			<div class="form-group">
				<label class="group-name" for="skip_next_entry"><?= _t('conf.shortcut.skip_next_article') ?></label>
				<div class="group-controls">
					<input type="text" id="skip_next_entry" name="shortcuts[skip_next_entry]" list="keys" value="<?= $s['skip_next_entry'] ?>"
						data-leave-validation="<?= $s['skip_next_entry'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="skip_prev_entry"><?= _t('conf.shortcut.skip_previous_article') ?></label>
				<div class="group-controls">
					<input type="text" id="skip_prev_entry" name="shortcuts[skip_prev_entry]" list="keys" value="<?= $s['skip_prev_entry'] ?>"
						data-leave-validation="<?= $s['skip_prev_entry'] ?>"/>
				</div>
			</div>
		</fieldset>

		<fieldset>
			<legend><?= _t('conf.shortcut.article_action') ?></legend>

			<div class="form-group">
				<p class="alert alert-warn"><?= _t('conf.shortcut.shift_for_all_read') ?></p>
				<label class="group-name" for="mark_read"><?= _t('conf.shortcut.mark_read') ?></label>
				<div class="group-controls">
					<input type="text" id="mark_read" name="shortcuts[mark_read]" list="keys" value="<?= $s['mark_read'] ?>"
						data-leave-validation="<?= $s['mark_read'] ?>"/>
				</div>
			</div>

			<p class="alert alert-warn"><?= _t('conf.shortcut.navigation_no_mod_help') ?></p>

			<div class="form-group">
				<label class="group-name" for="mark_favorite"><?= _t('conf.shortcut.mark_favorite') ?></label>
				<div class="group-controls">
					<input type="text" id="mark_favorite" name="shortcuts[mark_favorite]" list="keys" value="<?= $s['mark_favorite'] ?>"
						data-leave-validation="<?= $s['mark_favorite'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="go_website"><?= _t('conf.shortcut.see_on_website') ?></label>
				<div class="group-controls">
					<input type="text" id="go_website" name="shortcuts[go_website]" list="keys" value="<?= $s['go_website'] ?>"
						data-leave-validation="<?= $s['go_website'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="auto_share_shortcut"><?= _t('conf.shortcut.auto_share') ?></label>
				<div class="group-controls">
					<input type="text" id="auto_share_shortcut" name="shortcuts[auto_share]" list="keys" value="<?= $s['auto_share'] ?>"
						data-leave-validation="<?= $s['auto_share'] ?>"/>
						<p class="help"><?= _i('help') ?> <?= _t('conf.shortcut.auto_share_help') ?></p>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="mylabels_shortcut"><?= _t('index.menu.mylabels') ?></label>
				<div class="group-controls">
					<input type="text" id="mylabels_shortcut" name="shortcuts[mylabels]" list="keys" value="<?= $s['mylabels'] ?>"
						data-leave-validation="<?= $s['mylabels'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="collapse_entry"><?= _t('conf.shortcut.collapse_article') ?></label>
				<div class="group-controls">
					<input type="text" id="collapse_entry" name="shortcuts[collapse_entry]" list="keys" value="<?= $s['collapse_entry'] ?>"
						data-leave-validation="<?= $s['collapse_entry'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="toggle_media"><?= _t('conf.shortcut.toggle_media') ?></label>
				<div class="group-controls">
					<input type="text" id="toggle_media" name="shortcuts[toggle_media]" list="keys" value="<?= $s['toggle_media'] ?>"
						data-leave-validation="<?= $s['toggle_media'] ?>"/>
				</div>
			</div>
		</fieldset>

		<fieldset>
			<legend><?= _t('conf.shortcut.other_action') ?></legend>

			<div class="form-group">
				<label class="group-name" for="actualize"><?= _t('gen.action.actualize') ?></label>
				<div class="group-controls">
					<input type="text" id="actualize" name="shortcuts[actualize]" list="keys" value="<?= $s['actualize'] ?>"
						data-leave-validation="<?= $s['actualize'] ?>" />
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="load_more_shortcut"><?= _t('conf.shortcut.load_more') ?></label>
				<div class="group-controls">
					<input type="text" id="load_more_shortcut" name="shortcuts[load_more]" list="keys" value="<?= $s['load_more'] ?>"
						data-leave-validation="<?= $s['load_more'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="focus_search_shortcut"><?= _t('conf.shortcut.focus_search') ?></label>
				<div class="group-controls">
					<input type="text" id="focus_search_shortcut" name="shortcuts[focus_search]" list="keys" value="<?= $s['focus_search'] ?>"
						data-leave-validation="<?= $s['focus_search'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="user_filter_shortcut"><?= _t('conf.shortcut.user_filter') ?></label>
				<div class="group-controls">
					<input type="text" id="user_filter_shortcut" name="shortcuts[user_filter]" list="keys" value="<?= $s['user_filter'] ?>"
						data-leave-validation="<?= $s['user_filter'] ?>"/>
						<p class="help"><?= _i('help') ?> <?= _t('conf.shortcut.user_filter_help') ?></p>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="close_dropdown_shortcut"><?= _t('conf.shortcut.close_dropdown') ?></label>
				<div class="group-controls">
					<input type="text" id="close_dropdown" name="shortcuts[close_dropdown]" list="keys" value="<?= $s['close_dropdown'] ?>"
						data-leave-validation="<?= $s['close_dropdown'] ?>"/>
				</div>
			</div>

			<div class="form-group">
				<label class="group-name" for="help_shortcut"><?= _t('conf.shortcut.help') ?></label>
				<div class="group-controls">
					<input type="text" id="help_shortcut" name="shortcuts[help]" list="keys" value="<?= $s['help'] ?>"
						data-leave-validation="<?= $s['help'] ?>"/>
				</div>
			</div>
		</fieldset>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.submit') ?></button>
				<button type="submit" class="btn btn-important confirm" name="load_default_shortcuts" value="1"><?= _t('gen.action.load_default_shortcuts') ?></button>
				<button type="reset" class="btn"><?= _t('gen.action.cancel') ?></button>
			</div>
		</div>
	</form>
</main>
