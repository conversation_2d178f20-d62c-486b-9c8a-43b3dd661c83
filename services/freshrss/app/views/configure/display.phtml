<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_configure');
?>
<main class="post">
	<h1><?= _t('conf.display') ?></h1>

	<form method="post" action="<?= _url('configure', 'display') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />

		<div class="form-group">
			<label class="group-name" for="language"><?= _t('conf.display.language') ?></label>
			<div class="group-controls">
				<select name="language" id="language" data-leave-validation="<?= FreshRSS_Context::userConf()->language ?>">
				<?php $languages = Minz_Translate::availableLanguages(); ?>
				<?php foreach ($languages as $lang) { ?>
				<option value="<?= $lang ?>"<?= FreshRSS_Context::userConf()->language === $lang ? ' selected="selected"' : '' ?>><?= _t('gen.lang.' . $lang) ?></option>
				<?php } ?>
				</select>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name" for="timezone"><?= _t('conf.display.timezone') ?></label>
			<div class="group-controls">
				<select name="timezone" id="timezone" data-leave-validation="<?= FreshRSS_Context::userConf()->timezone ?>">
				<?php
					$timezones = array_merge([''], DateTimeZone::listIdentifiers());
					if (!in_array(FreshRSS_Context::userConf()->timezone, $timezones, true)) {
						FreshRSS_Context::userConf()->timezone = '';
					}
				?>
				<?php foreach ($timezones as $timezone): ?>
				<option value="<?= $timezone ?>"<?= FreshRSS_Context::userConf()->timezone === $timezone ? ' selected="selected"' : '' ?>>
					<?= $timezone == '' ? _t('gen.short.by_default') . ' (' . FreshRSS_Context::defaultTimeZone() . ')' : $timezone ?>
				</option>
				<?php endforeach; ?>
				</select>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name" for="theme"><?= _t('conf.display.theme') ?></label>
			<div class="group-controls">
				<ul class="theme-preview-list">
					<?php $slides = count($this->themes); $i = 1; $themeAvailable = false; ?>
					<?php
						foreach ($this->themes as $theme) { ?>
						<?php if (FreshRSS_Context::userConf()->theme === $theme['id']) {
							$checked = 'checked="checked"';
							$themeAvailable = true;
						} else {
							$checked = '';
						} ?>
						<input type="radio" name="theme" id="img-<?= $i ?>" <?= $checked ?> value="<?= $theme['id'] ?>"
							data-leave-validation="<?= (FreshRSS_Context::userConf()->theme === $theme['id']) ? 1 : 0 ?>" />
						<li class="preview-container">
							<div class="preview">
								<img src="<?= Minz_Url::display('/themes/' . $theme['id'] . '/thumbs/original.png') ?>" loading="lazy" />
							</div>
							<div class="nav">
								<?php if ($i !== 1) {?>
									<label for="img-<?= $i - 1 ?>" class="prev">‹</label>
								<?php } ?>
								<?php if ($i !== $slides) {?>
									<label for="img-<?= $i + 1 ?>" class="next">›</label>
								<?php } ?>
							</div>
							<div class="properties">
								<div>
									<?php if (!empty($theme['deprecated'])) { ?>
										<span class="deprecated error"><?= _t('conf.display.theme.deprecated') ?>:</span>
									<?php } ?>
									<?= sprintf('%s — %s %s', $theme['name'], _t('gen.short.by_author'), $theme['author']) ?>
								</div>
								<div>
									<?php if (!empty($theme['deprecated'])) { ?>
										<span class="deprecated"><?= _t('conf.display.theme.deprecated.description') ?></span><br />
									<?php } ?>
									<?= $theme['description'] ?>
								</div>
								<?php if (!empty($theme['theme-color']['dark'])) { ?>
									<div class="darkMode">✔ <?= _t('conf.display.darkMode') ?></div>
								<?php } ?>
								<div class="page-number"><?= sprintf('%d/%d', $i, $slides) ?></div>
							</div>
						</li>
						<?php $i++ ?>
					<?php } ?>
					<?php if (!$themeAvailable) {?>
						<input type="radio" name="theme" checked="checked" value="Origine" data-leave-validation="0" />
						<li class="preview-container">
							<div class="preview">
							</div>
							<div class="nav">
								<label for="img-<?= $i - 1 ?>" class="prev">‹</label>
							</div>
							<div class="properties alert-error">
								<div><?= _t('conf.display.theme_not_available', FreshRSS_Context::userConf()->theme)?></div>
							</div>
						</li>
					<?php }?>
				</ul>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name" for="darkMode"><?= _t('conf.display.darkMode') ?></label>
			<div class="group-controls">
				<select name="darkMode" id="darkMode" data-leave-validation="<?= FreshRSS_Context::userConf()->darkMode ?>">
					<option value="no"<?= FreshRSS_Context::userConf()->darkMode === 'no' ? ' selected' : '' ?>><?= _t('conf.display.darkMode.no') ?></option>
					<option value="auto"<?= FreshRSS_Context::userConf()->darkMode === 'auto' ? ' selected' : '' ?>><?= _t('conf.display.darkMode.auto') ?></option>
				</select>
				<span class="help"><?= _i('help') ?> <?= _t('conf.display.darkMode.help') ?></span>
			</div>
		</div>

		<?php $width = FreshRSS_Context::userConf()->content_width; ?>
		<div class="form-group">
			<label class="group-name" for="content_width"><?= _t('conf.display.width.content') ?></label>
			<div class="group-controls">
				<select name="content_width" id="content_width" required="" data-leave-validation="<?= $width ?>">
					<option value="thin" <?= $width === 'thin' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.width.thin') ?>
					</option>
					<option value="medium" <?= $width === 'medium' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.width.medium') ?>
					</option>
					<option value="large" <?= $width === 'large' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.width.large') ?>
					</option>
					<option value="no_limit" <?= $width === 'no_limit' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.width.no_limit') ?>
					</option>
				</select>
			</div>
		</div>

		<?php $topline_website = FreshRSS_Context::userConf()->topline_website; ?>
		<div class="form-group">
			<label class="group-name" for="topline_website"><?= _t('conf.display.website.label') ?></label>
			<div class="group-controls">
				<select name="topline_website" id="topline_website" required="" data-leave-validation="<?= $topline_website ?>">
					<option value="none" <?= $topline_website === 'none' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.website.none') ?>
					</option>
					<option value="icon" <?= $topline_website === 'icon' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.website.icon') ?>
					</option>
					<option value="name" <?= $topline_website === 'name' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.website.name') ?>
					</option>
					<option value="full" <?= $topline_website === 'full' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.website.full') ?>
					</option>
				</select>
			</div>
		</div>

		<?php $topline_thumbnail = FreshRSS_Context::userConf()->topline_thumbnail; ?>
		<div class="form-group">
			<label class="group-name" for="topline_thumbnail"><?= _t('conf.display.thumbnail.label') ?></label>
			<div class="group-controls">
				<select name="topline_thumbnail" id="topline_thumbnail" required="" data-leave-validation="<?= $topline_thumbnail ?>">
					<option value="none" <?= $topline_thumbnail === 'none' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.thumbnail.none') ?>
					</option>
					<option value="portrait" <?= $topline_thumbnail === 'portrait' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.thumbnail.portrait') ?>
					</option>
					<option value="square" <?= $topline_thumbnail === 'square' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.thumbnail.square') ?>
					</option>
					<option value="landscape" <?= $topline_thumbnail === 'landscape' ? 'selected="selected"' : '' ?>>
						<?= _t('conf.display.thumbnail.landscape') ?>
					</option>
				</select>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name"><?= _t('conf.display.icon.entry') ?></label>
			<div class="group-controls">
				<table class="config-articleicons">
					<thead>
						<tr>
							<th> </th>
							<th title="<?= _t('conf.shortcut.mark_read') ?>"><?= _i('read') ?></th>
							<th title="<?= _t('conf.shortcut.mark_favorite') ?>"><?= _i('starred') ?></th>
							<th title="<?= _t('index.menu.mylabels') ?>"><?= _i('label') ?></th>
							<th title="<?= _t('conf.display.icon.related_tags') ?>"><?= _i('tag') ?></th>
							<th title="<?= _t('conf.display.icon.sharing') ?>"><?= _i('share') ?></th>
							<th><?= _t('conf.display.icon.summary') ?></th>
							<th><?= _t('conf.display.icon.display_authors') ?></th>
							<th><?= _t('conf.display.icon.publication_date') ?></th>
							<th><?= _i('link') ?></th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<th><?= _t('conf.display.icon.top_line') ?></th>
							<td><input type="checkbox" name="topline_read" value="1"<?=
								FreshRSS_Context::userConf()->topline_read ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->topline_read ?>" /></td>
							<td><input type="checkbox" name="topline_favorite" value="1"<?=
								FreshRSS_Context::userConf()->topline_favorite ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->topline_favorite ?>" /></td>
							<td><input type="checkbox" name="topline_myLabels" value="1"<?=
								FreshRSS_Context::userConf()->topline_myLabels ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->topline_myLabels ?>" /></td>
							<td><input type="checkbox" disabled="disabled" /></td>
							<td><input type="checkbox" name="topline_sharing" value="1"<?=
								FreshRSS_Context::userConf()->topline_sharing ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->topline_sharing ?>" /></td>
							<td><input type="checkbox" name="topline_summary" value="1"<?=
								FreshRSS_Context::userConf()->topline_summary ? 'checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->topline_summary ?>" /></td>
							<td><input type="checkbox" name="topline_display_authors" value="1"<?=
								FreshRSS_Context::userConf()->topline_display_authors ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->topline_display_authors ?>" /></td>
							<td><input type="checkbox" name="topline_date" value="1"<?=
								FreshRSS_Context::userConf()->topline_date ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->topline_date ?>" /></td>
							<td><input type="checkbox" name="topline_link" value="1"<?= FreshRSS_Context::userConf()->topline_link ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->topline_link ?>" /></td>
						</tr><tr>
							<th><?= _t('conf.display.icon.bottom_line') ?></th>
							<td><input type="checkbox" name="bottomline_read" value="1"<?=
								FreshRSS_Context::userConf()->bottomline_read ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->bottomline_read ?>" /></td>
							<td><input type="checkbox" name="bottomline_favorite" value="1"<?=
								FreshRSS_Context::userConf()->bottomline_favorite ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->bottomline_favorite ?>" /></td>
							<td><input type="checkbox" name="bottomline_myLabels" value="1"<?=
								FreshRSS_Context::userConf()->bottomline_myLabels ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->bottomline_myLabels ?>" /></td>
							<td><input type="checkbox" name="bottomline_tags" value="1"<?=
								FreshRSS_Context::userConf()->bottomline_tags ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->bottomline_tags ?>" /></td>
							<td><input type="checkbox" name="bottomline_sharing" value="1"<?=
								FreshRSS_Context::userConf()->bottomline_sharing ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->bottomline_sharing ?>" /></td>
							<td><input type="checkbox" disabled="disabled" /></td>
							<td><input type="checkbox" disabled="disabled" /></td>
							<td><input type="checkbox" name="bottomline_date" value="1"<?=
								FreshRSS_Context::userConf()->bottomline_date ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->bottomline_date ?>" /></td>
							<td><input type="checkbox" name="bottomline_link" value="1"<?=
								FreshRSS_Context::userConf()->bottomline_link ? ' checked="checked"' : '' ?>
								data-leave-validation="<?= FreshRSS_Context::userConf()->bottomline_link ?>" /></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name" for="html5_notif_timeout"><?= _t('conf.display.notif_html5.timeout') ?></label>
			<div class="group-controls">
				<input type="number" id="html5_notif_timeout" name="html5_notif_timeout" value="<?=
					FreshRSS_Context::userConf()->html5_notif_timeout ?>"
					data-leave-validation="<?= FreshRSS_Context::userConf()->html5_notif_timeout ?>" /> <?= _t('conf.display.notif_html5.seconds') ?>
			</div>
		</div>

		<div class="form-group">
			<div class="group-controls">
				<label class="checkbox" for="show_nav_buttons">
					<input type="checkbox" name="show_nav_buttons" id="show_nav_buttons" value="1"<?=
						FreshRSS_Context::userConf()->show_nav_buttons ? ' checked="checked"' : '' ?>
						data-leave-validation="<?= FreshRSS_Context::userConf()->show_nav_buttons ?>" />
					<?= _t('conf.display.show_nav_buttons') ?>
				</label>
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.submit') ?></button>
				<button type="reset" class="btn"><?= _t('gen.action.cancel') ?></button>
			</div>
		</div>
	</form>
</main>
