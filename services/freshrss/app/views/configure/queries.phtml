<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_configure');
?>
<main class="post">
	<form method="post" action="<?= _url('configure', 'queries') ?>" class="draggableList">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<h1><?= _t('conf.query') ?></h1>
		<?php if (count($this->queries) < 1) { ?>
			<div class="alert alert-warn">
				<p><?= _t('conf.query.no_queries') ?></p>
				<p class="help"><?= _i('help') ?> <?= _t('conf.query.no_queries.help') ?></p>
		</div>
		<?php
		} else {
			foreach ($this->queries as $key => $query) { ?>
				<div class="form-group" id="query-group-<?= $key ?>" draggable="true">
					<div class="box">
						<div class="box-title">
							<a class="configure open-slider" href="<?= _url('configure', 'query', 'id', '' . $key) ?>"><?= _i('configure') ?></a><h2><?= $query->getName() ?></h2>
							<input type="hidden" id="queries_<?= $key ?>_name" name="queries[<?= $key ?>][name]" value="<?= $query->getName() ?>"/>
							<input type="hidden" id="queries_<?= $key ?>_token" name="queries[<?= $key ?>][token]" value="<?= $query->getToken() ?>"/>
							<input type="hidden" id="queries_<?= $key ?>_shareRss" name="queries[<?= $key ?>][token]" value="<?= $query->shareRss() ?>"/>
							<input type="hidden" id="queries_<?= $key ?>_shareOpml" name="queries[<?= $key ?>][token]" value="<?= $query->shareOpml() ?>"/>
							<input type="hidden" id="queries_<?= $key ?>_url" name="queries[<?= $key ?>][url]" value="<?= $query->getUrl() ?>"/>
							<input type="hidden" id="queries_<?= $key ?>_search" name="queries[<?= $key ?>][search]" value="<?= urlencode($query->getSearch()->getRawInput()) ?>"/>
							<input type="hidden" id="queries_<?= $key ?>_state" name="queries[<?= $key ?>][state]" value="<?= $query->getState() ?>"/>
							<input type="hidden" id="queries_<?= $key ?>_order" name="queries[<?= $key ?>][order]" value="<?= $query->getOrder() ?>"/>
							<input type="hidden" id="queries_<?= $key ?>_get" name="queries[<?= $key ?>][get]" value="<?= $query->getGet() ?>"/>
						</div>
						<?php if (!$query->hasParameters()) { ?>
						<div class="box-content">
							<div class="alert alert-warn">
								<div class="alert-head"><?= _t('conf.query.no_filter') ?></div>
							</div>
						</div>
						<?php } elseif ($query->isDeprecated()) { ?>
						<div class="box-content">
							<div class="alert alert-error">
								<div class="alert-head"><?= _t('conf.query.deprecated') ?></div>
							</div>
						</div>
						<?php } else { ?>
							<ul class="box-content scrollbar-thin">
							<?php if ($query->hasSearch()) { ?>
							<li class="item"><?= _t('conf.query.search', htmlspecialchars($query->getSearch()->getRawInput(), ENT_NOQUOTES, 'UTF-8')) ?></li>
							<?php } ?>

							<?php if ($query->getState()) { ?>
							<li class="item"><?= _t('conf.query.state_' . $query->getState()) ?></li>
							<?php } ?>

							<?php if ($query->getOrder() !== '') { ?>
							<li class="item"><?= _t('conf.query.order_' . strtolower($query->getOrder())) ?></li>
							<?php } ?>

							<?php if ($query->getGet() !== '') { ?>
							<li class="item"><?= _t('conf.query.get_' . $query->getGetType(), $query->getGetName()) ?></li>
							<?php } ?>
							<?php } ?>
							</ul>
					</div>
				</div>
			<?php
			}
		}?>
	</form>
</main>

<?php $class = ($this->query != null) ? ' active' : ''; ?>
<aside id="slider" class="<?= $class ?>">
<a class="toggle_aside" href="#close"><img class="icon" src="../themes/icons/close.svg" loading="lazy" alt="❌"></a>
	<div id="slider-content">
		<?php
			if ($this->query != null) {
			$this->renderHelper('configure/query');
			}
		?>
	</div>
</aside>
<a href="#" id="close-slider">
	<?= _i('close') ?>
</a>
