<?php
	/** @var FreshRSS_View $this */
	$this->partial('aside_configure');
?>

<main class="post">
	<h1><?= _t('conf.privacy') ?></h1>

	<form method="post" action="<?= _url('configure', 'privacy') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />

		<div class="form-group">
			<label class="group-name" for="retrieve_extension_list"><?= _t('conf.privacy.retrieve_extension_list') ?></label>
			<div class="group-controls">
				<input type="checkbox" id="retrieve_extension_list" name="retrieve_extension_list" value="1"<?=
					FreshRSS_Context::userConf()->retrieve_extension_list !== false ? ' checked="checked"' : '' ?>
					data-leave-validation="<?= FreshRSS_Context::userConf()->retrieve_extension_list !== false ? 1 : 0 ?>"/>
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.submit') ?></button>
				<button type="reset" class="btn"><?= _t('gen.action.cancel') ?></button>
			</div>
		</div>
	</form>
</main>
