<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_configure');
?>
<main class="post">
	<h1><?= _t('conf.archiving') ?></h1>
	<p class="help"><?= _i('help') ?> <?= _t('conf.archiving.help') ?></p>

	<form method="post" action="<?= _url('configure', 'archiving') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />

		<div class="form-group">
			<label class="group-name" for="ttl_default"><?= _t('conf.archiving.ttl') ?></label>
			<div class="group-controls">
				<select class="number" name="ttl_default" id="ttl_default" required="required" data-leave-validation="<?= FreshRSS_Context::userConf()->ttl_default ?>"><?php
					$found = false;
					foreach ([1200 => '20min', 1500 => '25min', 1800 => '30min', 2700 => '45min',
							3600 => '1h', 5400 => '1.5h', 7200 => '2h', 10800 => '3h', 14400 => '4h', 18800 => '5h', 21600 => '6h', 25200 => '7h', 28800 => '8h',
							36000 => '10h', 43200 => '12h', 64800 => '18h',
							86400 => '1d', 129600 => '1.5d', 172800 => '2d', 259200 => '3d', 345600 => '4d', 432000 => '5d', 518400 => '6d',
							604800 => '1wk'] as $v => $t) {
						echo '<option value="' . $v . (FreshRSS_Context::userConf()->ttl_default == $v ? '" selected="selected' : '') . '">' . $t . '</option>';
						if (FreshRSS_Context::userConf()->ttl_default == $v) {
							$found = true;
						}
					}
					if (!$found) {
						echo '<option value="' . intval(FreshRSS_Context::userConf()->ttl_default) . '" selected="selected">'
							. intval(FreshRSS_Context::userConf()->ttl_default) . 's</option>';
					}
				?></select> (<?= _t('gen.short.by_default') ?>)
			</div>
		</div>

		<p class="alert alert-warn">
			<?= _t('conf.archiving.policy_warning') ?>
		</p>

		<div class="form-group">
			<label class="group-name"><?= _t('conf.archiving.policy') ?><br /><small>(<?= _t('gen.short.by_default') ?>)</small></label>
			<div class="group-controls">
				<label class="checkbox" for="enable_keep_max">
					<input type="checkbox" name="enable_keep_max" id="enable_keep_max" value="1"<?=
						empty(FreshRSS_Context::userConf()->archiving['keep_max']) ? '' : ' checked="checked"' ?>
						data-leave-validation="<?= empty(FreshRSS_Context::userConf()->archiving['keep_max']) ? 0 : 1 ?>"/>
					<?= _t('conf.archiving.keep_max') ?>
					<?php $keepMax = empty(FreshRSS_Context::userConf()->archiving['keep_max']) ? 200 : FreshRSS_Context::userConf()->archiving['keep_max']; ?>
					<input type="number" id="keep_max" name="keep_max" min="0" value="<?= $keepMax ?>" data-leave-validation="<?= $keepMax ?>"/>
				</label>
			</div>
		</div>

		<div class="form-group">
			<div class="group-controls">
				<label class="checkbox" for="enable_keep_period">
					<input type="checkbox" name="enable_keep_period" id="enable_keep_period" value="1"<?=
						empty(FreshRSS_Context::userConf()->volatile['enable_keep_period']) ? '' : ' checked="checked"' ?>
						data-leave-validation="<?= empty(FreshRSS_Context::userConf()->volatile['enable_keep_period']) ? 0 : 1 ?>"/>
					<?= _t('conf.archiving.keep_period') ?>
					<input type="number" id="keep_period_count" name="keep_period_count" min="0" value="<?= FreshRSS_Context::userConf()->volatile['keep_period_count'] ?>"
						data-leave-validation="<?= FreshRSS_Context::userConf()->volatile['keep_period_count'] ?>"/>
					<select class="number" name="keep_period_unit" id="keep_period_unit" data-leave-validation="<?= FreshRSS_Context::userConf()->volatile['keep_period_unit'] ?>">
						<option></option>
						<option value="P1Y" <?= 'P1Y' === FreshRSS_Context::userConf()->volatile['keep_period_unit'] ? 'selected="selected"' : '' ?>><?= _t('gen.period.years') ?></option>
						<option value="P1M" <?= 'P1M' === FreshRSS_Context::userConf()->volatile['keep_period_unit'] ? 'selected="selected"' : '' ?>><?= _t('gen.period.months') ?></option>
						<option value="P1W" <?= 'P1W' === FreshRSS_Context::userConf()->volatile['keep_period_unit'] ? 'selected="selected"' : '' ?>><?= _t('gen.period.weeks') ?></option>
						<option value="P1D" <?= 'P1D' === FreshRSS_Context::userConf()->volatile['keep_period_unit'] ? 'selected="selected"' : '' ?>><?= _t('gen.period.days') ?></option>
						<option value="PT1H" <?= 'PT1H' === FreshRSS_Context::userConf()->volatile['keep_period_unit'] ? 'selected="selected"' : '' ?>><?= _t('gen.period.hours') ?></option>
					</select>
				</label>
			</div>
		</div>

		<div class="form-group">
			<label class="group-name"><?= _t('conf.archiving.exception') ?><br /><small>(<?= _t('gen.short.by_default') ?>)</small></label>
			<div class="group-controls">
				<label class="checkbox" for="keep_favourites">
					<input type="checkbox" name="keep_favourites" id="keep_favourites" value="1"<?=
						FreshRSS_Context::userConf()->archiving['keep_favourites'] !== false ? ' checked="checked"' : '' ?>
						data-leave-validation="<?= FreshRSS_Context::userConf()->archiving['keep_favourites'] !== false ? 1 : 0 ?>"/>
					<?= _t('conf.archiving.keep_favourites') ?>
				</label>
			</div>
		</div>

		<div class="form-group">
			<div class="group-controls">
				<label class="checkbox" for="keep_labels">
					<input type="checkbox" name="keep_labels" id="keep_labels" value="1"<?=
						FreshRSS_Context::userConf()->archiving['keep_labels'] !== false ? ' checked="checked"' : '' ?>
						data-leave-validation="<?= FreshRSS_Context::userConf()->archiving['keep_labels'] !== false ? 1 : 0 ?>"/>
					<?= _t('conf.archiving.keep_labels') ?>
				</label>
			</div>
		</div>

		<div class="form-group">
			<div class="group-controls">
				<label class="checkbox" for="keep_unreads">
					<input type="checkbox" name="keep_unreads" id="keep_unreads" value="1"<?=
						FreshRSS_Context::userConf()->archiving['keep_unreads'] ? ' checked="checked"' : '' ?>
						data-leave-validation="<?= FreshRSS_Context::userConf()->archiving['keep_unreads'] ? 1 : 0 ?>"/>
					<?= _t('conf.archiving.keep_unreads') ?>
				</label>
			</div>
		</div>

		<div class="form-group">
			<div class="group-controls">
				<label for="keep_min_default"><?= _t('conf.archiving.keep_min_by_feed') ?>
					<input type="number" id="keep_min_default" name="keep_min_default" min="0" value="<?=
						FreshRSS_Context::userConf()->archiving['keep_min'] ?>"
						data-leave-validation="<?= FreshRSS_Context::userConf()->archiving['keep_min'] ?>">
				</label>
			</div>
		</div>

		<div class="form-group form-actions">
			<div class="group-controls">
				<button type="submit" class="btn btn-important"><?= _t('gen.action.submit') ?></button>
				<button type="reset" class="btn"><?= _t('gen.action.cancel') ?></button>
			</div>
		</div>
	</form>

	<h2><?= _t('conf.archiving.maintenance') ?></h2>
	<form method="post" action="<?= _url('entry', 'purge') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />

		<div class="form-group">
			<label class="group-name"><?= _t('conf.user.current') ?></label>
			<div class="group-controls">
				<?= _t('conf.user.articles_and_size', format_number($this->nb_total), format_bytes($this->size_user)) ?>
			</div>
		</div>

		<div class="form-group">
			<div class="group-controls">
				<button type="submit" class="btn btn-important confirm"><?= _t('conf.archiving.purge_now') ?></button>
			</div>
		</div>
	</form>

	<form method="post" action="<?= _url('entry', 'optimize') ?>">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		<div class="form-group">
			<div class="group-controls">
				<input type="hidden" name="optimiseDatabase" value="1" />
				<button type="submit" class="btn btn-important"><?= _t('conf.archiving.optimize') ?></button>
				<p class="help"><?= _i('help') ?> <?= _t('conf.archiving.optimize_help') ?></p>
			</div>
		</div>
	</form>

	<?php if (FreshRSS_Auth::hasAccess('admin')): ?>
	<div class="form-group">
		<label class="group-name"><?= _t('conf.user.users') ?></label>
		<div class="group-controls">
			<?= format_bytes($this->size_total) ?>
		</div>
	</div>
	<?php endif; ?>
</main>
