<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
?>
<?= '<?xml version="1.0" encoding="UTF-8" ?>'; ?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:media="http://search.yahoo.com/mrss/"
	<?= $this->rss_base == '' ? '' : ' xml:base="' . $this->rss_base . '"' ?>
>
	<channel>
		<title><?= $this->rss_title ?></title>
		<link><?= $this->html_url ?></link>
		<description><?= $this->description ?: _t('index.feed.rss_of', $this->rss_title) ?></description>
		<pubDate><?= date('D, d M Y H:i:s O') ?></pubDate>
		<lastBuildDate><?= gmdate('D, d M Y H:i:s') ?> GMT</lastBuildDate>
		<atom:link href="<?= $this->rss_url ?>" rel="self" type="application/rss+xml" />
		<?php if ($this->image_url !== ''): ?>
		<image>
			<url><?= $this->image_url ?></url>
			<title><?= $this->rss_title ?></title>
			<link><?= $this->html_url ?></link>
		</image>
		<?php endif; ?>
<?php
foreach ($this->entries as $item) {
	if (!$this->internal_rendering) {
		/** @var FreshRSS_Entry|null $item */
		$item = Minz_ExtensionManager::callHook('entry_before_display', $item);
		if ($item === null) {
			continue;
		}
	}
?>
		<item>
			<title><?= $item->title() ?></title>
			<link><?= $item->link() ?></link>
			<?php
				$authors = $item->authors();
				if (is_array($authors)) {
					foreach ($authors as $author) {
						echo "\t\t\t", '<dc:creator>', $author, '</dc:creator>', "\n";
					}
				}
				$categories = $item->tags();
				if (is_array($categories)) {
					foreach ($categories as $category) {
						echo "\t\t\t", '<category>', $category, '</category>', "\n";
					}
				}

				$enclosures = iterator_to_array($item->enclosures(false));
				$thumbnail = $item->thumbnail(false);
				if (!empty($thumbnail['url'])) {
					// https://www.rssboard.org/media-rss#media-thumbnails
					echo "\t\t\t", '<media:thumbnail url="' . $thumbnail['url']
						. (empty($thumbnail['width']) ? '' : '" width="' . $thumbnail['width'])
						. (empty($thumbnail['height']) ? '' : '" height="' . $thumbnail['height'])
						. (empty($thumbnail['time']) ? '' : '" time="' . $thumbnail['time'])
						. '" />', "\n";
					// Mostly for MailChimp + Feedbro which do not support <media:thumbnail> https://mailchimp.com/help/rss-merge-tags/
					$thumbnail['medium'] ??= 'image';
					array_unshift($enclosures, $thumbnail);
				}

				$urls = [];
				foreach ($enclosures as $enclosure) {
					if (empty($enclosure['url']) || isset($urls[$enclosure['url']])) {
						continue;
					}
					$urls[$enclosure['url']] = true;

					$credits = $enclosure['credit'] ?? [];
					if (!is_array($credits)) {	// For entries < FreshRSS 1.24
						$credits = [$credits];
					}
					$mediaCredits = '';
					foreach ($credits as $credit) {
						$mediaCredits = '<media:credit>' . $credit . '</media:credit>';
					}

					// https://www.rssboard.org/media-rss
					echo "\t\t\t", '<media:content url="' . $enclosure['url']
						. (empty($enclosure['medium']) ? '' : '" medium="' . $enclosure['medium'])
						. (empty($enclosure['type']) ? '' : '" type="' . $enclosure['type'])
						. (empty($enclosure['length']) ? '' : '" length="' . $enclosure['length'])
						. (empty($enclosure['height']) ? '' : '" height="' . $enclosure['height'])
						. (empty($enclosure['width']) ? '' : '" width="' . $enclosure['width'])
						. '">'
						. (empty($enclosure['title']) ? '' : '<media:title type="html">' . $enclosure['title'] . '</media:title>')
						. $mediaCredits
						. '</media:content>', "\n";
				}
			?>
			<description><![CDATA[<?php
	echo $item->content(false);
?>]]></description>
			<pubDate><?= date('D, d M Y H:i:s O', $item->date(true)) ?></pubDate>
			<guid isPermaLink="false"><?= $item->id() > 0 ? $item->id() : $item->guid() ?></guid>
		</item>
<?php } ?>

	</channel>
</rss>
