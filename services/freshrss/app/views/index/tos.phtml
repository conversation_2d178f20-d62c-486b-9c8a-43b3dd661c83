<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	if (FreshRSS_Auth::hasAccess()) {
		$this->partial('aside_configure');
	}
?>
<main class="post content<?= !FreshRSS_Auth::hasAccess() ? ' centered' : ''?>">
	<h1><?= _t('index.tos.title')?></h1>
	<?= $this->terms_of_service ?>
</main>

<?php if (!FreshRSS_Auth::hasAccess()) { ?>
<footer class="main-footer">
	<a href="<?= _url('index', 'about') ?>"><?= _t('gen.freshrss.about') ?></a>
</footer>
<?php } ?>
