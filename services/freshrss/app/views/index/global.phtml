<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('nav_menu');

	$class = '';
	$state_unread = false;

	if (FreshRSS_Context::userConf()->hide_read_feeds &&
			FreshRSS_Context::isStateEnabled(FreshRSS_Entry::STATE_NOT_READ) &&
			!FreshRSS_Context::isStateEnabled(FreshRSS_Entry::STATE_READ)) {
		$class = ' state_unread';
		$state_unread = true;
	}
?>

<datalist id="datalist-labels"></datalist>

<template id="share_article_template">
	<?php $this->renderHelper('index/normal/entry_share_menu'); ?>
	<a class="dropdown-close" href="#close">❌</a>
</template>

<template id="labels_article_template">
	<div class="dropdown-menu">
		<ul class="dropdown-menu-scrollable scrollbar-thin">
			<li class="dropdown-header">
				<?= _t('index.menu.mylabels') ?>
				<?php if (FreshRSS_Auth::hasAccess()) { ?>
					<a href="<?= _url('tag', 'index') ?>"><?= _i('configure') ?></a>
				<?php } ?>
			</li>
			<!-- Ajax -->
		</ul>
	</div>
	<a class="dropdown-close" href="#close">❌</a>
</template>

<main id="stream" class="global<?= $class ?>">
	<h1 class="title_hidden"><?= _t('conf.reading.view.global') ?></h1>
<?php
	$params = array_filter($_GET, 'is_string', ARRAY_FILTER_USE_KEY);
	unset($params['c']);
	unset($params['a']);
	$url_base = [
		'c' => 'index',
		'a' => 'normal',
		'params' => $params,
	];

	$unreadArticles = 0;

	foreach ($this->categories as $cat) {
		$feeds = $cat->feeds();
		$url_base['params']['get'] = 'c_' . $cat->id();

		if (!empty($feeds)) {
			$unreadArticles += $cat->nbNotRead();
?>
	<div class="box category" data-unread="<?= $cat->nbNotRead() ?>">
		<div class="box-title"><a class="title" data-unread="<?= format_number($cat->nbNotRead()) ?>"
			href="<?= Minz_Url::display($url_base) ?>"><h2><?= $cat->name() ?></h2></a></div>

		<ul class="box-content scrollbar-thin">
			<?php
				foreach ($feeds as $feed) {
					$nb_not_read = $feed->nbNotRead();

					$error_class = '';
					$error_title = '';
					if ($feed->inError() && !$feed->mute()) {
						$error_class = ' error';
						$error_title = _t('sub.feed.error');
					}

					$empty_class = '';
					$empty_title = '';
					if ($feed->nbEntries() == 0) {
						$empty_class = ' empty';
						$empty_title = _t('sub.feed.empty');
					}
					$mute_class = $feed->mute() ? ' mute' : '';

					$url_base['params']['get'] = 'f_' . $feed->id();
			?>
			<li id="f_<?= $feed->id() ?>" class="item feed<?= $error_class, $empty_class, $mute_class ?>" title="<?= $error_title, $empty_title ?>"
				data-unread="<?= $feed->nbNotRead() ?>" data-priority="<?= $feed->priority() ?>">
				<?php if (FreshRSS_Context::userConf()->show_favicons): ?><img class="favicon" src="<?= $feed->favicon() ?>" alt="✇" loading="lazy" /><?php endif; ?>
				<a class="item-title" data-unread="<?= format_number($feed->nbNotRead()) ?>" href="<?= Minz_Url::display($url_base) ?>"><?= $feed->name() ?></a>
			</li>
			<?php } ?>
		</ul>
	</div>
<?php
		}
	}

	if ($unreadArticles < 1 && $state_unread) {
		?>
	<div id="noArticlesToShow" class="prompt alert alert-warn">
		<h2 class="alert-head"><?= _t('index.feed.empty') ?></h2>
	</div>
	<?php } ?>
</main>

<div id="overlay">
	<a class="close" href="#"><?= _i('close') ?></a>
	<div id="panel"<?= FreshRSS_Context::userConf()->display_posts ? '' : ' class="hide_posts"' ?>>
</div>
</div>
