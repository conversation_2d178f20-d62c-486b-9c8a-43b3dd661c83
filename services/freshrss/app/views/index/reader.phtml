<?php
declare(strict_types=1);
/** @var FreshRSS_View $this */
if (!Minz_Request::paramBoolean('ajax')) {
	$this->partial('aside_feed');
	$this->partial('nav_menu');
}

call_user_func($this->callbackBeforeEntries, $this);

$lazyload = FreshRSS_Context::userConf()->lazyload;
$useKeepUnreadImportant = !FreshRSS_Context::isImportant() && !FreshRSS_Context::isFeed();
?>
<main id="stream" class="reader">
	<h1 class="title_hidden"><?= _t('conf.reading.view.reader') ?></h1>
	<div id="new-article" hidden="hidden">
		<a href="<?= Minz_Url::display(Minz_Request::currentRequest()) ?>"><?= _t('gen.js.new_article'); /* TODO: move string in JS*/ ?></a>
	</div><?php
	$lastEntry = null;
	$nbEntries = 0;
	foreach ($this->entries as $entry):
		/** @var FreshRSS_Entry|null $entry */
		$entry = Minz_ExtensionManager::callHook('entry_before_display', $entry);
		if ($entry === null) {
			continue;
		}
		ob_flush();
		$this->entry = $entry;
		$lastEntry = $entry;
		$nbEntries++;

		//We most likely already have the feed object in cache, otherwise make a request
		$this->feed = FreshRSS_Category::findFeed($this->categories, $this->entry->feedId()) ?? $this->entry->feed() ?? FreshRSS_Feed::default();
	?><div class="flux<?= !$this->entry->isRead() ? ' not_read' : ''
		?><?= $this->entry->isFavorite() ? ' favorite' : ''
		?><?= $useKeepUnreadImportant && ($this->feed->priority() >= FreshRSS_Feed::PRIORITY_IMPORTANT) ? ' keep_unread ' : ''
		?>" id="flux_<?= $this->entry->id()
		?>" data-entry="<?= $this->entry->id()
		?>" data-feed="<?= $this->feed->id()
		?>" data-priority="<?= $this->feed->priority()
		?>"><?php $this->renderHelper('index/article'); ?>
	</div><?php
	endforeach;

	if ($nbEntries > 0):
		call_user_func($this->callbackBeforePagination, $this, $nbEntries, $lastEntry);
		$this->renderHelper('stream-footer');
?></main><?php
	else:
		ob_end_clean();	//Discard the articles headers, as we have no articles
?>
<main id="stream" class="reader">
	<div id="new-article" hidden="hidden">
		<a href="<?= Minz_Url::display(Minz_Request::currentRequest()) ?>"><?= _t('gen.js.new_article'); /* TODO: move string in JS*/ ?></a>
	</div>
	<div class="prompt alert alert-warn">
		<h2 class="alert-head"><?= _t('index.feed.empty') ?></h2>
	</div>
</main>
<?php endif; ?>
