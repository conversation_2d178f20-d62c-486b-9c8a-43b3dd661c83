<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_configure');
?>
<main class="post">
	<h1><?= _t('admin.update') ?></h1>

	<?php if (!empty($this->message)) { ?>
	<?php
		$class = 'alert-warn';
		switch ($this->message['status']) {
		case 'bad':
			$class = 'alert-error';
			break;
		case 'latest':
			$class = 'alert-success';
			break;
		default:
			$class = 'alert-warn';
			break;
		}
	?>
	<p class="alert <?= $class ?>">
		<?= $this->message['body'] ?>
	</p>
	<?php } ?>

	<div class="form-group">
		<label class="group-name"><?= _t('admin.update.current_version') ?></label>
		<div class="group-controls">
			<?= FRESHRSS_VERSION ?> (<a href="https://github.com/FreshRSS/FreshRSS/releases" target="_blank"><?= _t('admin.update.changelog') ?></a>)
		</div>
	</div>

	<div class="form-group">
		<label class="group-name"><?= _t('admin.update.releaseChannel') ?></label>
		<div class="group-controls">
			<?php if ($this->is_release_channel_stable) { ?>
				<a href="https://github.com/FreshRSS/FreshRSS/releases/latest" target="_blank">
					<?= _t('admin.update.releaseChannel.latest') ?>
				</a>
			<?php } else { ?>
				<a href="https://github.com/FreshRSS/FreshRSS/tree/edge" target="_blank">
					<?= _t('admin.update.releaseChannel.edge') ?>
				</a>
			<?php } ?>
		</div>
	</div>

	<?php if (!FreshRSS_Context::systemConf()->disable_update): ?>
	<div class="form-group">
		<label class="group-name"><?= _t('admin.update.last') ?></label>
		<div class="group-controls">
		<?= $this->last_update_time ?>
		</div>
	</div>

	<?php
		if (empty($this->message) || $this->message['status'] !== 'good') {
	?>
	<div class="form-group form-actions">
		<div class="group-controls">
			<a href="<?= _url('update', 'check') ?>" class="btn btn-important"><?= _t('admin.update.check') ?></a>
		</div>
	</div>
	<?php } ?>

	<?php if ($this->update_to_apply) { ?>
	<div class="form-group form-actions">
		<div class="group-controls">
			<a class="btn btn-attention btn-state1" href="<?= _url('update', 'apply') ?>" data-state2-id="button-update-loading"><?= _t('admin.update.apply') ?></a>
			<span class="btn btn-state2" id="button-update-loading"><?= _t('admin.update.loading') ?></span>
		</div>
	</div>
	<?php } ?>
	<?php endif; ?>
</main>
