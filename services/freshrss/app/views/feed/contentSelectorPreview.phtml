<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	FreshRSS::preLayout();
	$class = '';
	$dir = '';
	if (_t('gen.dir') === 'rtl') {
		$dir = ' dir="rtl"';
		$class = ' rtl ';
	}
	if (FreshRSS_Context::userConf()->darkMode !== 'no') {
		$class .= ' darkMode_' . FreshRSS_Context::userConf()->darkMode;
	}
?>
<!DOCTYPE html>
<html class="preview_background<?= $class ?>" lang="<?= FreshRSS_Context::userConf()->language ?>"<?= $dir ?> xml:lang="<?= FreshRSS_Context::userConf()->language ?>">
	<head>
		<?= FreshRSS_View::headStyle() ?>
		<script src="<?= Minz_Url::display('/scripts/preview.js?' . @filemtime(PUBLIC_PATH . '/scripts/preview.js')) ?>"></script>
	</head>
	<body class="preview_background">
		<?php if ($this->fatalError != '') { ?>
			<p class="alert alert-warn"><?= $this->fatalError ?></p>
		<?php } else { ?>
			<?php if ($this->selectorSuccess === false) { ?>
				<p class="alert alert-warn">
					<?= _t('feedback.sub.feed.selector_preview.no_result') ?>
				</p>
			<?php } ?>

			<div class="preview_controls">
				<label for="freshrss_rendered">
					<input type="radio" id="freshrss_rendered" name="freshrss_type" checked="checked" />
					<?= _t('sub.feed.selector_preview.show_rendered') ?>
				</label>

				<label for="freshrss_raw">
					<input type="radio" id="freshrss_raw" name="freshrss_type" />
					<?= _t('sub.feed.selector_preview.show_raw') ?>
				</label>
			</div>

			<div class="content content_large">
				<div dir="auto" id="freshrss_rendered_view"><?= $this->htmlContent ?></div>
				<pre id="freshrss_raw_view" hidden="hidden"> <?= htmlspecialchars($this->htmlContent) ?></pre>
			</div>
		<?php } ?>
	</body>
</html>
