<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	$this->partial('aside_configure');
?>
<main class="post">
	<h1><?= _t('admin.extensions.title') ?></h1>
	<form id="form-extension" method="post">
	<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
	<?php if (!empty($this->extension_list['system'])) { ?>
	<h2><?= _t('admin.extensions.system') ?></h2>
	<ul class="manage-list">
	<?php
		foreach ($this->extension_list['system'] as $ext) {
			$this->ext_details = $ext; ?>
			<li>
			<?php $this->renderHelper('extension/details'); ?>
			</li>
		<?php } ?>
	</ul>
	<?php } ?>

	<?php if (!empty($this->extension_list['user'])) { ?>
	<h2><?= _t('admin.extensions.user') ?></h2>
	<ul class="manage-list">
	<?php
		foreach ($this->extension_list['user'] as $ext) {
			$this->ext_details = $ext; ?>
			<li>
			<?php $this->renderHelper('extension/details'); ?>
			</li>
		<?php } ?>
		</ul>
	<?php }

	if (empty($this->extension_list['system']) && empty($this->extension_list['user'])) {
	?>
	<p class="alert alert-warn"><?= _t('admin.extensions.empty_list') ?></p>
	<?php } ?>
	</form>

	<?php if (!empty($this->available_extensions)) { ?>
		<h2><?= _t('admin.extensions.community') ?></h2>
		<div class="table-wrapper scrollbar-thin">
			<table>
				<tr>
					<th><?= _t('admin.extensions.name') ?></th>
					<th><?= _t('admin.extensions.version') ?></th>
					<th><?= _t('admin.extensions.author') ?></th>
					<th><?= _t('admin.extensions.description') ?></th>
				</tr>
				<?php foreach ($this->available_extensions as $ext) { ?>
					<tr>
						<td><a href="<?= $ext['url'] ?>" target="_blank"><?= $ext['name'] ?></a></td>
						<td><?= $ext['version'] ?></td>
						<td><?= $ext['author'] ?></td>
						<td>
							<?= $ext['description'] ?>
							<?php if (isset($this->extensions_installed[$ext['entrypoint']])) { ?>
								<?php if (version_compare(strval($this->extensions_installed[$ext['entrypoint']]), strval($ext['version'])) >= 0) { ?>
									<span class="alert alert-success">
										<?= _t('admin.extensions.latest') ?>
									</span>
								<?php } elseif (strval($this->extensions_installed[$ext['entrypoint']]) !== strval($ext['version'])) { ?>
									<span class="alert alert-warn">
										<?= _t('admin.extensions.update') ?>
									</span>
								<?php } ?>
							<?php } ?>
						</td>
					</tr>
				<?php } ?>
			</table>
		</div>
	<?php } else { ?>
		<p class="alert alert-warn"><?= _t('admin.extensions.empty_list_help') ?></p>
	<?php } ?>
</main>

<?php $class = isset($this->extension) ? ' active' : ''; ?>
<?php $closelink = isset($this->extension) ? _url('extension', 'index') : ''; ?>
<aside id="slider">
<a class="toggle_aside" href="<?= $closelink ?>#close"><img class="icon" src="../themes/icons/close.svg" loading="lazy" alt="❌"></a>
	<div id="slider-content">
		<?php
			if (isset($this->extension)) {
				$this->renderHelper('extension/configure');
			}
		?>
	</div>
</aside>
<a href="#" id="close-slider">
	<?= _i('close') ?>
</a>
