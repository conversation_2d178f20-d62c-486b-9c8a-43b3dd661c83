<?php
	declare(strict_types=1);
	/** @var FreshRSS_ViewStats $this */
	$this->partial('aside_subscription');
?>
<main class="post">
	<h1><?= _t('admin.stats.idle') ?></h1>

	<?php
		$current_url = Minz_Url::display(
			['c' => 'stats', 'a' => 'idle'],
			'php', true
		);
		$nothing = true;
		foreach ($this->idleFeeds as $period => $feedsInPeriod) {
			if (!empty($feedsInPeriod)) {
				$nothing = false;
	?>
	<div class="box">
		<div class="box-title"><h2><?= _t('gen.date.' . $period) ?></h2></div>
			<ul class="box-content scrollbar-thin">
				<?php
					foreach ($feedsInPeriod as $feedInPeriod) {
						$feed = $this->feeds[$feedInPeriod['id']] ?? null;

						$error_class = '';
						$error_title = '';
						if ($feed === null || $feed->inError()) {
							$error_class = ' error';
							$error_title = _t('sub.feed.error');
						}

						$empty_class = '';
						$empty_title = '';
						if ($feed !== null && $feed->nbEntries() == 0) {
							$empty_class = ' empty';
							$empty_title = _t('sub.feed.empty');
						}
						$mute_class = ($feed !== null && $feed->mute()) ? ' mute' : '';
				?>
					<li class="item feed<?= $error_class, $empty_class, $mute_class ?>" title="<?= $error_title, $empty_title ?>">
						<a class="configure open-slider" href="<?= _url('stats', 'feed', 'id', $feedInPeriod['id'], 'sub', 'idle') ?>" title="<?= _t('gen.action.manage') ?>"><?= _i('configure') ?></a><?php
						if (FreshRSS_Context::userConf()->show_favicons): ?><img class="favicon" src="<?= $feedInPeriod['favicon'] ?>" alt="✇" loading="lazy" /><?php
						endif; ?><span title="<?= timestamptodate((int)($feedInPeriod['last_date']), false) ?>"><?= $feedInPeriod['name'] ?>
							(<?= _t('admin.stats.number_entries', $feedInPeriod['nb_articles']) ?>)</span>
					</li>
				<?php } ?>
			</ul>
	</div>
	<?php
			}
		}

		if ($nothing) {
	?>
	<p class="alert alert-warn">
		<span class="alert-head"><?= _t('admin.stats.no_idle') ?></span>
	</p>
	<?php } ?>
</main>

<?php $class = isset($this->feed) ? ' active' : ''; ?>
<aside id="slider" class="<?= $class ?>">
	<a class="toggle_aside" href="#close"><img class="icon" src="../themes/icons/close.svg" loading="lazy" alt="❌"></a>
	<div id="slider-content">
		<?php
		if (isset($this->feed)) {
			$this->renderHelper('feed/update');
		}
		?>
	</div>
</aside>
<a href="#" id="close-slider" class="<?= $class ?>">
	<?= _i('close') ?>
</a>
