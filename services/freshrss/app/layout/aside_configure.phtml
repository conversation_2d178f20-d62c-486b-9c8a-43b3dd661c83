<?php
	declare(strict_types=1);
?>
<nav class="nav nav-list aside" id="aside_feed">
	<form id="post-csrf" method="post">
		<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
	</form>
	<a class="toggle_aside" href="#close"><?= _i('close') ?></a>

	<ul>
		<li class="item nav-section">
			<ul>
				<li class="item">
					<a href="<?= _url('index', 'index') ?>"><?= _t('gen.action.back_to_rss_feeds') ?></a>
				</li>
			</ul>
		</li>
		<li class="item nav-section">
			<div class="item nav-header"><?= _t('gen.menu.account') ?>: <?= htmlspecialchars(Minz_User::name() ?? '', ENT_NOQUOTES, 'UTF-8') ?></div>
			<ul>
				<li class="item<?= Minz_Request::controllerName() === 'user' && Minz_Request::actionName() === 'profile' ? ' active' : '' ?>">
					<a href="<?= _url('user', 'profile') ?>"><?= _t('gen.menu.user_profile') ?></a>
				</li>
				<li class="item">
					<button class="as-link signout" form="post-csrf" formaction="<?=
						FreshRSS_auth_Controller::getLogoutUrl() ?>"><?= _t('gen.auth.logout'); ?><?= _i('logout') ?></button>
				</li>
			</ul>
		</li>

		<li class="item nav-section">
			<div class="item nav-header"><?= _t('gen.menu.configuration') ?></div>
			<ul>
				<li class="item<?= Minz_Request::actionName() === 'display' ? ' active' : '' ?>">
					<a href="<?= _url('configure', 'display') ?>"><?= _t('gen.menu.display') ?></a>
				</li>
				<li class="item<?= Minz_Request::actionName() === 'reading' ? ' active' : '' ?>">
					<a href="<?= _url('configure', 'reading') ?>"><?= _t('gen.menu.reading') ?></a>
				</li>
				<li class="item<?= Minz_Request::actionName() === 'archiving' ? ' active' : '' ?>">
					<a href="<?= _url('configure', 'archiving') ?>"><?= _t('gen.menu.archiving') ?></a>
				</li>
				<li class="item<?= Minz_Request::actionName() === 'integration' ? ' active' : '' ?>">
					<a href="<?= _url('configure', 'integration') ?>"><?= _t('gen.menu.sharing') ?></a>
				</li>
				<li class="item<?= Minz_Request::actionName() === 'shortcut' ? ' active' : '' ?>">
					<a href="<?= _url('configure', 'shortcut') ?>"><?= _t('gen.menu.shortcuts') ?></a>
				</li>
				<li class="item<?= in_array(Minz_Request::actionName(), ['queries', 'query'], true) ? ' active' : '' ?>">
					<a href="<?= _url('configure', 'queries') ?>"><?= _t('gen.menu.queries') ?></a>
				</li>
				<li class="item<?= Minz_Request::controllerName() === 'extension' ? ' active' : '' ?>">
					<a href="<?= _url('extension', 'index') ?>"><?= _t('gen.menu.extensions') ?></a>
				</li>
				<li class="item<?= Minz_Request::controllerName() === 'privacy' ? ' active' : '' ?>">
					<a href="<?= _url('configure', 'privacy') ?>"><?= _t('gen.menu.privacy') ?></a>
				</li>
				<?php if (!FreshRSS_Auth::hasAccess('admin')) { ?>
				<li class="item<?= Minz_Request::actionName() === 'logs' ? ' active' : '' ?>">
					<a href="<?= _url('index', 'logs') ?>"><?= _t('gen.menu.logs') ?></a>
				</li>
				<?php } ?>
				<?= Minz_ExtensionManager::callHookString('menu_configuration_entry') ?>
			</ul>
		</li>

		<?php if (FreshRSS_Auth::hasAccess('admin')) { ?>
		<li class="item nav-section">
			<div class="item nav-header"><?= _t('gen.menu.admin') ?></div>
			<ul>
				<li class="item<?= Minz_Request::actionName() === 'system' ? ' active' : '' ?>">
					<a href="<?= _url('configure', 'system') ?>"><?= _t('gen.menu.system') ?></a>
				</li>
				<li class="item<?= Minz_Request::controllerName() === 'user' && in_array(Minz_Request::actionName(), ['manage', 'details'], true) ? ' active' : '' ?>">
					<a href="<?= _url('user', 'manage') ?>"><?= _t('gen.menu.user_management') ?></a>
				</li>
				<li class="item<?= Minz_Request::controllerName() === 'auth' ? ' active' : '' ?>">
					<a href="<?= _url('auth', 'index') ?>"><?= _t('gen.menu.authentication') ?></a>
				</li>
				<li class="item<?= Minz_Request::controllerName() === 'update' && Minz_Request::actionName() === 'checkInstall' ? ' active' : '' ?>">
					<a href="<?= _url('update', 'checkInstall') ?>"><?= _t('gen.menu.check_install') ?></a>
				</li>
				<?php if (!FreshRSS_Context::systemConf()->disable_update) { ?>
				<li class="item<?= Minz_Request::controllerName() === 'update' && Minz_Request::actionName() === 'index' ? ' active' : '' ?>">
					<a href="<?= _url('update', 'index') ?>"><?= _t('gen.menu.update') ?></a>
				</li>
				<?php } ?>
				<li class="item<?= Minz_Request::actionName() === 'logs' ? ' active' : '' ?>">
					<a href="<?= _url('index', 'logs') ?>"><?= _t('gen.menu.logs') ?></a>
				</li>
				<?= Minz_ExtensionManager::callHookString('menu_admin_entry') ?>
			</ul>
		</li>
		<?php } ?>

		<li class="item nav-section">
			<div class="item nav-header"><!-- empty headline --></div>
			<ul>
				<li class="item<?= Minz_Request::actionName() === 'about' ? ' active' : '' ?>">
					<a href="<?= _url('index', 'about') ?>"><?= _t('gen.menu.about') ?></a>
				</li>
				<?php if (file_exists(TOS_FILENAME)) { ?>
					<li class="item<?= Minz_Request::actionName() === 'tos' ? ' active' : '' ?>">
						<a href="<?= _url('index', 'tos') ?>"><?= _t('index.tos.title')?></a>
					</li>
				<?php } ?>
			</ul>
		</li>
	</ul>
</nav>
<a class="close-aside" href="#close">❌</a>
<nav class="nav_menu nav_mobile">
	<a class="btn toggle_aside" href="#aside_feed"><?= _i('category') ?></a>
</nav>
