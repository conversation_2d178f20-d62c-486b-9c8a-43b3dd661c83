<?php
	declare(strict_types=1);
	$actual_view = Minz_Request::actionName();
?>
<nav class="nav_menu">
	<?php if ($actual_view === 'normal' || $actual_view === 'reader') { ?>
		<div id="nav_menu_toggle_aside" class="group">
			<a class="btn toggle_aside" href="#aside_feed"><?= _i('category') ?></a>
		</div>
	<?php } ?>

	<?php if (FreshRSS_Auth::hasAccess()) { ?>
	<div id="nav_menu_actions" class="group">
		<?php
			$states = [
				'read' => FreshRSS_Entry::STATE_READ,
				'unread' => FreshRSS_Entry::STATE_NOT_READ,
				'starred' => FreshRSS_Entry::STATE_FAVORITE,
				'non-starred' => FreshRSS_Entry::STATE_NOT_FAVORITE,
			];

			foreach ($states as $state_str => $state) {
				$state_enabled = FreshRSS_Context::isStateEnabled($state);
				$url_state = Minz_Request::currentRequest();
				$reverted_state = FreshRSS_Context::getRevertState($state);
				$reverted_state &= FreshRSS_Entry::STATE_ANDS;	// Keep only the AND states
				$url_state['params']['state'] = $reverted_state;
		?>
		<a id="toggle-<?= $state_str ?>"
			class="btn <?= $state_enabled ? 'active' : '' ?>"
			role="checkbox" aria-checked="<?= $state_enabled ? 'true' : 'false' ?>"
			title="<?= _t('index.menu.' . $state_str) ?>"
			href="<?= Minz_Url::display($url_state) ?>"><?= _i($state_str) ?></a>
		<?php } ?>

		<div id="dropdown-search-wrapper" class="dropdown only-mobile">
			<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
			<div id="dropdown-search" class="dropdown-target"></div>

			<a id="toggle-search" class="dropdown-toggle btn<?= (strlen(FreshRSS_Context::$search->getRawInput()) > 0) ? ' active' : ''; ?>" title="<?= _t('gen.menu.search') ?>"
				href="#dropdown-search"><?= _i('search') ?></a>
			<ul class="dropdown-menu">
				<li class="item">
					<span>
						<form action="<?= _url('index', 'index') ?>" method="get">
							<?php if (in_array(Minz_Request::actionName(), ['normal', 'global', 'reader'], true)) { ?>
							<input type="hidden" name="a" value="<?= Minz_Request::actionName() ?>" />
							<?php } if (Minz_Request::paramString('get') !== '') { ?>
							<input type="hidden" name="get" value="<?= FreshRSS_Context::currentGet() ?>" />
							<?php } if (ctype_upper(Minz_Request::paramString('order'))) { ?>
							<input type="hidden" name="order" value="<?= FreshRSS_Context::$order ?>" />
							<?php } if (Minz_Request::paramInt('state') !== 0) { ?>
							<input type="hidden" name="state" value="<?= FreshRSS_Context::$state ?>" />
							<?php } ?>
							<div class="stick search">
								<input type="search" name="search"
									value="<?= htmlspecialchars(htmlspecialchars_decode(FreshRSS_Context::$search->getRawInput(), ENT_QUOTES), ENT_COMPAT, 'UTF-8'); ?>"
									placeholder="<?= _t('gen.menu.search') ?>" title="<?= _t('gen.menu.search') ?>" /><button class="btn" type="submit" title="<?= _t('index.menu.search_short') ?>"><?= _i('search') ?></button>
							</div>
							<p class="help"><?= _i('help') ?> <?= _t('gen.menu.search_help') ?></a></p>
						</form>
					</span>
				</li>
			</ul>
			<a class="dropdown-close" href="#close">❌</a>
		</div>

		<div id="nav_menu_queries" class="dropdown">
			<div id="dropdown-query" class="dropdown-target"></div>

			<a id="toggle-userqueries" class="dropdown-toggle btn" href="#dropdown-query" title="<?= _t('index.menu.queries') ?>"><?= _i('bookmark-tag') ?></a>
			<ul class="dropdown-menu">
				<li class="dropdown-header">
					<?= _t('index.menu.queries') ?>
					<a href="<?= _url('configure', 'queries') ?>"><?= _i('configure') ?></a>
				</li>

				<?php foreach (FreshRSS_Context::userConf()->queries as $raw_query): ?>
					<li class="item query">
						<?php if (!empty($raw_query['url'])): ?>
							<a href="<?= $raw_query['url'] ?>"><?= $raw_query['name'] ?? $raw_query['url'] ?></a>
						<?php else: ?>
						<?php $query = new FreshRSS_UserQuery($raw_query, FreshRSS_Context::categories(), FreshRSS_Context::labels()); ?>
							<a href="<?= $query->getUrl() ?>"><?= $query->getName() ?></a>
						<?php endif; ?>
					</li>
				<?php endforeach; ?>

				<?php
					$classSeparator = '';
					if (count(FreshRSS_Context::userConf()->queries) > 0) {
						$classSeparator = ' separator';
					}

					$url_query = Minz_Request::currentRequest();
					$url_query['c'] = 'configure';
					$url_query['a'] = 'bookmarkQuery';
				?>
				<li class="item<?= $classSeparator ?>"><a href="<?= Minz_Url::display($url_query) ?>"><?= _i('bookmark-add') ?> <?= _t('index.menu.bookmark_query') ?></a></li>
			</ul>
			<a class="dropdown-close" href="#close">❌</a>
		</div>
	</div>

	<?php
		$get = FreshRSS_Context::currentGet();
		$string_mark = _t('index.menu.mark_all_read');
		$string_unmark = _t('index.menu.mark_selection_unread');
		if ($get[0] === 'f') {
			$string_mark = _t('index.menu.mark_feed_read');
		} elseif ($get[0] === 'c') {
			$string_mark = _t('index.menu.mark_cat_read');
		}

		$mark_read_url = [
			'c' => 'entry',
			'a' => 'read',
			'params' => [
				'get' => $get,
				'nextGet' => FreshRSS_Context::$next_get,
				'idMax' => FreshRSS_Context::$id_max,
				'search' => htmlspecialchars_decode(FreshRSS_Context::$search->getRawInput(), ENT_QUOTES),
				'state' => FreshRSS_Context::$state,
			],
		];

		$mark_unread_url = $mark_read_url;
		$mark_unread_url['params']['is_read'] = '0';
		$mark_unread_url['params']['nextGet'] = $get;
	?>

	<div id="nav_menu_read_all" class="group stick">
		<form id="mark-read-menu" method="post">
		<?php $confirm = FreshRSS_Context::userConf()->reading_confirm ? 'confirm" disabled="disabled' : ''; ?>
		<button class="read_all btn <?= $confirm ?>"
			form="mark-read-menu"
			formaction="<?= Minz_Url::display($mark_read_url) ?>"
			type="submit"><?= _t('gen.action.mark_read') ?></button>

		<div id="nav_menu_read" class="dropdown">
			<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
			<div id="dropdown-read" class="dropdown-target"></div>

			<a class="dropdown-toggle btn" href="#dropdown-read"><?= _i('down') ?></a>
			<ul class="dropdown-menu">
				<li class="item">
					<button class="as-link <?= $confirm ?>"
						form="mark-read-menu"
						formaction="<?= Minz_Url::display($mark_read_url) ?>"
						type="submit"><?= $string_mark ?></button>
				</li>
<?php
	$mark_read_enabled = FreshRSS_Context::$sort === 'id';
	$today = @strtotime('today');
	$mark_before_today = $mark_read_url;
	$mark_before_today['params']['idMax'] = $today . '000000';
	$mark_before_one_week = $mark_read_url;
	$mark_before_one_week['params']['idMax'] = ($today - 604800) . '000000';
	$mark_unread_enabled = FreshRSS_Context::isStateEnabled(FreshRSS_Entry::STATE_READ) ||
		(!FreshRSS_Context::isStateEnabled(FreshRSS_Entry::STATE_NOT_READ) && !FreshRSS_Context::isStateEnabled(FreshRSS_Entry::STATE_OR_NOT_READ));
?>
				<li class="item separator">
					<button class="as-link <?= $mark_read_enabled ? $confirm : '" disabled="disabled' ?>"
						form="mark-read-menu"
						formaction="<?= Minz_Url::display($mark_before_today) ?>"
						type="submit"><?= _t('index.menu.before_one_day') ?></button>
				</li>
				<li class="item">
					<button class="as-link <?= $mark_read_enabled ? $confirm : '" disabled="disabled' ?>"
						form="mark-read-menu"
						formaction="<?= Minz_Url::display($mark_before_one_week) ?>"
						type="submit"><?= _t('index.menu.before_one_week') ?></button>
				</li>
				<li class="item separator">
					<button class="as-link <?= $mark_unread_enabled ? $confirm : '" disabled="disabled' ?>"
						form="mark-read-menu"
						formaction="<?= Minz_Url::display($mark_unread_url) ?>"
						type="submit"><?= $string_unmark ?></button>
				</li>
			</ul>
			<a class="dropdown-close" href="#close">❌</a>
		</div>
		</form>
	</div>
	<?php } ?>

	<?php $url_output = Minz_Request::currentRequest(); ?>
	<div id="nav_menu_views" class="group">
		<?php
		$readingModes = FreshRSS_ReadingMode::getReadingModes();
		$readingModes = Minz_ExtensionManager::callHook('nav_reading_modes', $readingModes);
		if (!is_iterable($readingModes)) {
			$readingModes = FreshRSS_ReadingMode::getReadingModes();
		}

		/** @var FreshRSS_ReadingMode $mode */
		foreach ($readingModes as $mode) {
			?>
			<a class="<?= $mode->getId() ?> btn <?php if ($mode->isActive()) { echo 'active'; } ?>" title="<?=
				$mode->getTitle() ?>" href="<?= Minz_Url::display($mode->getUrlParams()) ?>">
				<?= $mode->getName() ?>
			</a>
			<?php
		}
		?>
	</div>

	<?php $nav_menu_hooks = Minz_ExtensionManager::callHookString('nav_menu'); ?>
	<?php if ($nav_menu_hooks != '') { ?>
	<div id="nav_menu_hooks" class="group">
		<?= $nav_menu_hooks ?>
	</div>
	<?php } ?>

	<?php
		if (FreshRSS_Context::$order === 'ASC') {
			$icon = 'sort-up';
			$title = _t('index.menu.older_first');
		} else {
			$icon = 'sort-down';
			$title = _t('index.menu.newer_first');
		}
		$url_order = Minz_Request::currentRequest();
	?>
	<div id="nav_menu_sort" class="group">
		<div class="dropdown">
			<div id="dropdown-sort" class="dropdown-target"></div>
			<a id="toggle-order" class="dropdown-toggle btn" href="#dropdown-sort" title="<?= _t('index.menu.sort') ?>"><?= _i($icon) ?></a>
			<ul class="dropdown-menu" role="radiogroup">
				<li class="item" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'DESC' && FreshRSS_Context::$sort === 'id' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'id', 'order' => 'DESC']]) ?>"><?= _t('index.menu.sort.id_desc') ?></a></li>
				<li class="item" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'DESC' && FreshRSS_Context::$sort === 'date' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'date', 'order' => 'DESC']]) ?>"><?= _t('index.menu.sort.date_desc') ?></a></li>
				<li class="item" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'DESC' && FreshRSS_Context::$sort === 'link' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'link', 'order' => 'DESC']]) ?>"><?= _t('index.menu.sort.link_desc') ?></a></li>
				<li class="item" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'DESC' && FreshRSS_Context::$sort === 'title' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'title', 'order' => 'DESC']]) ?>"><?= _t('index.menu.sort.title_desc') ?></a></li>
				<li class="item" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'DESC' && FreshRSS_Context::$sort === 'f.name' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'f.name', 'order' => 'DESC']]) ?>"><?= _t('index.menu.sort.f.name_desc') ?></a></li>
				<li class="item" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'DESC' && FreshRSS_Context::$sort === 'c.name' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'c.name', 'order' => 'DESC']]) ?>"><?= _t('index.menu.sort.c.name_desc') ?></a></li>
				<li class="item separator" role="radio" aria-checked="<?= FreshRSS_Context::$sort === 'rand' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'rand', 'order' => null]]) ?>"><?= _t('index.menu.sort.rand') ?></a></li>
				<li class="item separator" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'ASC' && FreshRSS_Context::$sort === 'id' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'id', 'order' => 'ASC']]) ?>"><?= _t('index.menu.sort.id_asc') ?></a></li>
				<li class="item" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'ASC' && FreshRSS_Context::$sort === 'date' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'date', 'order' => 'ASC']]) ?>"><?= _t('index.menu.sort.date_asc') ?></a></li>
				<li class="item" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'ASC' && FreshRSS_Context::$sort === 'link' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'link', 'order' => 'ASC']]) ?>"><?= _t('index.menu.sort.link_asc') ?></a></li>
				<li class="item" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'ASC' && FreshRSS_Context::$sort === 'title' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'title', 'order' => 'ASC']]) ?>"><?= _t('index.menu.sort.title_asc') ?></a></li>
				<li class="item" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'ASC' && FreshRSS_Context::$sort === 'f.name' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'f.name', 'order' => 'ASC']]) ?>"><?= _t('index.menu.sort.f.name_asc') ?></a></li>
				<li class="item" role="radio" aria-checked="<?= FreshRSS_Context::$order === 'ASC' && FreshRSS_Context::$sort === 'c.name' ? 'true' : 'false' ?>">
					<a href="<?= Minz_Url::display($url_order, amend: ['params' => ['sort' => 'c.name', 'order' => 'ASC']]) ?>"><?= _t('index.menu.sort.c.name_asc') ?></a></li>
			</ul>
			<a class="dropdown-close" href="#close">❌</a>
		</div>
	</div>

	<?php if (FreshRSS_Auth::hasAccess() || FreshRSS_Context::systemConf()->allow_anonymous_refresh) { ?>
		<div id="nav_menu_actualize" class="group">
			<a id="actualize" class="btn" href="<?= _url('feed', 'actualize') ?>" title="<?= _t('gen.action.actualize') ?>"><?= _i('refresh') ?></a>
		</div>
	<?php } ?>
</nav>
<?php flush(); ?>
