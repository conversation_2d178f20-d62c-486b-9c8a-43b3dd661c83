<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
?>
<header class="header">
	<div class="item title">
		<a href="<?= Minz_Url::display(['c' => 'index', 'a' => 'index'], 'html', 'root') ?>">
			<?php if (FreshRSS_Context::systemConf()->logo_html == '') { ?>
				<img class="logo" src="<?= _i('FreshRSS-logo', FreshRSS_Themes::ICON_URL) ?>" alt="FreshRSS" loading="lazy" />
			<?php
				} else {
					echo FreshRSS_Context::systemConf()->logo_html;
				}
			?>
		</a>
	</div>

	<div class="item search">
		<?php if (FreshRSS_Auth::hasAccess() || FreshRSS_Context::systemConf()->allow_anonymous) { ?>
		<form action="<?= $this->html_url ?>" method="get">
			<?php if (Minz_Request::controllerName() === 'index'): ?>
				<?php if (in_array(Minz_Request::actionName(), ['normal', 'global', 'reader'], true)) { ?>
				<input type="hidden" name="a" value="<?= Minz_Request::actionName() ?>" />
				<?php } if (Minz_Request::paramString('get') !== '') { ?>
				<input type="hidden" name="get" value="<?= FreshRSS_Context::currentGet() ?>" />
				<?php } if (Minz_Request::paramInt('state') !== 0) { ?>
				<input type="hidden" name="state" value="<?= Minz_Request::paramInt('state') ?>" />
				<?php } ?>
			<?php endif; ?>
			<?php if (Minz_Request::paramString('user') !== '') { ?>
			<input type="hidden" name="user" value="<?= Minz_User::name() ?>" />
			<?php } if (ctype_alnum(Minz_Request::paramString('t'))) { ?>
			<input type="hidden" name="t" value="<?= Minz_Request::paramString('t') ?>" />
			<?php } if (ctype_lower(Minz_Request::paramString('sort'))) { ?>
			<input type="hidden" name="sort" value="<?= FreshRSS_Context::$sort ?>" />
			<?php } if (ctype_upper(Minz_Request::paramString('order'))) { ?>
			<input type="hidden" name="order" value="<?= FreshRSS_Context::$order ?>" />
			<?php } if (ctype_lower(Minz_Request::paramString('f'))) { ?>
			<input type="hidden" name="f" value="<?= Minz_Request::paramString('f') ?>" />
			<?php } ?>
			<div class="stick">
				<input type="search" name="search" id="search"
					value="<?= htmlspecialchars(htmlspecialchars_decode(Minz_Request::paramString('search'), ENT_QUOTES), ENT_COMPAT, 'UTF-8') ?>"
					placeholder="<?= _t('gen.menu.search') ?>" />
				<button class="btn" type="submit"><?= _i('search') ?></button>
			</div>
		</form>
		<?php } ?>
	</div>

	<?php if (FreshRSS_Auth::hasAccess()) { ?>
	<nav class="item configure">
		<form id="post-csrf" method="post">
			<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
		</form>
		<div class="dropdown">
			<div id="dropdown-configure" class="dropdown-target"></div>
			<a class="btn dropdown-toggle" href="#dropdown-configure"><?= _i('configure') ?></a>
			<ul class="dropdown-menu scrollbar-thin">
				<li class="dropdown-header-close"><a class="toggle_aside" href="#close"><?= _i('close') ?></a></li>

				<li class="item dropdown-section">
					<div class="dropdown-section-title">
						<?= _t('gen.menu.account') ?>: <?= htmlspecialchars(Minz_User::name() ?? '', ENT_NOQUOTES, 'UTF-8') ?>
					</div>
					<ul>
						<li class="item"><a href="<?= _url('user', 'profile') ?>"><?= _t('gen.menu.user_profile') ?></a></li>
						<?php if (FreshRSS_Auth::accessNeedsAction()): ?>
						<li class="item">
							<button class="as-link signout" form="post-csrf" formaction="<?= _url('auth', 'logout') ?>"><?= _t('gen.auth.logout'); ?><?= _i('logout') ?></button>
						</li>
						<?php else: ?>
						<li class="item"><span class="signout">(<?= htmlspecialchars(Minz_User::name() ?? '', ENT_NOQUOTES, 'UTF-8') ?>)</span></li>
						<?php endif; ?>
					</ul>
				</li>
				<li class="item dropdown-section">
					<div class="dropdown-section-title">
						<?= _t('gen.menu.configuration') ?>
					</div>
					<ul>
						<li class="item"><a href="<?= _url('configure', 'display') ?>"><?= _t('gen.menu.display') ?></a></li>
						<li class="item"><a href="<?= _url('configure', 'reading') ?>"><?= _t('gen.menu.reading') ?></a></li>
						<li class="item"><a href="<?= _url('configure', 'archiving') ?>"><?= _t('gen.menu.archiving') ?></a></li>
						<li class="item"><a href="<?= _url('configure', 'integration') ?>"><?= _t('gen.menu.sharing') ?></a></li>
						<li class="item"><a href="<?= _url('configure', 'shortcut') ?>"><?= _t('gen.menu.shortcuts') ?></a></li>
						<li class="item"><a href="<?= _url('configure', 'queries') ?>"><?= _t('gen.menu.queries') ?></a></li>
						<li class="item"><a href="<?= _url('extension', 'index') ?>"><?= _t('gen.menu.extensions') ?></a></li>
						<li class="item"><a href="<?= _url('configure', 'privacy') ?>"><?= _t('gen.menu.privacy') ?></a></li>
						<?= Minz_ExtensionManager::callHookString('menu_configuration_entry') ?>
					</ul>
				</li>
				<?php if (FreshRSS_Auth::hasAccess('admin')) { ?>
				<li class="item dropdown-section">
					<div class="dropdown-section-title">
						<?= _t('gen.menu.admin') ?>
					</div>
					<ul>
						<li class="item"><a href="<?= _url('configure', 'system') ?>"><?= _t('gen.menu.system') ?></a></li>
						<li class="item"><a href="<?= _url('user', 'manage') ?>"><?= _t('gen.menu.user_management') ?></a></li>
						<li class="item"><a href="<?= _url('auth', 'index') ?>"><?= _t('gen.menu.authentication') ?></a></li>
						<li class="item"><a href="<?= _url('update', 'checkInstall') ?>"><?= _t('gen.menu.check_install') ?></a></li>
						<?php if (!FreshRSS_Context::systemConf()->disable_update) { ?>
						<li class="item"><a href="<?= _url('update', 'index') ?>"><?= _t('gen.menu.update') ?></a></li>
						<?php } ?>
						<?= Minz_ExtensionManager::callHookString('menu_admin_entry') ?>
					</ul>
				</li>
				<?php } ?>

				<li class="item dropdown-section">
					<ul>
						<li class="item"><a href="<?= _url('index', 'logs') ?>"><?= _t('gen.menu.logs') ?></a></li>
						<li class="item"><a href="<?= _url('index', 'about') ?>"><?= _t('gen.menu.about') ?></a></li>
						<?php if (file_exists(TOS_FILENAME)) { ?>
							<li class="item">
								<a href="<?= _url('index', 'tos') ?>"><?= _t('index.tos.title')?></a>
							</li>
						<?php } ?>
						<?= Minz_ExtensionManager::callHookString('menu_other_entry') ?>
					</ul>
				</li>
			</ul>
			<a class="dropdown-close" href="#close">❌</a>
		</div>
	</nav>
	<?php } elseif (FreshRSS_Auth::accessNeedsAction()) { ?>
	<div class="item configure">
		<a class="signin" href="<?= Minz_Url::display(['c' => 'auth', 'a' => 'login'], 'html', 'root') ?>"><?= _i('login') ?><?= _t('gen.auth.login') ?></a>
	</div>
	<?php } ?>
</header>
