<?php
	declare(strict_types=1);
	/** @var FreshRSS_View $this */
	FreshRSS::preLayout();
	$class = '';
	$dir = '';
	if (_t('gen.dir') === 'rtl') {
		$dir = ' dir="rtl"';
		$class = 'rtl ';
	}
	$class .= 'theme_' . FreshRSS_Context::userConf()->theme;
	if (FreshRSS_Context::userConf()->darkMode !== 'no') {
		$class .= ' darkMode_' . FreshRSS_Context::userConf()->darkMode;
	}
?>
<!DOCTYPE html>
<html lang="<?= FreshRSS_Context::userConf()->language ?>"<?= $dir ?> xml:lang="<?= FreshRSS_Context::userConf()->language ?>" class="<?= $class ?>">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
		<?= FreshRSS_View::metaThemeColor() ?>
		<?= FreshRSS_View::headStyle() ?>
		<script id="jsonVars" type="application/json">
<?php $this->renderHelper('javascript_vars'); ?>
		</script>
		<?= FreshRSS_View::headScript() ?>
		<link rel="manifest" href="<?= Minz_Url::display('/themes/manifest.json') ?>" />
		<link rel="shortcut icon" id="favicon" type="image/x-icon" sizes="16x16 64x64" href="<?= Minz_Url::display('/favicon.ico') ?>" />
		<link rel="icon msapplication-TileImage apple-touch-icon" type="image/png" sizes="256x256" href="<?= Minz_Url::display('/themes/icons/favicon-256.png') ?>" />
		<link rel="apple-touch-icon" href="<?= Minz_Url::display('/themes/icons/apple-touch-icon.png') ?>" />
		<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black" />
		<meta name="apple-mobile-web-app-title" content="<?= FreshRSS_Context::systemConf()->title ?>">
		<meta name="msapplication-TileColor" content="#FFF" />
		<meta name="theme-color" content="#FFF" />
<?php if (!FreshRSS_Context::systemConf()->allow_referrer) { ?>
		<meta name="referrer" content="never" />
<?php } ?>
		<?= FreshRSS_View::headTitle() ?>
<?php
	$url_base = Minz_Request::currentRequest();
	if ($this->rss_title != '') {
		$url_rss = $url_base;
		$url_rss['a'] = 'rss';
		$url_rss['params']['user'] = Minz_User::name() ?? '';
		$url_rss['params']['token'] = FreshRSS_Context::userConf()->token;
		unset($url_rss['params']['rid']);
		if (FreshRSS_Context::userConf()->since_hours_posts_per_rss) {
			$url_rss['params']['hours'] = FreshRSS_Context::userConf()->since_hours_posts_per_rss;
		}
?>
		<link rel="alternate" type="application/rss+xml" title="<?= $this->rss_title ?>" href="<?= Minz_Url::display($url_rss) ?>" />
<?php } if (FreshRSS_Context::isAll() || FreshRSS_Context::isAllAndCategories() || FreshRSS_Context::isAllAndArchived() || FreshRSS_Context::isCategory() || FreshRSS_Context::isFeed()) {
		$opml_rss = $url_base;
		$opml_rss['a'] = 'opml';
		$opml_rss['params']['user'] = Minz_User::name() ?? '';
		$opml_rss['params']['token'] = FreshRSS_Context::userConf()->token;
		unset($opml_rss['params']['rid']);
?>
		<link rel="outline" type="text/x-opml" title="OPML" href="<?= Minz_Url::display($opml_rss) ?>" />
<?php } if (FreshRSS_Context::systemConf()->allow_robots) { ?>
		<meta name="description" content="<?= htmlspecialchars(FreshRSS_Context::$name . ' | ' . FreshRSS_Context::$description, ENT_COMPAT, 'UTF-8') ?>" />
<?php } else { ?>
		<meta name="robots" content="noindex,nofollow" />
<?php } ?>
	</head>
	<body class="<?= Minz_Request::actionName() ?>">
<?php
	if (!Minz_Request::paramBoolean('ajax')) {
		flush();
		$this->partial('header');
	}
?>

<div id="global">
	<?php
		flush();
		if (isset($this->callbackBeforeFeeds)) {
			call_user_func($this->callbackBeforeFeeds, $this);
		}
		$this->render();
	?>
</div>

<?php
	$msg = '';
	$status = 'closed';
	$notificationName = '';
	$notif = Minz_Request::getNotification();
	if (!empty($notif)) {
		$msg = $notif['content'];
		$status = $notif['type'];
		$notificationName = $notif['notificationName'];
		invalidateHttpCache();
	}
?>
<div role="dialog" id="notification" class="notification <?= $status ?> <?= $notificationName ?>" aria-describedby="dialogMsg">
	<span class="msg" id="dialogMsg"><?= $msg ?></span>
	<button class="close" title="<?= _t('gen.action.close') ?>"><?= _i('close') ?></button>
</div>
	</body>
</html>
