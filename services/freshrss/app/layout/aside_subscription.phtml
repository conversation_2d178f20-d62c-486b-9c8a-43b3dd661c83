<?php
	declare(strict_types=1);
?>
<nav class="nav nav-list aside" id="aside_feed">
	<a class="toggle_aside" href="#close"><?= _i('close') ?></a>
	<ul>
		<li class="item nav-section">
			<ul>
				<li class="item">
					<a href="<?= _url('index', 'index') ?>"><?= _t('gen.action.back_to_rss_feeds') ?></a>
				</li>
			</ul>
		</li>
		<li class="item nav-section">
			<div class="nav-header"><?= _t('sub.menu.subscription_management') ?></div>
			<ul>
				<li class="item<?= Minz_Request::controllerName() === 'subscription' && Minz_Request::actionName() === 'add' ? ' active' : '' ?>">
					<a href="<?= _url('subscription', 'add') ?>"><?= _t('sub.menu.add') ?></a>
				</li>

				<li class="item<?= Minz_Request::controllerName() === 'subscription' && in_array(Minz_Request::actionName(), ['index', 'category', 'feed'], true) ? ' active' : '' ?>">
					<a href="<?= _url('subscription', 'index') ?>"><?= _t('sub.menu.subscription_management') ?></a>
				</li>

				<li class="item<?= Minz_Request::controllerName() === 'tag' ? ' active' : '' ?>">
					<a href="<?= _url('tag', 'index') ?>"><?= _t('sub.menu.label_management') ?></a>
				</li>

				<li class="item<?= Minz_Request::controllerName() === 'importExport' ? ' active' : '' ?>">
					<a href="<?= _url('importExport', 'index') ?>"><?= _t('sub.menu.import_export') ?></a>
				</li>

				<li class="item<?= Minz_Request::controllerName() === 'subscription' && Minz_Request::actionName() === 'bookmarklet' ? ' active' : '' ?>">
					<a href="<?= _url('subscription', 'bookmarklet') ?>"><?= _t('sub.menu.subscription_tools') ?></a>
				</li>
			</ul>
		</li>

		<li class="item nav-section">
			<div class="nav-header"><?= _t('admin.stats') ?></div>
			<ul>
				<li class="item<?= Minz_Request::controllerName() == 'stats' && Minz_Request::actionName() == 'index' ? ' active' : '' ?>">
					<a href="<?= _url('stats', 'index') ?>"><?= _t('sub.menu.stats.main') ?></a>
				</li>
				<li class="item<?= Minz_Request::actionName() == 'idle' ? ' active' : '' ?>">
					<a href="<?= _url('stats', 'idle') ?>"><?= _t('sub.menu.stats.idle') ?></a>
				</li>
				<li class="item<?= Minz_Request::actionName() == 'repartition' ? ' active' : '' ?>">
					<a href="<?= _url('stats', 'repartition') ?>"><?= _t('sub.menu.stats.repartition') ?></a>
				</li>
			</ul>
		</li>
	</ul>
</nav>
<?php if (Minz_Request::actionName() != 'repartition') { ?>
	<a class="close-aside" href="#close">❌</a>
	<nav class="nav_menu nav_mobile">
		<a class="btn toggle_aside" href="#aside_feed"><?= _i('category') ?></a>
	</nav>
<?php } ?>
