includes:
	- phpstan.dist.neon
	- vendor/phpstan/phpstan/conf/bleedingEdge.neon

parameters:
	level: max
	strictRules:
		strictArrayFilter: true	# TODO pass
	excludePaths:
		analyse:
			# TODO: Update files below and remove them from this list
			- app/Controllers/configureController.php
			- app/Controllers/feedController.php
			- app/Controllers/subscriptionController.php
			- app/Models/Entry.php
			- app/Models/UserQuery.php
			- cli/CliOption.php
			- cli/CliOptionsParser.php
			- cli/create-user.php
			- cli/reconfigure.php
			- cli/update-user.php
			- lib/Minz/Migrator.php
