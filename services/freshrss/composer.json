{"name": "freshrss.org/freshrss", "description": "A free, self-hostable aggregator", "type": "project", "homepage": "https://freshrss.org/", "license": "AGPL-3.0", "support": {"docs": "https://freshrss.github.io/FreshRSS/", "issues": "https://github.com/FreshRSS/FreshRSS/issues", "source": "https://github.com/FreshRSS/FreshRSS/"}, "keywords": ["news", "aggregator", "RSS", "Atom", "WebSub"], "require": {"php": ">=8.1", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gmp": "*", "ext-intl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-pdo_sqlite": "*", "ext-session": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-zend-opcache": "*", "ext-zip": "*", "ext-zlib": "*"}, "suggest": {"ext-iconv": "*", "ext-pdo_mysql": "*", "ext-pdo_pgsql": "*"}, "config": {"allow-plugins": {"phpstan/extension-installer": false}, "platform": {"php": "8.1"}}, "require-dev": {"php": ">=8.1", "ext-phar": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "phpstan/phpstan": "^2.1", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^10", "squizlabs/php_codesniffer": "^3.12"}, "scripts": {"php-lint": "find . -type d -name 'vendor' -prune -o -name '*.php' -print0 | xargs -0 -n1 -P4 php -l 1>/dev/null", "phtml-lint": "find . -type d -name 'vendor' -prune -o -name '*.phtml' -print0 | xargs -0 -n1 -P4 php -l 1>/dev/null", "phpcs": "phpcs . -s", "phpcbf": "phpcbf . -p -s", "phpstan": "phpstan analyse .", "phpstan-next": "phpstan analyse -c phpstan-next.neon .", "phpunit": "phpunit --bootstrap ./tests/bootstrap.php --display-notices --display-phpunit-deprecations ./tests", "translations": "cli/manipulate.translation.php -a format && cli/check.translation.php -g", "test": ["@php-lint", "@phtml-lint", "@phpunit", "@phpcs", "@phpstan", "@phpstan-next"], "fix": ["@translations", "@phpcbf"]}}