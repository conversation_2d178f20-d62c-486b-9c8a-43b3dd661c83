# French translations for PACKAGE package
# Copyright (C) 2019 Free Software Foundation, Inc.
# This file is distributed under the same license as the PACKAGE package.
#
# Automatically generated, 2019.
# <PERSON><PERSON> <<EMAIL>>, 2019.
# <PERSON><PERSON> <<EMAIL>>, 2019.
#
msgid ""
msgstr ""
"Project-Id-Version: FreshRSS\n"
"Report-Msgid-Bugs-To: https://github.com/FreshRSS/FreshRSS/issues\n"
"POT-Creation-Date: 2023-05-11 00:12+0200\n"
"PO-Revision-Date: 2019-12-07 10:50+0100\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: French <>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Gtranslator 3.34.0\n"

#. type: Title ##
#: en/./contributing.md:1
#, no-wrap
msgid "Report a bug"
msgstr "Signaler un bug"

#. type: Plain text
#: en/./contributing.md:4
msgid ""
"Have you found a bug? Don’t panic, here are some steps to report it with "
"ease:"
msgstr ""
"Avez-vous trouvé un bogue ? Ne paniquez pas, voici quelques étapes pour le "
"signaler facilement :"

#. type: Bullet: '1. '
#: en/./contributing.md:8
msgid ""
"Search for it on [the bug tracker](https://github.com/FreshRSS/FreshRSS/"
"issues) (don’t forget to use the search bar)."
msgstr ""
"Cherche sur [le bug tracker](https://github.com/FreshRSS/FreshRSS/issues) "
"(n’oubliez pas d’utiliser la barre de recherche)."

#. type: Bullet: '2. '
#: en/./contributing.md:8
msgid ""
"If you find a similar bug, don’t hesitate to post a comment to add more "
"importance to the related ticket."
msgstr ""
"Si vous constatez un bogue similaire, n’hésitez pas à poster un commentaire "
"pour ajouter de l’importance au ticket correspondant."

#. type: Bullet: '3. '
#: en/./contributing.md:8
msgid ""
"If you didn’t find it, [open a new ticket](https://github.com/FreshRSS/"
"FreshRSS/issues/new)."
msgstr ""
"Si vous ne l’avez pas trouvé, [ouvrez un nouveau ticket](https://github.com/"
"FreshRSS/FreshRSS/issues/new)."

#. type: Plain text
#: en/./contributing.md:10
msgid ""
"If you have to create a new ticket, please try to keep in mind the following "
"advice:"
msgstr ""
"Si vous devez créer un nouveau ticket, essayez de garder les conseils "
"suivants :"

#. type: Bullet: '* '
#: en/./contributing.md:13
msgid ""
"Give an explicit title to the ticket so it will be easier to find it later."
msgstr ""
"Donnez un titre explicite au ticket pour le retrouver plus facilement plus "
"tard."

#. type: Bullet: '* '
#: en/./contributing.md:13
msgid ""
"Be as exhaustive as possible in the description: what did you do? What is "
"the bug? What are the steps to reproduce the bug?"
msgstr ""
"Soyez aussi exhaustif que possible dans la description : qu’avez-vous fait ? "
"Quel est le bogue ? Quelles sont les étapes pour reproduire le bogue ?"

#. type: Plain text
#: en/./contributing.md:15
msgid "We also need some information:"
msgstr "Nous avons aussi besoin de quelques informations :"

#. type: Bullet: '* '
#: en/./contributing.md:20
msgid ""
"Your FreshRSS version (on the about page or in the `constants.php` file)"
msgstr ""
"Votre version de FreshRSS (sur la page A propos) ou le fichier `constants."
"php`)"

#. type: Bullet: '* '
#: en/./contributing.md:20
msgid "Your server configuration: the type of hosting and the PHP version"
msgstr "Votre configuration de serveur : type d’hébergement, version PHP"

#. type: Bullet: '* '
#: en/./contributing.md:20
msgid "Your storage system (SQLite, MySQL, MariaDB, PostgreSQL)"
msgstr ""
"Quelle base de données : SQLite, MySQL, MariaDB, PostgreSQL ? Quelle "
"version ?"

#. type: Bullet: '* '
#: en/./contributing.md:20
msgid ""
"If possible, the related logs (PHP logs and FreshRSS logs under `data/users/"
"your_user/log.txt`)"
msgstr ""
"Si possible, les logs associés (logs PHP et logs FreshRSS sous `data/users/"
"your_user/log.txt`)"

#. type: Plain text
#: en/./contributing.md:22
msgid ""
"For a more detailed guide on writing bug reports, please refer to [the in-"
"depth guide on reporting bugs](developers/06_Reporting_Bugs)."
msgstr ""

#. type: Title ##
#: en/./contributing.md:23
#, no-wrap
msgid "Fix a bug"
msgstr "Corriger un bogue"

#. type: Plain text
#: en/./contributing.md:26
msgid ""
"Would you like to fix a bug? For optimum coordination between collaborators, "
"you should follow these indications:"
msgstr ""
"Voulez-vous corriger un bogue ? Pour maintenir une grande coordination entre "
"les collaborateurs, vous devrez suivre ces indications :"

#. type: Bullet: '1. '
#: en/./contributing.md:31
msgid ""
"Be sure the bug is associated with a ticket and indicate that you’ll work on "
"it."
msgstr ""
"Assurez-vous que le bogue est associé à un ticket et indiquez que vous allez "
"travailler sur le bogue."

#. type: Bullet: '2. '
#: en/./contributing.md:31
msgid ""
"[Fork the project repository](https://help.github.com/articles/fork-a-repo/)."
msgstr ""
"[Fork du répertoire de projet](https://help.github.com/articles/fork-a-"
"repo/)."

#. type: Bullet: '3. '
#: en/./contributing.md:31
msgid ""
"[Create a new branch](https://help.github.com/articles/creating-and-deleting-"
"branches-within-your-repository/). The name of the branch should be clear, "
"and ideally prefixed by the related ticket id. For instance, `783-"
"contributing-file` to fix [ticket #783](https://github.com/FreshRSS/FreshRSS/"
"issues/783)."
msgstr ""
"[Créez une nouvelle branche](https://help.github.com/articles/creating-and-"
"deleting-branches-within-your-repository/). Le nom de la branche doit être "
"clair, et idéalement préfixé par l’identifiant du ticket correspondant. Par "
"exemple, `783-contributing-file` pour réparer [ticket #783](https://github."
"com/FreshRSS/FreshRSS/issues/783)."

#. type: Bullet: '4. '
#: en/./contributing.md:31
msgid ""
"Make your changes to your fork and [send a pull request](https://help.github."
"com/articles/using-pull-requests/)."
msgstr ""
"Ajoutez vos modifications à votre fork et [ouvrez une demande de pull "
"request](https://help.github.com/articles/using-pull-requests/)."

#. type: Plain text
#: en/./contributing.md:33
#, fuzzy
#| msgid ""
#| "If you have to write code, please follow [our coding style "
#| "recommendations](developers/01_First_steps.md)."
msgid ""
"If you have to write code, please follow [our coding style recommendations]"
"(developers/02_First_steps.md)."
msgstr ""
"Si vous devez écrire du code, veuillez suivre [nos recommandations de style "
"de codage](developers/01_First_steps.md)."

#. type: Plain text
#: en/./contributing.md:35
#, no-wrap
msgid "**Tip:** if you’re searching for easy-to-fix bugs, please have a look at the “[good first issue](https://github.com/FreshRSS/FreshRSS/issues?q=is%3Aopen+is%3Aissue+label%3A%22good+first+issue%22)” ticket label.\n"
msgstr "**Conseil : **si vous cherchez des bugs faciles à corriger, jetez un coup d’oeil à la vignette « [good first issue](https://github.com/FreshRSS/FreshRSS/issues?q=is%3Aopen+is%3Aissue+label%3A%22good+first+issue%22) ».\n"

#. type: Title ##
#: en/./contributing.md:36
#, no-wrap
msgid "Submit an idea"
msgstr "Soumettre une idée"

#. type: Plain text
#: en/./contributing.md:39
msgid ""
"You have great ideas, yes! Don’t be shy and open [a new ticket](https://"
"github.com/FreshRSS/FreshRSS/issues/new) on our bug tracker to ask if we can "
"implement it. The greatest ideas often come from the shyest suggestions!"
msgstr ""
"Vous avez de bonnes idées, oui ! Ne soyez pas timide et ouvrez [un nouveau "
"ticket](https://github.com/FreshRSS/FreshRSS/issues/new) sur notre tracker "
"bogue pour nous demander si nous pouvons le mettre en œuvre. Les plus "
"grandes idées viennent souvent des suggestions les plus timides !"

#. type: Plain text
#: en/./contributing.md:41
msgid "If your idea is nice, we’ll have a look at it."
msgstr "Si votre idée est bonne, nous y jetterons un coup d’oeil."

#. type: Title ##
#: en/./contributing.md:42
#, no-wrap
msgid "Contribute to internationalization (i18n)"
msgstr "Contribuer à l’internationalisation (i18n)"

#. type: Plain text
#: en/./contributing.md:45
msgid ""
"Learn how to contribute to translations in [the dedicated documentation](./"
"internationalization.md)."
msgstr ""

#. type: Title ##
#: en/./contributing.md:46
#, no-wrap
msgid "Contribute to documentation"
msgstr "Contribuer à la documentation"

#. type: Plain text
#: en/./contributing.md:49
msgid ""
"The documentation needs a lot of improvements in order to be more useful to "
"new contributors and we are working on it.  If you want to give some help, "
"meet us in the main repositories [docs directory](https://github.com/"
"FreshRSS/FreshRSS/tree/edge/docs)!"
msgstr ""
"Il ne vous aura pas échappé que la documentation est encore un peu vide… il "
"y a énormément de choses à faire ! Si vous souhaitez aider à écrire quelques "
"pages, rendez-vous dans les principaux dépôts[fichier docs](https://github."
"com/FreshRSS/FreshRSS/tree/edge/docs) !"

#. type: Title #
#: en/./developers/01_Index.md:1
#, fuzzy, no-wrap
#| msgid "FreshRSS - Fever API implementation"
msgid "FreshRSS Development"
msgstr "FreshRSS - API compatible Fever"

#. type: Title ##
#: en/./developers/01_Index.md:3
#, no-wrap
msgid "First Steps"
msgstr ""

#. type: Plain text
#: en/./developers/01_Index.md:6
msgid ""
"Start by creating your development environment. A guide to setting up "
"FreshRSS’s development environment can be found on [the appropriate page]"
"(02_First_steps.md)."
msgstr ""

#. type: Title ##
#: en/./developers/01_Index.md:7
#, no-wrap
msgid "After That"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:16
msgid "[GitHub Branching and Pushing](02_GitHub.md)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:16
msgid "[Running tests](03_Running_tests.md)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:16
msgid "[Creating a pull request](04_Pull_requests.md)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:16
msgid "[Releasing a new version](05_Release_new_version.md)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:16
msgid "[Reporting bugs](06_Reporting_Bugs.md)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:16
msgid "[Fever API](06_Fever_API.md)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:16
msgid "[GoogleReader API](06_GoogleReader_API.md)"
msgstr ""

#. type: Title ##
#: en/./developers/01_Index.md:17
#, no-wrap
msgid "Backend Development"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:22
msgid "[Making extensions for FreshRSS](03_Backend/05_Extensions.md)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:22
msgid "[Database Schema](03_Backend/01_Database_schema.md)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:22
msgid "[External libraries](03_Backend/03_External_libraries.md)"
msgstr ""

#. type: Title ##
#: en/./developers/01_Index.md:23
#, no-wrap
msgid "Frontend Development"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:27
msgid "[View files](04_Frontend/01_View_files.md)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:27
msgid "[Design (Themes/Theming)](04_Frontend/02_Design.md)"
msgstr ""

#. type: Title ##
#: en/./developers/01_Index.md:28
#, no-wrap
msgid "Namespaces"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/01_Index.md:31
msgid "[OPML FreshRSS namespace](OPML.md)"
msgstr ""

#. type: Title ##
#: en/./developers/01_Index.md:32
#, no-wrap
msgid "Minz"
msgstr ""

#. type: Plain text
#: en/./developers/01_Index.md:34
msgid ""
"Minz is the homemade PHP framework used by FreshRSS. More information can be "
"found [here](Minz/index.md)."
msgstr ""

#. type: Title #
#: en/./developers/02_First_steps.md:1
#, no-wrap
msgid "Environment configuration (Docker)"
msgstr "Configurer son environnement (Docker)"

#. type: Plain text
#: en/./developers/02_First_steps.md:4
msgid ""
"FreshRSS is built with PHP and uses a homemade framework, Minz. The "
"dependencies are directly included in the source code, so you don’t need "
"Composer."
msgstr ""
"FreshRSS est construit en PHP et utilise le framework Minz. Les "
"dépendancessont directement incluses dans le code source, donc vous n’avez "
"pas besoin d’utiliser Composer."

#. type: Plain text
#: en/./developers/02_First_steps.md:6
msgid ""
"There are various ways to configure your development environment. The "
"easiest and most supported method is based on Docker, which is the solution "
"documented below. If you already have a working PHP environment, you "
"probably don’t need it."
msgstr ""
"Il existe plusieurs façons de configurer votre environnement "
"dedéveloppement. La méthode la plus simple et la plus supportée est basée "
"surDocker. C’est la solution qui est documentée ci-dessous. Si vous avez "
"déjà unenvironnement PHP fonctionnel, vous n’en avez probablement pas besoin."

#. type: Plain text
#: en/./developers/02_First_steps.md:8
msgid ""
"We assume here that you use a GNU/Linux distribution, capable of running "
"Docker. Otherwise, you’ll have to adapt the commands accordingly."
msgstr ""
"Nous supposons ici que vous utilisez une distribution GNU/Linux, capable "
"d’exécuter Docker. Sinon, vous devrez adapter les commandes en conséquence."

#. type: Plain text
#: en/./developers/02_First_steps.md:10
msgid ""
"The commands that follow have to be executed in a console. They start by `$` "
"when commands need to be executed as normal user, and by `#` when they need "
"to be executed as root user. You don’t have to type these characters. A path "
"may be indicated before these characters to help you identify where they "
"need to be executed. For instance, `app$ echo 'Hello World'` indicates that "
"you have to execute `echo` command in the `app/` directory."
msgstr ""
"Les commandes qui suivent doivent être exécutées dans une console. Ils "
"commencent par `$` quand les commandes doivent être exécutées en tant "
"qu’utilisateur normal, et par `#` quand elles doivent être exécutées en tant "
"qu’utilisateur root. Vous n’avez pas besoin de taper ces caractères. Un "
"chemin d’accès peut être indiqué devant ces caractères pour vous aider à "
"identifier où ils doivent être exécutés. Par exemple, `app$ echo 'Hello "
"World'` indique que vous devez exécuter la commande `echo` dans le "
"répertoire `app/`."

#. type: Plain text
#: en/./developers/02_First_steps.md:12
msgid ""
"First, you need to install [Docker](https://docs.docker.com/install/linux/"
"docker-ce/ubuntu/)."
msgstr ""
"Tout d’abord, vous devez installer [Docker](https://docs.docker.com/install/"
"linux/docker-ce/ubuntu/)."

#. type: Plain text
#: en/./developers/02_First_steps.md:14
msgid "Once you’re done, clone the repository with:"
msgstr ""
"Une fois que c’est fait, clonez le dépôt de code de la manière suivante :"

#. type: Fenced code block (sh)
#: en/./developers/02_First_steps.md:15
#, fuzzy, no-wrap
#| msgid ""
#| "$ git clone https://github.com/FreshRSS/FreshRSS.git\n"
#| "$ cd FreshRSS\n"
msgid ""
"git clone https://github.com/FreshRSS/FreshRSS.git\n"
"cd FreshRSS\n"
msgstr ""
"$ git clone https://github.com/FreshRSS/FreshRSS.git\n"
"$ cd FreshRSS\n"

#. type: Plain text
#: en/./developers/02_First_steps.md:21
msgid ""
"Note that, if you want to contribute, you have to fork the repository first "
"and clone your fork instead of the \"root\" one. Adapt the commands in "
"consequence."
msgstr ""
"Notez que, pour contribuer, vous devrez d’abord « forker » ce dépôt de code "
"(ou dépôt de code référent) et cloner votre « fork » à la place de ce dépôt. "
"Adaptez les commandes en conséquence."

#. type: Plain text
#: en/./developers/02_First_steps.md:23
msgid "Then, the only command you need to know is the following:"
msgstr "Ensuite, la seule commande que vous devez connaître est la suivante :"

#. type: Fenced code block (sh)
#: en/./developers/02_First_steps.md:24
#, fuzzy, no-wrap
#| msgid "$ make start\n"
msgid "make start\n"
msgstr "$ make start\n"

#. type: Plain text
#: en/./developers/02_First_steps.md:29
msgid ""
"This might take some time while Docker downloads the image. If your user "
"isn’t in the `docker` group, you’ll need to prepend the command with `sudo`."
msgstr ""
"Cela peut prendre un certain temps pour que Docker télécharge l’image "
"utilisée. Dans le cas où la commande échoue pour un problème de droit, il "
"faudra soit ajouter votre utilisateur au groupe `docker`, soit relancer la "
"commande en la préfixant par `sudo`."

#. type: Plain text
#: en/./developers/02_First_steps.md:31
#, no-wrap
msgid "**You can now access FreshRSS at [http://localhost:8080](http://localhost:8080).** Just follow the install process and select the SQLite database.\n"
msgstr "**Vous pouvez maintenant accéder à FreshRSS à [http://localhost:8080](http://localhost:8080).** Suivez simplement le processus d’installation et sélectionnez la base de données SQLite.\n"

#. type: Plain text
#: en/./developers/02_First_steps.md:33
#, no-wrap
msgid "You can stop the containers by typing <kbd>Control</kbd> + <kbd>c</kbd> or with the following command, in another terminal:\n"
msgstr "Vous pouvez arrêter les conteneurs en tapant <kbd>Control</kbd> + <kbd>c</kbd> ou avec la commande suivante, dans un autre terminal:\n"

#. type: Fenced code block (sh)
#: en/./developers/02_First_steps.md:34
#, fuzzy, no-wrap
#| msgid "$ make stop\n"
msgid "make stop\n"
msgstr "$ make stop\n"

#. type: Plain text
#: en/./developers/02_First_steps.md:39
msgid ""
"If you’re interested in the configuration, the `make` commands are defined "
"in the [`Makefile`](/Makefile)."
msgstr ""
"Si la configuration vous intéresse, les commandes `make' sont définies dans "
"le fichier [`Makefile`](/Makefile)."

#. type: Plain text
#: en/./developers/02_First_steps.md:41
msgid ""
"If you need to use a different tag image (default is `alpine`), you can set "
"the `TAG` environment variable:"
msgstr ""
"Si vous avez besoin d’utiliser une image Docker identifiée par un tag "
"différent (par défaut `alpine`), vous pouvez surcharger de la manière "
"suivante la variable d’environnement `TAG` au moment de l’exécution de la "
"commande :"

#. type: Fenced code block (sh)
#: en/./developers/02_First_steps.md:42
#, fuzzy, no-wrap
#| msgid "$ TAG=arm make start\n"
msgid "TAG=arm make start\n"
msgstr "$ TAG=arm make start\n"

#. type: Plain text
#: en/./developers/02_First_steps.md:47
msgid ""
"You can find the full list of available tags [on the Docker hub](https://hub."
"docker.com/r/freshrss/freshrss/tags)."
msgstr ""
"Vous pouvez trouver la liste complète des tags disponibles [sur le hub "
"Docker](https://hub.docker.com/r/freshrss/freshrss/tags)."

#. type: Plain text
#: en/./developers/02_First_steps.md:49
msgid ""
"If you want to build the Docker image yourself, you can use the following "
"command:"
msgstr ""
"Si vous voulez construire l’image Docker, vous pouvez lancer la commande "
"suivante :"

#. type: Fenced code block (sh)
#: en/./developers/02_First_steps.md:50
#, fuzzy, no-wrap
#| msgid ""
#| "$ make build\n"
#| "$ # or\n"
#| "$ TAG=arm make build\n"
msgid ""
"make build\n"
"# or\n"
"TAG=arm make build\n"
msgstr ""
"$ make build\n"
"$ # ou\n"
"$ TAG=arm make build\n"

#. type: Plain text
#: en/./developers/02_First_steps.md:57
msgid ""
"The `TAG` variable can be anything (e.g. `local`). You can target a specific "
"architecture by adding `-alpine` or `-arm` at the end of the tag (e.g. "
"`local-arm`)."
msgstr ""
"La valeur de la variable `TAG` peut contenir n’importe quelle valeur (par "
"exemple `local`). Vous pouvez cibler une architecture spécifique en ajoutant "
"`-alpine` ou `-arm` à la fin du tag (par exemple `local-arm`)."

#. type: Title ##
#: en/./developers/02_First_steps.md:58
#, no-wrap
msgid "Project architecture"
msgstr "Architecture du projet"

#. type: Bullet: '- '
#: en/./developers/02_First_steps.md:61
msgid "the PHP framework: [Minz](Minz/index.md)"
msgstr ""

#. type: Title #
#: en/./developers/02_First_steps.md:62 en/./users/05_Configuration.md:178
#, no-wrap
msgid "Extensions"
msgstr "Extensions"

#. type: Plain text
#: en/./developers/02_First_steps.md:65
msgid ""
"If you want to create your own FreshRSS extension, take a look at the "
"[extension documentation](03_Backend/05_Extensions.md)."
msgstr ""
"Si vous souhaitez créer votre propre extension FreshRSS, consultez la "
"[documentation de l’extension](03_Backend/05_Extensions.md)."

#. type: Title ##
#: en/./developers/02_First_steps.md:66
#, no-wrap
msgid "Coding style"
msgstr "Style de codage"

#. type: Plain text
#: en/./developers/02_First_steps.md:69
msgid ""
"If you want to contribute to the source code, it’s important to follow the "
"project’s coding style. The actual code doesn’t always follow it throughout "
"the project, but we should fix it every time an opportunity presents itself."
msgstr ""
"Si vous désirez contribuer au code, il est important de respecter le style "
"de codage suivant. Le code actuel ne le respecte pas entièrement mais il est "
"de notre devoir à tous de le changer dès que l’occasion se présente."

#. type: Plain text
#: en/./developers/02_First_steps.md:71
msgid ""
"Contributions which don’t follow the coding style will be rejected as long "
"as the coding style is not fixed."
msgstr ""
"Aucune nouvelle contribution ne respectant pas ces règles ne sera acceptée "
"tant que les corrections nécessaires ne sont pas appliquées."

#. type: Title ##
#: en/./developers/02_First_steps.md:72
#, no-wrap
msgid "GitHub Actions"
msgstr ""

#. type: Plain text
#: en/./developers/02_First_steps.md:76
msgid ""
"The code will be checked for every pull request commit on GitHub via [GitHub "
"Actions](https://github.com/FreshRSS/FreshRSS/actions).  See the "
"configuration file [`tests.yml`](../../../.github/workflows/tests.yml)."
msgstr ""

#. type: Title ##
#: en/./developers/02_First_steps.md:77
#, no-wrap
msgid "Running fixes & tests"
msgstr ""

#. type: Plain text
#: en/./developers/02_First_steps.md:80
msgid ""
"Tests can be run locally, e.g. by running `make test-all`, and several "
"problems can be automatically fixed by running `make fix-all`."
msgstr ""

#. type: Fenced code block (sh)
#: en/./developers/02_First_steps.md:81
#, no-wrap
msgid ""
"make fix-all\n"
"make test-all\n"
msgstr ""

#. type: Plain text
#: en/./developers/02_First_steps.md:87
msgid ""
"This requires `make` and `npm` in addition to the FreshRSS requirements. See "
"below for the precise requirements for a few platforms."
msgstr ""

#. type: Title ###
#: en/./developers/02_First_steps.md:88
#, no-wrap
msgid "Debian / Ubuntu"
msgstr ""

#. type: Plain text
#: en/./developers/02_First_steps.md:91
#, no-wrap
msgid "> ℹ️ Also applies to [Microsoft Windows](https://docs.microsoft.com/windows/wsl/install-win10) thanks to [WSL](https://ubuntu.com/wsl).\n"
msgstr ""

#. type: Plain text
#: en/./developers/02_First_steps.md:93
msgid ""
"Here are the dependencies that need to be manually installed prior to "
"running the fixes & tests."
msgstr ""

#. type: Fenced code block (sh)
#: en/./developers/02_First_steps.md:94
#, no-wrap
msgid "sudo apt update && sudo apt install --no-install-recommends -y make npm php-cli php-curl php-mbstring php-xml unzip wget\n"
msgstr ""

#. type: Title ###
#: en/./developers/02_First_steps.md:98
#, no-wrap
msgid "Fedora / Red Hat"
msgstr ""

#. type: Fenced code block (sh)
#: en/./developers/02_First_steps.md:100
#, no-wrap
msgid "yum install -y git make npm php-cli php-curl php-mbstring php-xml php-pdo unzip wget\n"
msgstr ""

#. type: Title ###
#: en/./developers/02_First_steps.md:104
#, no-wrap
msgid "Alpine Linux"
msgstr ""

#. type: Fenced code block (sh)
#: en/./developers/02_First_steps.md:106
#, no-wrap
msgid "apk add git make npm php-cli php-curl php-ctype php-dom php-mbstring php-openssl php-phar php-simplexml php-xml php-pdo php-tokenizer php-xmlreader php-xmlwriter unzip wget\n"
msgstr ""

#. type: Title ###
#: en/./developers/02_First_steps.md:110
#, no-wrap
msgid "Partial fixes & tests"
msgstr ""

#. type: Bullet: '- '
#: en/./developers/02_First_steps.md:114
msgid ""
"composer-based: `npm run fix && npm test` or see the [`scripts` section of "
"`composer.json`](../../../composer.json) for individual tests or fixes such "
"as `composer phpstan`"
msgstr ""

#. type: Bullet: '- '
#: en/./developers/02_First_steps.md:114
msgid ""
"npm-based: `npm run fix && npm test` or see the [`scripts` section of "
"`package.json`](../../../package.json) for individual tests or fixes such as "
"`npm run rtlcss`"
msgstr ""

#. type: Title ###
#: en/./developers/02_First_steps.md:115
#, no-wrap
msgid "Tests summary"
msgstr ""

#. type: Plain text
#: en/./developers/02_First_steps.md:118
msgid "A short (not complete) summary:"
msgstr ""

#. type: Title ####
#: en/./developers/02_First_steps.md:119
#, no-wrap
msgid "PHP"
msgstr ""

#. type: Bullet: '- '
#: en/./developers/02_First_steps.md:127
msgid "Syntax of `php` and `phtml` files is checked."
msgstr ""

#. type: Bullet: '- '
#: en/./developers/02_First_steps.md:127
msgid ""
"translation files (`i18n`) are checked ([more information about i18n files]"
"(internationalization.html))."
msgstr ""

#. type: Bullet: '- '
#: en/./developers/02_First_steps.md:127
msgid "unit test (`tests`) are run by [PHPunit](https://phpunit.de/)."
msgstr ""

#. type: Bullet: '- '
#: en/./developers/02_First_steps.md:127 en/./developers/02_First_steps.md:134
#: en/./developers/02_First_steps.md:139 en/./developers/02_First_steps.md:144
msgid "Linter:"
msgstr ""

#. type: Bullet: '  - '
#: en/./developers/02_First_steps.md:127 en/./developers/02_First_steps.md:134
msgid "[PHP_Codesniffer (phpcs)](https://github.com/squizlabs/PHP_CodeSniffer)"
msgstr ""

#. type: Bullet: '  - '
#: en/./developers/02_First_steps.md:127
msgid "[PHPstan](https://github.com/phpstan/phpstan)"
msgstr ""

#. type: Title ###
#: en/./developers/02_First_steps.md:128
#, no-wrap
msgid "CSS"
msgstr ""

#. type: Bullet: '  - '
#: en/./developers/02_First_steps.md:134
msgid "via npm `.styleintrc.json`"
msgstr ""

#. type: Bullet: '  - '
#: en/./developers/02_First_steps.md:134
msgid "check that RTL (right-to-left) CSS files match to standard CSS files"
msgstr ""

#. type: Title ###
#: en/./developers/02_First_steps.md:135
#, fuzzy, no-wrap
#| msgid "javascript"
msgid "JavaScript"
msgstr "javascript"

#. type: Bullet: '  - '
#: en/./developers/02_First_steps.md:139
msgid ""
"via npm `.styleintrc.json` ([ECMAScript 2017](https://en.wikipedia.org/wiki/"
"ECMAScript#8th_Edition_%E2%80%93_ECMAScript_2017))"
msgstr ""

#. type: Title ###
#: en/./developers/02_First_steps.md:140
#, no-wrap
msgid "Markdown"
msgstr ""

#. type: Bullet: '  - '
#: en/./developers/02_First_steps.md:144
msgid "via npm `.markdownlint.json`"
msgstr ""

#. type: Title ##
#: en/./developers/02_First_steps.md:145
#, no-wrap
msgid "Spaces, tabs and other whitespace characters"
msgstr "Espaces, tabulations et autres caractères blancs"

#. type: Title ###
#: en/./developers/02_First_steps.md:147
#, no-wrap
msgid "Indentation"
msgstr "Indentation"

#. type: Plain text
#: en/./developers/02_First_steps.md:150
msgid "Code indentation must use tabs."
msgstr ""
"L’indentation du code doit être faite impérativement avec des tabulations."

#. type: Title ###
#: en/./developers/02_First_steps.md:151
#, no-wrap
msgid "Alignment"
msgstr "Alignement"

#. type: Plain text
#: en/./developers/02_First_steps.md:154
msgid ""
"Once the code has been correctly indented, it might be useful to align it "
"for ease of reading. In that case, please use spaces."
msgstr ""
"Une fois l’indentation faite, il peut être nécessaire de faire un alignement "
"pour simplifier la lecture. Dans ce cas, il faut utiliser les espaces."

#. type: Fenced code block (php)
#: en/./developers/02_First_steps.md:155
#, no-wrap
msgid ""
"$result = a_function_with_a_really_long_name($param1, $param2,\n"
"                                             $param3, $param4);\n"
msgstr ""
"$resultat = une_fonction_avec_un_nom_long($param1, $param2,\n"
"                                          $param3, $param4);\n"

#. type: Title ###
#: en/./developers/02_First_steps.md:160
#, no-wrap
msgid "End of line"
msgstr "Fin de ligne"

#. type: Plain text
#: en/./developers/02_First_steps.md:163
msgid ""
"The newline character must be a line feed (LF), which is the default line "
"ending on *NIX systems. This character must not follow other white space."
msgstr ""
"Le caractère de fin de ligne doit être un saut de ligne (LF) qui est le "
"caractère de fin de ligne des systèmes *NIX. Ce caractère ne doit pas être "
"précédé par des caractères blanc."

#. type: Plain text
#: en/./developers/02_First_steps.md:165
msgid ""
"You can verify if there is any unintended white space at the end of line "
"with the following Git command:"
msgstr ""
"Il est possible de vérifier la présence de caractères blancs en fin de ligne "
"grâce à Git avec la commande suivante :"

#. type: Fenced code block (sh)
#: en/./developers/02_First_steps.md:166
#, no-wrap
msgid ""
"# command to check files before adding them in the Git index\n"
"git diff --check\n"
"# command to check files after adding them in the Git index\n"
"git diff --check --cached\n"
msgstr ""
"# commande à lancer avant l’ajout des fichiers dans l’index\n"
"git diff --check\n"
"# commande à lancer après l’ajout des fichiers dans l’index mais avant le commit\n"
"git diff --check --cached\n"

#. type: Title ###
#: en/./developers/02_First_steps.md:173
#, no-wrap
msgid "End of file"
msgstr "Fin de fichier"

#. type: Plain text
#: en/./developers/02_First_steps.md:176
msgid "Every file must end by an empty line."
msgstr "Chaque fichier doit se terminer par une ligne vide."

#. type: Title ###
#: en/./developers/02_First_steps.md:177
#, no-wrap
msgid "Commas, dots and semi-columns"
msgstr "Le cas de la virgule, du point et du point-virgule"

#. type: Plain text
#: en/./developers/02_First_steps.md:180
msgid ""
"There should no space before those characters, but there should be one after."
msgstr "Il n’y a pas d’espace avant ces caractères, il y en a un après."

#. type: Title ###
#: en/./developers/02_First_steps.md:181
#, no-wrap
msgid "Operators"
msgstr "Le cas des opérateurs"

#. type: Plain text
#: en/./developers/02_First_steps.md:184
msgid "There should be a space before and after every operator."
msgstr "Chaque opérateur est entouré d’espaces."

#. type: Fenced code block (php)
#: en/./developers/02_First_steps.md:185
#, no-wrap
msgid ""
"if ($a == 10) {\n"
"\t// do something\n"
"}\n"
"\n"
"echo $a ? 1 : 0;\n"
msgstr ""
"if ($a == 10) {\n"
"\t// faire quelque chose\n"
"}\n"
"\n"
"echo $a ? 1 : 0;\n"

#. type: Title ###
#: en/./developers/02_First_steps.md:193
#, no-wrap
msgid "Parentheses"
msgstr "Le cas des parenthèses"

#. type: Plain text
#: en/./developers/02_First_steps.md:196
msgid ""
"There should be no spaces in between brackets. There should be no spaces "
"before the opening bracket, except if it’s after a keyword. There shouldn’t "
"be any spaces after the closing bracket, except if it’s followed by a curly "
"bracket."
msgstr ""
"Il n’y a pas d’espaces entre des parenthèses. Il n’y a pas d’espaces avant "
"une parenthèse ouvrante sauf si elle est précédée d’un mot-clé. Il n’y a pas "
"d’espaces après une parenthèse fermante sauf si elle est suivie d’une "
"accolade ouvrante."

#. type: Fenced code block (php)
#: en/./developers/02_First_steps.md:197
#, no-wrap
msgid ""
"if ($a == 10) {\n"
"\t// do something\n"
"}\n"
"\n"
"if ((int)$a == 10) {\n"
"\t// do something\n"
"}\n"
msgstr ""
"if ($a == 10) {\n"
"\t// faire quelque chose\n"
"}\n"
"\n"
"if ((int)$a == 10) {\n"
"\t// faire quelque chose\n"
"}\n"

#. type: Title ###
#: en/./developers/02_First_steps.md:207
#, no-wrap
msgid "With chained functions"
msgstr "Le cas des fonctions chainées"

#. type: Plain text
#: en/./developers/02_First_steps.md:210
#, fuzzy
#| msgid ""
#| "It happens most of the time in Javascript files. When there are chained "
#| "functions with closures and callback functions, it’s hard to understand "
#| "the code if not properly formatted. In those cases, we add a new indent "
#| "level for the complete instruction and reset the indent for a new "
#| "instruction on the same level."
msgid ""
"It happens most of the time in JavaScript files. When there are chained "
"functions with closures and call-back functions, it’s hard to understand the "
"code if not properly formatted. In those cases, we add a new indent level "
"for the complete instruction and reset the indent for a new instruction on "
"the same level."
msgstr ""
"Ce cas se présente le plus souvent en Javascript. Quand on a des fonctions "
"chainées, des fonctions anonymes ainsi que des fonctions de rappels, il est "
"très facile de se perdre. Dans ce cas là, on ajoute une indentation "
"supplémentaire pour toute l’instruction et on revient au même niveau pour "
"une instruction de même niveau."

#. type: Fenced code block (javascript)
#: en/./developers/02_First_steps.md:211
#, no-wrap
msgid ""
"// First instruction\n"
"shortcut.add(shortcuts.mark_read, function () {\n"
"\t\t//...\n"
"\t}, {\n"
"\t\t'disable_in_input': true\n"
"\t});\n"
"// Second instruction\n"
"shortcut.add(\"shift+\" + shortcuts.mark_read, function () {\n"
"\t\t//...\n"
"\t}, {\n"
"\t\t'disable_in_input': true\n"
"\t});\n"
msgstr ""
"// Première instruction\n"
"shortcut.add(shortcuts.mark_read, function () {\n"
"\t\t//...\n"
"\t}, {\n"
"\t\t'disable_in_input': true\n"
"\t});\n"
"// Deuxième instruction\n"
"shortcut.add(\"shift+\" + shortcuts.mark_read, function () {\n"
"\t\t//...\n"
"\t}, {\n"
"\t\t'disable_in_input': true\n"
"\t});\n"

#. type: Title ##
#: en/./developers/02_First_steps.md:226
#, no-wrap
msgid "Line length"
msgstr "Longueur des lignes"

#. type: Plain text
#: en/./developers/02_First_steps.md:229
msgid ""
"Lines should strive to be shorter than 80 characters. However, this limit "
"may be extended to 100 characters when strictly necessary."
msgstr ""
"Les lignes ne doivent pas dépasser 80 caractères. Il est cependant autorisé "
"exceptionnellement de dépasser cette limite s’il n’est pas possible de la "
"respecter mais en aucun cas, les lignes ne doivent dépasser les 100 "
"caractères."

#. type: Plain text
#: en/./developers/02_First_steps.md:231
msgid "With functions, parameters can be declared on multiple lines."
msgstr ""
"Dans le cas des fonctions, les paramètres peuvent être déclarés sur "
"plusieurs lignes."

#. type: Fenced code block (php)
#: en/./developers/02_First_steps.md:232
#, no-wrap
msgid ""
"function my_function($param_1, $param_2,\n"
"                     $param_3, $param_4) {\n"
"\t// do something\n"
"}\n"
msgstr ""
"function ma_fonction($param_1, $param_2,\n"
"                     $param_3, $param_4) {\n"
"\t// faire quelque chose\n"
"}\n"

#. type: Title ##
#: en/./developers/02_First_steps.md:239
#, no-wrap
msgid "Naming"
msgstr "Nommage"

#. type: Plain text
#: en/./developers/02_First_steps.md:242
msgid ""
"All code elements (functions, classes, methods and variables) must describe "
"their usage succinctly."
msgstr ""
"L’ensemble des éléments du code (fonctions, classes, méthodes et variables) "
"doivent être nommés de manière à décrire leur usage de façon concise."

#. type: Title ###
#: en/./developers/02_First_steps.md:243
#, no-wrap
msgid "Functions and variables"
msgstr "Fonctions et variables"

#. type: Plain text
#: en/./developers/02_First_steps.md:246
msgid ""
"Functions and variables must follow the \"snake case\" naming convention."
msgstr ""
"Les fonctions et les variables doivent suivre la convention \"snake case\"."

#. type: Fenced code block (php)
#: en/./developers/02_First_steps.md:247
#, no-wrap
msgid ""
"// a function\n"
"function function_name() {\n"
"\t// do something\n"
"}\n"
"// a variable\n"
"$variable_name;\n"
msgstr ""
"// une fontion\n"
"function nom_de_la_fontion() {\n"
"\t// faire quelque chose\n"
"}\n"
"// une variable\n"
"$nom_de_la_variable;\n"

#. type: Title ###
#: en/./developers/02_First_steps.md:256
#, no-wrap
msgid "Methods"
msgstr "Méthodes"

#. type: Plain text
#: en/./developers/02_First_steps.md:259
msgid "Methods must follow the \"lower camel case\" naming convention."
msgstr "Les méthodes doivent suivre la convention \"lower camel case\"."

#. type: Fenced code block (php)
#: en/./developers/02_First_steps.md:260
#, no-wrap
msgid ""
"private function methodName() {\n"
"\t// do something\n"
"}\n"
msgstr ""
"private function nomDeLaMethode() {\n"
"\t// faire quelque chose\n"
"}\n"

#. type: Title ###
#: en/./developers/02_First_steps.md:266
#, no-wrap
msgid "Classes"
msgstr "Classes"

#. type: Plain text
#: en/./developers/02_First_steps.md:269
msgid "Classes must follow the \"upper camel case\" naming convention."
msgstr "Les classes doivent suivre la convention \"upper camel case\"."

#. type: Fenced code block (php)
#: en/./developers/02_First_steps.md:270
#, no-wrap
msgid "abstract class ClassName {}\n"
msgstr "abstract class NomDeLaClasse {}\n"

#. type: Title ##
#: en/./developers/02_First_steps.md:274
#, no-wrap
msgid "Encoding"
msgstr "Encodage"

#. type: Plain text
#: en/./developers/02_First_steps.md:277
msgid "Files must be encoded with the UTF-8 character set."
msgstr "Les fichiers doivent être encodés en UTF-8."

#. type: Title ##
#: en/./developers/02_First_steps.md:278
#, no-wrap
msgid "PHP compatibility"
msgstr "Compatibilité PHP"

#. type: Plain text
#: en/./developers/02_First_steps.md:281
msgid ""
"Please ensure that your code works with the oldest PHP version officially "
"supported by FreshRSS."
msgstr ""
"Assurez-vous que votre code fonctionne avec une version de PHP aussi "
"ancienne que celle que FreshRSS supporte officiellement."

#. type: Title ###
#: en/./developers/02_First_steps.md:282 en/./developers/OPML.md:47
#, no-wrap
msgid "Miscellaneous"
msgstr "Divers"

#. type: Title ###
#: en/./developers/02_First_steps.md:284
#, no-wrap
msgid "Operators on multiple lines"
msgstr ""

#. type: Plain text
#: en/./developers/02_First_steps.md:287
msgid ""
"Operators must be at the end of the line if a condition is split over more "
"than one line."
msgstr ""
"Les opérateurs doivent être en fin de ligne dans le cas de conditions sur "
"plusieurs lignes."

#. type: Fenced code block (php)
#: en/./developers/02_First_steps.md:288
#, no-wrap
msgid ""
"if ($a == 10 ||\n"
"    $a == 20) {\n"
"\t// do something\n"
"}\n"
msgstr ""
"if ($a == 10 ||\n"
"    $a == 20) {\n"
"\t// faire quelque chose\n"
"}\n"

#. type: Title ###
#: en/./developers/02_First_steps.md:295
#, fuzzy, no-wrap
#| msgid "End of file"
msgid "End of PHP file"
msgstr "Fin de fichier"

#. type: Plain text
#: en/./developers/02_First_steps.md:298
msgid ""
"If the file contains only PHP code, the PHP closing tag must be omitted."
msgstr ""
"Si le fichier ne contient que du PHP, il ne doit pas comporter de balise "
"fermante."

#. type: Title ###
#: en/./developers/02_First_steps.md:299
#, no-wrap
msgid "Arrays"
msgstr "Tableaux"

#. type: Plain text
#: en/./developers/02_First_steps.md:302
msgid ""
"If an array declaration runs on more than one line, each element must be "
"followed by a comma, including the last one."
msgstr ""
"Lors de l’écriture de tableaux sur plusieurs lignes, tous les éléments "
"doivent être suivis d’une virgule (même le dernier)."

#. type: Fenced code block (php)
#: en/./developers/02_First_steps.md:303
#, no-wrap
msgid ""
"$variable = [\n"
"\t\"value 1\",\n"
"\t\"value 2\",\n"
"\t\"value 3\",\n"
"];\n"
msgstr ""
"$variable = [\n"
"\t\"valeur 1\",\n"
"\t\"valeur 2\",\n"
"\t\"valeur 3\",\n"
"];\n"

#. type: Title #
#: en/./developers/02_GitHub.md:1
#, no-wrap
msgid "Branching"
msgstr "Système de branches"

#. type: Title ##
#: en/./developers/02_GitHub.md:3
#, no-wrap
msgid "Basic"
msgstr "Élémentaire"

#. type: Plain text
#: en/./developers/02_GitHub.md:6
msgid ""
"If you are new to Git, here are some of the resources you might find useful:"
msgstr ""
"Si vous êtes novice dans Git, voici quelques ressources qui pourraient vous "
"être utiles :"

#. type: Bullet: '* '
#: en/./developers/02_GitHub.md:11
msgid "[GitHub’s blog post](https://github.com/blog/120-new-to-git)"
msgstr "[Article du blog de GitHub](https://github.com/blog/120-new-to-git)"

#. type: Bullet: '* '
#: en/./developers/02_GitHub.md:11
msgid "<http://try.github.com/>"
msgstr "<http://try.github.com/>"

#. type: Bullet: '* '
#: en/./developers/02_GitHub.md:11
msgid "<http://sixrevisions.com/resources/git-tutorials-beginners/>"
msgstr "<http://sixrevisions.com/resources/git-tutorials-beginners/>"

#. type: Bullet: '* '
#: en/./developers/02_GitHub.md:11
msgid "<http://rogerdudler.github.io/git-guide/>"
msgstr "<http://rogerdudler.github.io/git-guide/>"

#. type: Title ##
#: en/./developers/02_GitHub.md:12
#, no-wrap
msgid "Getting the latest code from the FreshRSS repository"
msgstr "Obtenir le dernier code du répertoire FreshRSS"

#. type: Plain text
#: en/./developers/02_GitHub.md:15
msgid "First you need to add the official repo to your remote repo list:"
msgstr ""
"Vous devez avant tout ajouter le repo officiel à votre liste de repo remote :"

#. type: Fenced code block (sh)
#: en/./developers/02_GitHub.md:16
#, no-wrap
msgid "git remote <NAME_EMAIL>:FreshRSS/FreshRSS.git\n"
msgstr "git remote <NAME_EMAIL>:FreshRSS/FreshRSS.git\n"

#. type: Plain text
#: en/./developers/02_GitHub.md:21
msgid "You can verify the remote repo is successfully added by using:"
msgstr ""
"Vous pouvez vérifier que le repo remote a été ajouté avec succès en "
"utilisant :"

#. type: Fenced code block (sh)
#: en/./developers/02_GitHub.md:22
#, no-wrap
msgid "git remote -v show\n"
msgstr "git remote -v show\n"

#. type: Plain text
#: en/./developers/02_GitHub.md:27
msgid "Now you can pull the latest development code:"
msgstr "Vous pouvez maintenant pull le dernier code de développement :"

#. type: Fenced code block (sh)
#: en/./developers/02_GitHub.md:28
#, no-wrap
msgid ""
"git checkout edge\n"
"git pull upstream edge\n"
msgstr ""
"git checkout edge\n"
"git pull upstream edge\n"

#. type: Title ##
#: en/./developers/02_GitHub.md:33
#, no-wrap
msgid "Starting a new development branch"
msgstr "Lancer une nouvelle branche de développement"

#. type: Fenced code block (sh)
#: en/./developers/02_GitHub.md:35
#, no-wrap
msgid "git checkout -b my-development-branch\n"
msgstr "git checkout -b mon-branch-developpement\n"

#. type: Title ##
#: en/./developers/02_GitHub.md:39
#, no-wrap
msgid "Sending a patch"
msgstr "Proposer un patch"

#. type: Fenced code block (sh)
#: en/./developers/02_GitHub.md:41
#, no-wrap
msgid ""
"# Add the changed file, here actualize_script.php\n"
"git add app/actualize_script.php\n"
"# Commit the change and write a proper commit message\n"
"git commit\n"
"# Double check all looks well\n"
"git show\n"
"# Push it to your fork\n"
"git push\n"
msgstr ""
"# Ajoutez le fichier modifié, ici actualize_script.php\n"
"git add app/actualize_script.php\n"
"# Commitez le changement et écrivez un message de commit approprié.\n"
"git commit\n"
"# Vérifiez deux fois que tout a l’air d’aller bien\n"
"git show\n"
"# Poussez les changements sur ton fork\n"
"git push\n"

#. type: Plain text
#: en/./developers/02_GitHub.md:53
msgid "Now you can create a PR based on your branch."
msgstr "Vous pouvez maintenant créer une PR en fonction de votre branche."

#. type: Title ##
#: en/./developers/02_GitHub.md:54
#, no-wrap
msgid "How to write a commit message"
msgstr "Comment écrire un message de commit"

#. type: Plain text
#: en/./developers/02_GitHub.md:57
#, fuzzy
#| msgid ""
#| "A commit message should succintly describe the changes on the first line. "
#| "For example:"
msgid ""
"A commit message should succinctly describe the changes on the first line. "
"For example:"
msgstr ""
"Un message de commit devrait décrire succinctement les changements sur la "
"première ligne. Par exemple :"

#. type: Plain text
#: en/./developers/02_GitHub.md:59
#, no-wrap
msgid "> Fix broken icon\n"
msgstr "> Fixe une icône cassée\n"

#. type: Plain text
#: en/./developers/02_GitHub.md:61
msgid ""
"If necessary, this can be followed by a blank line and a longer explanation."
msgstr ""
"Si nécessaire, une ligne blanche et une explication plus longue peuvent le "
"suivre."

#. type: Plain text
#: en/./developers/02_GitHub.md:62
msgid "For further tips, see [here](https://chris.beams.io/posts/git-commit/)."
msgstr ""
"Pour d’autres conseils, voir [ici](https://chris.beams.io/posts/git-commit/)."

#. type: Title #
#: en/./developers/03_Backend/01_Database_schema.md:1
#, no-wrap
msgid "Database Schema"
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/01_Database_schema.md:4
#: en/./developers/03_Backend/05_Extensions.md:169
#: en/./developers/04_Frontend/01_View_files.md:6
#: en/./developers/04_Frontend/01_View_files.md:10
#: en/./developers/04_Frontend/01_View_files.md:14
#: en/./developers/04_Frontend/01_View_files.md:17
#: en/./users/05_Configuration.md:185
#, fuzzy, no-wrap
#| msgid "**TODO**\n"
msgid "> **TODO**\n"
msgstr "**À FAIRE**\n"

#. type: Title ##
#: en/./developers/03_Backend/01_Database_schema.md:5
#, no-wrap
msgid "See also"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/01_Database_schema.md:7
msgid "[Database configuration](../../admins/DatabaseConfig.md)"
msgstr ""

#. type: Title #
#: en/./developers/03_Backend/03_External_libraries.md:1
#, no-wrap
msgid "External Libraries"
msgstr ""

#. type: Title #
#: en/./developers/03_Backend/05_Extensions.md:1
#, no-wrap
msgid "Writing extensions for FreshRSS"
msgstr "Écriture d’extensions pour FreshRSS"

#. type: Title ##
#: en/./developers/03_Backend/05_Extensions.md:3
#, no-wrap
msgid "About FreshRSS"
msgstr "Présentation de FreshRSS"

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:6
msgid ""
"FreshRSS is an RSS / Atom feed aggregator written in PHP dating back to "
"October 2012. The official site is located at [freshrss.org](https://"
"freshrss.org) and the official repository is hosted on GitHub: [github.com/"
"FreshRSS/FreshRSS](https://github.com/FreshRSS/FreshRSS)."
msgstr ""
"FreshRSS est un agrégateur de flux RSS / Atom écrit en PHP depuis octobre "
"2012. Le site officiel est situé à l’adresse [freshrss.org](https://freshrss."
"org) et son dépot Git est hébergé par GitHub : [github.com/FreshRSS/FreshRSS]"
"(https://github.com/FreshRSS/FreshRSS)."

#. type: Title ##
#: en/./developers/03_Backend/05_Extensions.md:7
#, no-wrap
msgid "The problem"
msgstr "Problème à résoudre"

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:10
msgid "FreshRSS is limited in its technical possibilities by various factors:"
msgstr ""
"FreshRSS est limité dans ses possibilités techniques par différents "
"facteurs :"

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:14
msgid "The number of developers"
msgstr "La disponibilité des développeurs principaux ;"

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:14
msgid "The will to integrate certain changes"
msgstr "La volonté d’intégrer certains changements ;"

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:14
msgid "The level of \"hacking\" required to integrate marginal features"
msgstr ""
"Le niveau de « hack » nécessaire pour intégrer des fonctionnalités à la "
"marge."

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:16
msgid ""
"While the first limitation can, in theory, be lifted by the participation of "
"new contributors to the project, it depends on the willingness of "
"contributors to take an interest in the source code of the entire project. "
"In order to remove the other two limitations, most of the time it will be "
"necessary to create a \"fork\"."
msgstr ""
"Si la première limitation peut, en théorie, être levée par la participation "
"de nouveaux contributeurs au projet, elle est en réalité conditionnée par la "
"volonté des contributeurs à s’intéresser au code source du projet en entier. "
"Afin de lever les deux autres limitations quant à elles, il faudra la "
"plupart du temps passer par un « à-coté » souvent synonyme de « fork »."

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:18
msgid ""
"Another solution consists of an extension system. By allowing users to write "
"their own extension without taking an interest in the core of the basic "
"software, we allow for:"
msgstr ""
"Une autre solution consiste à passer par un système d’extensions. En "
"permettant à des utilisateurs d’écrire leur propre extension sans avoir à "
"s’intéresser au cœur même du logiciel de base, on permet :"

#. type: Bullet: '1. '
#: en/./developers/03_Backend/05_Extensions.md:22
msgid "Reducing the amount of source code a new contributor has to take in"
msgstr ""
"De réduire la quantité de code source à assimiler pour un nouveau "
"contributeur ;"

#. type: Bullet: '2. '
#: en/./developers/03_Backend/05_Extensions.md:22
msgid "Unofficial integration of novelties"
msgstr "De permettre d’intégrer des nouveautés de façon non-officielles ;"

#. type: Bullet: '3. '
#: en/./developers/03_Backend/05_Extensions.md:22
msgid "No forking or main developer approval required."
msgstr ""
"De se passer des développeurs principaux pour d’éventuelles améliorations "
"sans passer par la case « fork »."

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:24
msgid ""
"Note: it is quite conceivable that the functionalities of an extension can "
"later be officially integrated into the FreshRSS code. Extensions make it "
"easy to propose a proof of concept."
msgstr ""
"Note : il est tout à fait imaginable que les fonctionnalités d’une extension "
"puissent par la suite être intégrées dans le code initial de FreshRSS de "
"façon officielle. Cela permet de proposer un « proof of concept » assez "
"facilement."

#. type: Title #
#: en/./developers/03_Backend/05_Extensions.md:25
#: en/./developers/Minz/index.md:1
#, no-wrap
msgid "Minz Framework"
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:28
msgid "see [Minz documentation](/docs/en/developers/Minz/index.md)"
msgstr ""

#. type: Title ##
#: en/./developers/03_Backend/05_Extensions.md:29
#, no-wrap
msgid "Write an extension for FreshRSS"
msgstr "Écrire une extension pour FreshRSS"

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:32
msgid ""
"Here we are! We’ve talked about the most useful features of Minz and how to "
"run FreshRSS correctly and it’s about time to address the extensions "
"themselves."
msgstr ""
"Nous y voilà ! Nous avons abordé les fonctionnalités les plus utiles de Minz "
"et qui permettent de faire tourner FreshRSS correctement et il est plus que "
"temps d’aborder les extensions en elles-même."

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:34
msgid ""
"An extension allows you to easily add functionality to FreshRSS without "
"having to touch the core of the project directly."
msgstr ""
"Une extension permet donc d’ajouter des fonctionnalités facilement à "
"FreshRSS sans avoir à toucher au cœur du projet directement."

#. type: Title ###
#: en/./developers/03_Backend/05_Extensions.md:35
#, no-wrap
msgid "Make it work in Docker"
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:38
msgid ""
"When working on an extension, it’s easier to see it working directly in its "
"environment. With Docker, you can leverage the use of the ```volume``` "
"option when starting the container. Hopefully, you can use it without Docker-"
"related knowledge by using the Makefile rule:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./developers/03_Backend/05_Extensions.md:38
#, no-wrap
msgid "make start extensions=\"/full/path/to/extension/1 /full/path/to/extension/2\"\n"
msgstr ""

#. type: Title ###
#: en/./developers/03_Backend/05_Extensions.md:42
#, no-wrap
msgid "Basic files and folders"
msgstr "Les fichiers et répertoires de base"

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:47
msgid ""
"The first thing to note is that **all** extensions **must** be located in "
"the `extensions` directory, at the base of the FreshRSS tree.  An extension "
"is a directory containing a set of mandatory (and optional) files and "
"subdirectories.  The convention requires that the main directory name be "
"preceded by an \"x\" to indicate that it is not an extension included by "
"default in FreshRSS."
msgstr ""
"La première chose à noter est que **toutes** les extensions **doivent** se "
"situer dans le répertoire `extensions`, à la base de l’arborescence de "
"FreshRSS. Une extension est un répertoire contenant un ensemble de fichiers "
"et sous-répertoires obligatoires ou facultatifs. La convention veut que l’on "
"précède le nom du répertoire principal par un « x » pour indiquer qu’il ne "
"s’agit pas d’une extension incluse par défaut dans FreshRSS."

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:49
msgid ""
"The main directory of an extension must contain at least two **mandatory** "
"files:"
msgstr ""
"Le répertoire principal d’une extension doit comporter au moins deux "
"fichiers **obligatoire** :"

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:52
msgid ""
"A `metadata.json` file that contains a description of the extension. This "
"file is written in JSON."
msgstr ""
"Un fichier `metadata.json` qui contient une description de l’extension. Ce "
"fichier est écrit en JSON ;"

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:52
msgid ""
"An `extension.php` file containing the entry point of the extension (which "
"is a class that inherits Minz_Extension)."
msgstr "Un fichier `extension.php` contenant le point d’entrée de l’extension."

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:56
msgid ""
"Please note that there is a not a required link between the directory name "
"of the extension and the name of the class inside `extension.php`, but you "
"should follow our best practice: If you want to write a `HelloWorld` "
"extension, the directory name should be `xExtension-HelloWorld` and the base "
"class name `HelloWorldExtension`."
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:58
msgid ""
"In the file `freshrss/extensions/xExtension-HelloWorld/extension.php` you "
"need the structure:"
msgstr ""

#. type: Fenced code block (php)
#: en/./developers/03_Backend/05_Extensions.md:58
#, no-wrap
msgid ""
"final class HelloWorldExtension extends Minz_Extension {\n"
"\t#[\Override]\n"
"\tpublic function init(): void {\n"
"\t\tparent::init();\n"
"\n"
"\t\t// your code here\n"
"\t}\n"
"}\n"
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:67
msgid ""
"There is an example HelloWorld extension that you can download from [our "
"GitHub repo](https://github.com/FreshRSS/xExtension-HelloWorld)."
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:69
msgid ""
"You may also need additional files or subdirectories depending on your needs:"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:75
msgid ""
"`configure.phtml` is the file containing the form to parameterize your "
"extension"
msgstr ""
"`configure.phtml` est le fichier contenant le formulaire pour paramétrer "
"votre extension"

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:75
msgid ""
"A `static/` directory containing CSS and JavaScript files that you will need "
"for your extension (note that if you need to write a lot of CSS it may be "
"more interesting to write a complete theme)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:75
msgid "A `Controllers` directory containing additional controllers"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:75
msgid "An `i18n` directory containing additional translations"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:75
msgid ""
"`layout` and `views` directories to define new views or to overwrite the "
"current views"
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:77
msgid ""
"In addition, it is good to have a `LICENSE` file indicating the license "
"under which your extension is distributed and a `README` file giving a "
"detailed description of it."
msgstr ""

#. type: Title ###
#: en/./developers/03_Backend/05_Extensions.md:78
#, no-wrap
msgid "The metadata.json file"
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:81
msgid ""
"The `metadata.json` file defines your extension through a number of "
"important elements. It must contain a valid JSON array containing the "
"following entries:"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:89
msgid "`name`: the name of your extension"
msgstr "`name` : le nom de votre extension ;"

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:89
msgid ""
"`author`: your name, your e-mail address … but there is no specific format "
"to adopt"
msgstr ""
"`author` : votre nom, éventuellement votre adresse mail mais il n’y a pas de "
"format spécifique à adopter ;"

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:89
msgid "`description`: a description of your extension"
msgstr "`description` : une description de votre extension ;"

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:89
msgid "`version`: the current version number of the extension"
msgstr "`version` : le numéro de version actuel de l’extension ;"

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:89
#, fuzzy, no-wrap
#| msgid "`entrypoint`: Indicates the entry point of your extension. It must match the name of the class contained in the file `extension.php` without the suffix` Extension` (so if the entry point is `HelloWorld`, your class will be called` HelloWorldExtension`)"
msgid ""
"* `entrypoint`: Indicates the entry point of your extension. It must match the name of the class contained in the file `extension.php` without the suffix `Extension`\n"
"(so if the entry point is `HelloWorld`, your class will be called `HelloWorldExtension`)\n"
"* `type`: Defines the type of your extension. There are two types: `system` and `user`. We will study this difference right after.\n"
msgstr "`entrypoint` : indique le point d’entrée de votre extension. Il doit correspondre au nom de la classe contenue dans le fichier `extension.php` sans le suffixe `Extension` (donc si le point d’entrée est `HelloWorld`, votre classe s’appellera `HelloWorldExtension`) ;"

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:91
#, fuzzy
#| msgid "Only the `name` and` entrypoint` fields are required."
msgid "Only the `name` and `entrypoint` fields are required."
msgstr "Seuls les champs `name` et `entrypoint` sont requis."

#. type: Title ###
#: en/./developers/03_Backend/05_Extensions.md:92
#, no-wrap
msgid "Choosing between `system` and `user`"
msgstr "Choisir entre extension « system » ou « user »"

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:95
msgid ""
"A *user* extension can be enabled by some users and not by others (typically "
"for user preferences)."
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:97
msgid "A *system* extension in comparison is enabled for every account."
msgstr ""

#. type: Title ###
#: en/./developers/03_Backend/05_Extensions.md:98
#, no-wrap
msgid "Writing your own extension.php"
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:102
msgid ""
"This file is the core of your extension.  It must define some key elements "
"to be loaded by the extension system:"
msgstr ""

#. type: Bullet: '1. '
#: en/./developers/03_Backend/05_Extensions.md:106
#, fuzzy
#| msgid ""
#| "`entrypoint`: Indicates the entry point of your extension. It must match "
#| "the name of the class contained in the file `extension.php` without the "
#| "suffix` Extension` (so if the entry point is `HelloWorld`, your class "
#| "will be called` HelloWorldExtension`)"
msgid ""
"The class name must be the `entrypoint` value defined in the `metadata.json` "
"file suffixed by `Extension` (if your `entrypoint` value is `HelloWorld`, "
"your class name will be `HelloWorldExtension`)."
msgstr ""
"`entrypoint` : indique le point d’entrée de votre extension. Il doit "
"correspondre au nom de la classe contenue dans le fichier `extension.php` "
"sans le suffixe `Extension` (donc si le point d’entrée est `HelloWorld`, "
"votre classe s’appellera `HelloWorldExtension`) ;"

#. type: Bullet: '1. '
#: en/./developers/03_Backend/05_Extensions.md:106
msgid ""
"The class must extend the `Minz_Extension` abstract class which defines the "
"core methods and properties of a FreshRSS extension."
msgstr ""

#. type: Bullet: '1. '
#: en/./developers/03_Backend/05_Extensions.md:106
msgid ""
"The class must define the `init` method. This method is called **only** if "
"the extension is loaded. Its purpose is to initialize the extension and its "
"behavior during every page load."
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:111
msgid ""
"The `Minz_Extension` abstract class defines a set of methods that can be "
"overridden to fit your needs: * the `install` method is called when the user "
"enables the extension in the configuration page. It must return `true` when "
"successful and a string containing an error message when not. Its purpose is "
"to prepare FreshRSS for the extension (adding a table to the database, "
"creating a folder tree, …).  * the `uninstall` method is called when the "
"user disables the extension in the configuration page. It must return `true` "
"when successful and a string containing an error message when not. Its "
"purpose is to clean FreshRSS (removing a table from the database, deleting a "
"folder tree, …). Usually it reverts changes introduced by the `install` "
"method.  * the `handleConfigureAction` method is called when a user loads "
"the extension configuration panel. It contains the logic to validate and "
"store the submitted values defined in the `configure.phtml` file."
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:113
#, no-wrap
msgid "> If your extension code is scattered in different classes, you need to load their source before using them. Of course you could include the files manually, but it’s more efficient to load them automatically. To do so, you just need to define the `autoload` method which will include them when needed. This method will be registered automatically when the extension is enabled.\n"
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:127
#, no-wrap
msgid ""
"The `Minz_Extension` abstract class defines another set of methods that should not be overridden:\n"
"* the `getName`, `getEntrypoint`, `getPath`, `getAuthor`, `getDescription`, `getVersion`, and `getType` methods return the extension internal properties. Those properties are extracted from the `metadata.json` file.\n"
"* the `getFileUrl` returns the URL of the selected file. The file must exist in the `static` folder of the extension.\n"
"* the `registerController` method register an extension controller in FreshRSS. The selected controller must be defined in the extension *Controllers* folder, its file name must be `\\<name\\>Controller.php`, and its class name must be `FreshExtension_\\<name\\>_Controller`.\n"
"* the `registerViews` method registers the extension views in FreshRSS.\n"
"* the `registerTranslates` method registers the extension translation files in FreshRSS.\n"
"* the `registerHook` method registers hook actions in different part of the application.\n"
"* the `getSystemConfiguration` method retrieves the extension configuration for the system.\n"
"* the `setSystemConfiguration` method stores the extension configuration for the system.\n"
"* the `removeSystemConfiguration` method removes the extension configuration for the system.\n"
"* the `getUserConfiguration` method retrieves the extension configuration for the current user.\n"
"* the `setUserConfiguration` method stores the extension configuration for the current user.\n"
"* the `removeUserConfiguration` method removes the extension configuration for the current user.\n"
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:129
#, no-wrap
msgid "> Note that if you modify the later set of methods, you might break the extension system. Thus making FreshRSS unusable. So it’s highly recommended to let those unmodified.\n"
msgstr ""

#. type: Title ###
#: en/./developers/03_Backend/05_Extensions.md:130
#, no-wrap
msgid "The \"hooks\" system"
msgstr "Le système « hooks »"

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:133
msgid ""
"You can register at the FreshRSS event system in an extensions `init()` "
"method, to manipulate data when some of the core functions are executed."
msgstr ""

#. type: Fenced code block (html)
#: en/./developers/03_Backend/05_Extensions.md:134
#, no-wrap
msgid ""
"final class HelloWorldExtension extends Minz_Extension\n"
"{\n"
"\t#[\Override]\n"
"\tpublic function init(): void {\n"
"\t\tparent::init();\n"
"\n"
"\t\t$this->registerHook('entry_before_display', array($this, 'renderEntry'));\n"
"\t}\n"
"\tpublic function renderEntry($entry) {\n"
"\t\t$entry->_content('<h1>Hello World</h1>' . $entry->content());\n"
"\t\treturn $entry;\n"
"\t}\n"
"}\n"
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:148
msgid "The following events are available:"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`check_url_before_add` (`function($url) -> Url | null`): will be executed "
"every time a URL is added. The URL itself will be passed as parameter. This "
"way a website known to have feeds which doesn’t advertise it in the header "
"can still be automatically supported."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`entry_before_display` (`function($entry) -> Entry | null`): will be "
"executed every time an entry is rendered. The entry itself (instance of "
"FreshRSS\\_Entry) will be passed as parameter."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`entry_before_insert` (`function($entry) -> Entry | null`): will be executed "
"when a feed is refreshed and new entries will be imported into the database. "
"The new entry (instance of FreshRSS\\_Entry) will be passed as parameter."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`feed_before_actualize` (`function($feed) -> Feed | null`): will be executed "
"when a feed is updated. The feed (instance of FreshRSS\\_Feed) will be "
"passed as parameter."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`feed_before_insert` (`function($feed) -> Feed | null`): will be executed "
"when a new feed is imported into the database. The new feed (instance of "
"FreshRSS\\_Feed) will be passed as parameter."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`freshrss_init` (`function() -> none`): will be executed at the end of the "
"initialization of FreshRSS, useful to initialize components or to do "
"additional access checks."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`freshrss_user_maintenance` (`function() -> none`): will be executed for "
"each user during the `actualize_script`, useful to run some maintenance "
"tasks on the user."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`js_vars` (`function($vars = array) -> array | null`): will be executed if "
"the `jsonVars` in the header will be generated."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`menu_admin_entry` (`function() -> string`): add an entry at the end of the "
"\"Administration\" menu, the returned string must be valid HTML (e.g. `<li "
"class=\"item active\"><a href=\"url\">New entry</a></li>`)."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`menu_configuration_entry` (`function() -> string`): add an entry at the end "
"of the \"Configuration\" menu, the returned string must be valid HTML (e.g. "
"`<li class=\"item active\"><a href=\"url\">New entry</a></li>`)."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`menu_other_entry` (`function() -> string`): add an entry at the end of the "
"header dropdown menu (i.e. after the \"About\" entry), the returned string "
"must be valid HTML (e.g. `<li class=\"item active\"><a href=\"url\">New "
"entry</a></li>`)."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`nav_menu` (`function() -> string`): will be executed if the navigation was "
"built."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`nav_reading_modes` (`function($reading_modes) -> array | null`): **TODO** "
"add documentation."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid "`post_update` (`function(none) -> none`): **TODO** add documentation."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/03_Backend/05_Extensions.md:164
msgid ""
"`simplepie_before_init` (`function($simplePie, $feed) -> none`): **TODO** "
"add documentation."
msgstr ""

#. type: Title ###
#: en/./developers/03_Backend/05_Extensions.md:165
#, no-wrap
msgid "Writing your own configure.phtml"
msgstr ""

#. type: Plain text
#: en/./developers/03_Backend/05_Extensions.md:168
msgid ""
"When you want to support user configurations for your extension or simply "
"display some information, you have to create the `configure.phtml` file."
msgstr ""

#. type: Title #
#: en/./developers/03_Running_tests.md:1
#, no-wrap
msgid "Running tests"
msgstr ""

#. type: Plain text
#: en/./developers/03_Running_tests.md:4
msgid ""
"FreshRSS is tested with [PHPUnit](https://phpunit.de/). No code should be "
"merged in `edge` if the tests don’t pass."
msgstr ""

#. type: Title ##
#: en/./developers/03_Running_tests.md:5
#, no-wrap
msgid "Locally"
msgstr ""

#. type: Plain text
#: en/./developers/03_Running_tests.md:8
msgid ""
"As a developer, you can run the test suite on your PC easily with `make` "
"commands. You can run the test suite with:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./developers/03_Running_tests.md:9
#, fuzzy, no-wrap
#| msgid "$ make stop\n"
msgid "make test\n"
msgstr "$ make stop\n"

#. type: Plain text
#: en/./developers/03_Running_tests.md:14
msgid ""
"This command downloads the PHPUnit binary and verifies its checksum. If the "
"verification fails, the file is deleted. In this case, you should [open an "
"issue on GitHub](https://github.com/FreshRSS/FreshRSS/issues/new) to let "
"maintainers know about the problem."
msgstr ""

#. type: Plain text
#: en/./developers/03_Running_tests.md:16
msgid ""
"Then, it executes PHPUnit in a Docker container. If you don’t use Docker, "
"you can run the command directly with:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./developers/03_Running_tests.md:17
#, fuzzy, no-wrap
#| msgid "$ make stop\n"
msgid "NO_DOCKER=true make test\n"
msgstr "$ make stop\n"

#. type: Plain text
#: en/./developers/03_Running_tests.md:22
msgid "The linter can be run with a `make` command as well:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./developers/03_Running_tests.md:23
#, no-wrap
msgid ""
"make lint # to execute the linter on the PHP files\n"
"make lint-fix # or, to fix the errors detected by the linter\n"
msgstr ""

#. type: Plain text
#: en/./developers/03_Running_tests.md:29
msgid ""
"Similarly to PHPUnit, it downloads a [PHP\\_CodeSniffer](https://github.com/"
"squizlabs/PHP_CodeSniffer) binary (i.e. `phpcs` or `phpcbf` depending on the "
"command) and verifies its checksum."
msgstr ""

#. type: Title ##
#: en/./developers/03_Running_tests.md:30
#, no-wrap
msgid "GitHub Actions for Continuous Integration"
msgstr ""

#. type: Plain text
#: en/./developers/03_Running_tests.md:35
msgid ""
"Tests are automatically run when you open a pull request on GitHub.  They "
"are performed with [GitHub Actions](https://github.com/FreshRSS/FreshRSS/"
"actions).  This ensures your code will not introduce some kind of "
"regression. We will not merge a PR if tests fail so we will ask you to fix "
"any bugs before reviewing your code."
msgstr ""

#. type: Plain text
#: en/./developers/03_Running_tests.md:37
#, fuzzy
#| msgid ""
#| "If your request is new, [open a new bug ticket](https://github.com/"
#| "FreshRSS/FreshRSS/issues/new)"
msgid ""
"If you are interested, you can take a look at [the configuration file]"
"(https://github.com/FreshRSS/FreshRSS/blob/edge/.github/workflows/tests.yml)."
msgstr ""
"Si votre demande est nouvelle, [ouvrez un nouveau ticket de bug](https://"
"github.com/FreshRSS/FreshRSS/issues/new)"

#. type: Title ##
#: en/./developers/03_Running_tests.md:38
#, no-wrap
msgid "Using feed snapshots"
msgstr ""

#. type: Plain text
#: en/./developers/03_Running_tests.md:42
msgid ""
"As feed data is volatile, it’s better to work with snapshots when debugging "
"some issues.  You can find the description to retrieve a snapshot [here]"
"(06_Reporting_Bugs.md#how-to-provide-feed-data)."
msgstr ""

#. type: Plain text
#: en/./developers/03_Running_tests.md:46
msgid ""
"To serve those snapshots, you can use a mock server.  Here we will "
"demonstrate how to work with [WireMock](https://wiremock.org/) but other "
"solutions exist.  Here are the steps to start using the WireMock mock server:"
msgstr ""

#. type: Plain text
#: en/./developers/03_Running_tests.md:76
#, no-wrap
msgid ""
"1. Go to the mock server home folder.\n"
"If you do not have one, you need to create one.\n"
"1. Inside the mock server home folder, create the ___file_ and _mappings_ folders.\n"
"1. Copy or move your snapshots in the ___file_ folder.\n"
"1. Create the _feed.json_ file in the _mappings_ folder with the following content:\n"
"\t```js\n"
"\t{\n"
"\t\t\"request\": {\n"
"\t\t\t\"method\": \"GET\",\n"
"\t\t\t\"urlPathPattern\": \"/.*\"\n"
"\t\t},\n"
"\t\t\"response\": {\n"
"\t\t\t\"status\": 200,\n"
"\t\t\t\"bodyFileName\": \"{{request.pathSegments.[0]}}\",\n"
"\t\t\t\"transformers\": [\"response-template\"],\n"
"\t\t\t\"headers\": {\n"
"\t\t\t\t\"Content-Type\": \"application/rss+xml\"\n"
"\t\t\t}\n"
"\t\t}\n"
"\t}\n"
"\t```\n"
"1. Launch the containerized server with the following command:\n"
"\t```bash\n"
"\t# <PORT> is the port used on the host to communicate with the server\n"
"\t# <NETWORK> is the name of the docker network used (by default, it’s freshrss-network)\n"
"\tdocker run -it --rm -p <PORT>:8080 --name wiremock --network <NETWORK> -v $PWD:/home/<USER>/wiremock:latest-alpine --local-response-templating\n"
"\t```\n"
"1. You can access the `<RSS>` mock file directly:\n"
"   * from the host by sending a GET request to `http://localhost:<PORT>/<RSS>`,\n"
"   * from any container connected on the same network by sending a GET request to `http://wiremock:8080/<RSS>`.\n"
msgstr ""

#. type: Title #
#: en/./developers/04_Frontend/01_View_files.md:1
#, fuzzy, no-wrap
#| msgid "Views"
msgid "View files"
msgstr "Vues"

#. type: Title ##
#: en/./developers/04_Frontend/01_View_files.md:3
#, no-wrap
msgid "The .phtml files"
msgstr "Les fichiers .phtml"

#. type: Title ##
#: en/./developers/04_Frontend/01_View_files.md:7
#, no-wrap
msgid "Writing a URL"
msgstr "Écrire une URL"

#. type: Title ##
#: en/./developers/04_Frontend/01_View_files.md:11
#, no-wrap
msgid "Displaying an icon"
msgstr "Afficher une icône"

#. type: Title ##
#: en/./developers/04_Frontend/01_View_files.md:15
#, no-wrap
msgid "Internationalisation"
msgstr "Internationalisation"

#. type: Title #
#: en/./developers/04_Frontend/02_Design.md:1
#, no-wrap
msgid "Writing a new theme"
msgstr "Écrire un nouveau thème"

#. type: Plain text
#: en/./developers/04_Frontend/02_Design.md:4
#, no-wrap
msgid "**Note: Currently personal themes are not officially supported and may be overwritten when updating. Be sure to keep backups!**\n"
msgstr ""

#. type: Plain text
#: en/./developers/04_Frontend/02_Design.md:6
#, no-wrap
msgid "**As of writing (02-02-2021), support for themes as extensions is under development.**\n"
msgstr ""

#. type: Plain text
#: en/./developers/04_Frontend/02_Design.md:8
msgid ""
"The easiest way to create a theme is by copying and modifying the base theme "
"(or another of the pre-installed themes). The base theme can be found in [/p/"
"themes/base-theme](https://github.com/FreshRSS/FreshRSS/tree/edge/p/themes/"
"base-theme). Themes require:"
msgstr ""

#. type: Bullet: '- '
#: en/./developers/04_Frontend/02_Design.md:13
msgid "a **metadata.json** file to describe the theme."
msgstr ""

#. type: Bullet: '- '
#: en/./developers/04_Frontend/02_Design.md:13
msgid ""
"a **loader.gif** file to use as a loading icon (assuming .loading’s "
"background has not been overridden)"
msgstr ""

#. type: Bullet: '- '
#: en/./developers/04_Frontend/02_Design.md:13
msgid ""
"an **icons** folder containing .svg, .ico, and .png files to override "
"existing icons"
msgstr ""

#. type: Bullet: '- '
#: en/./developers/04_Frontend/02_Design.md:13
msgid ""
"a **thumbs** folder containing a file, **original.png** that will be used as "
"the preview for the theme"
msgstr ""

#. type: Plain text
#: en/./developers/04_Frontend/02_Design.md:15
msgid ""
"\"_frss.css\" is normally added to the metadata file as a fallback for "
"missing aspects. The file is taken from the base theme. If submitting a pull "
"request for a theme, please know that [pull request themes must include this "
"file.](https://github.com/FreshRSS/FreshRSS/pull/2938#issuecomment-624085450)"
msgstr ""

#. type: Title ##
#: en/./developers/04_Frontend/02_Design.md:16
#, no-wrap
msgid "RTL Support"
msgstr ""

#. type: Plain text
#: en/./developers/04_Frontend/02_Design.md:19
msgid ""
"RTL (right-to-left) support for languages such as Hebrew and Arabic is "
"handled through CSSJanus. To generate an RTL CSS file from your standard "
"file, use `make rtl`. Be sure to commit the resulting file (filename.rtl."
"css)."
msgstr ""

#. type: Title ##
#: en/./developers/04_Frontend/02_Design.md:20
#, no-wrap
msgid "Overriding icons"
msgstr "Surcharger les icônes"

#. type: Plain text
#: en/./developers/04_Frontend/02_Design.md:23
msgid ""
"To replace the default icons, add an \"icons\" folder to your theme’s "
"folder. Use files with the same name as the default icon to override them."
msgstr ""

#. type: Title ##
#: en/./developers/04_Frontend/02_Design.md:24
#, no-wrap
msgid "Template file"
msgstr "Fichier modèle"

#. type: Plain text
#: en/./developers/04_Frontend/02_Design.md:27
msgid "`metadata.json`"
msgstr ""

#. type: Fenced code block (json)
#: en/./developers/04_Frontend/02_Design.md:28
#, no-wrap
msgid ""
"{\n"
"\t\"name\": \"Theme name\",\n"
"\t\"author\": \"Theme author\",\n"
"\t\"description\": \"Theme description\",\n"
"\t\"version\": 0.1,\n"
"\t\"files\": [\"_frss.css\", \"file1.css\", \"file2.css\"]\n"
"}\n"
msgstr ""

#. type: Plain text
#: en/./developers/04_Frontend/02_Design.md:38
msgid ""
"An example of a css theme file can be found at [/p/themes/base-theme/base."
"css](https://github.com/FreshRSS/FreshRSS/blob/edge/p/themes/base-theme/base."
"css)"
msgstr ""

#. type: Title #
#: en/./developers/04_Pull_requests.md:1
#, no-wrap
msgid "Opening a pull request"
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:4
#, fuzzy
#| msgid ""
#| "[Go to the bug ticket manager](https://github.com/FreshRSS/FreshRSS/"
#| "issues)"
msgid ""
"So you want to propose a patch to the community? It’s time to open a [pull "
"request](https://github.com/FreshRSS/FreshRSS/pulls)!"
msgstr ""
"[Rendez-vous sur le gestionnaire de tickets de bugs](https://github.com/"
"FreshRSS/FreshRSS/issues)"

#. type: Plain text
#: en/./developers/04_Pull_requests.md:6
msgid ""
"When you open a PR, your message will be prefilled with a message based on "
"[a template](https://github.com/FreshRSS/FreshRSS/blob/edge/docs/"
"pull_request_template.md). It contains a checklist to make sure you didn’t "
"forget anything. It is very important to verify you did everything mentioned "
"so documentation is up-to-date, the commit history stays clear and the code "
"is always stable."
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:8
msgid "The rest of this document explains specific points."
msgstr ""

#. type: Title ##
#: en/./developers/04_Pull_requests.md:9
#, no-wrap
msgid "How to rebase your branch on `edge`"
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:12
#, no-wrap
msgid "**TODO:** Update this section. With GitHub’s *squash and merge*, rebasing (and other forms of history rewriting) is more dangerous and annoying (e.g. breaking review mechanism) than useful.\n"
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:14
msgid ""
"Rebasing a branch is useful to make sure your code is based on the most "
"recent version of FreshRSS and there are no conflicts. You have two ways to "
"do that."
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:16
msgid ""
"If you have any doubt, please let us know and we’ll help you! We all began "
"with Git one day and it’s not an easy thing to work with."
msgstr ""

#. type: Title ###
#: en/./developers/04_Pull_requests.md:17
#, no-wrap
msgid "Rebasing"
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:20
msgid ""
"Rebasing is the cleanest method because the Git history will be completely "
"linear and consequently easier to read and navigate. It might also be more "
"difficult if you’re not at ease with Git since conflicts are harder to "
"resolve."
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:22
msgid ""
"Note that you should never rebase a branch if someone else is working on it. "
"Otherwise, since it rewrites the history, it can be a real mess to sort it "
"out."
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:24
msgid "To rebase a branch:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./developers/04_Pull_requests.md:25
#, no-wrap
msgid ""
"git checkout edge       # go on edge branch\n"
"git pull upstream edge  # pull the last version of edge\n"
"git checkout -          # go back to your branch\n"
"git rebase edge         # rebase your branch on edge\n"
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:33
msgid ""
"If you feel confident, you can use `git rebase -i edge` to rewrite your "
"history and make it clearer."
msgstr ""

#. type: Title ###
#: en/./developers/04_Pull_requests.md:34
#, fuzzy, no-wrap
#| msgid "Debugging"
msgid "Merging"
msgstr "Déboguer"

#. type: Plain text
#: en/./developers/04_Pull_requests.md:37
msgid ""
"If you prefer, you can simply merge `edge` into your own branch. Conflicts "
"might be easier to resolve, but your Git history will be less readable. "
"Don’t worry, we will take care of it before merging your PR back into `edge`."
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:39
msgid "To merge `edge`:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./developers/04_Pull_requests.md:40
#, no-wrap
msgid ""
"git checkout edge       # go on edge branch\n"
"git pull upstream edge  # pull the last version of edge\n"
"git checkout -          # go back to your branch\n"
"git merge edge          # merge edge into your branch\n"
msgstr ""

#. type: Title ##
#: en/./developers/04_Pull_requests.md:47
#, fuzzy, no-wrap
#| msgid "How to write a commit message"
msgid "How to write a Git commit message"
msgstr "Comment écrire un message de commit"

#. type: Plain text
#: en/./developers/04_Pull_requests.md:50
msgid ""
"It’s important to have proper commit messages in order to facilitate later "
"debugging, so please read the following advice. Commit messages should "
"explain the choices made in the past (the “why?”)"
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:52
msgid ""
"The first line should start with a verb (e.g., “Add”) and explain the "
"objective of the commit in few words. It’s usually less than 50 characters "
"so it remains concise. You can consider this line the subject of your "
"commit. Think of it as the second part of a sentence that starts with the "
"words “This commit will.”"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/04_Pull_requests.md:54
msgid "This commit will *add feature X*"
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:56
msgid ""
"Then, insert a blank line, and start to write the body. It’s usually wrapped "
"at 72 characters, but you are pretty free in the tone of the message. The "
"body is the place where you can clarify the context of your patch. For "
"instance, you can explain what you were doing when you identified a bug, or "
"the problem you had before your patch. Providing this information helps "
"other developers understand why a specific choice was made, especially when "
"a patch introduces a bug that is identified months later."
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:58
msgid ""
"You also can add references (e.g., the URL to the initial ticket in the bug "
"tracker, or a reference to some forum explaining a point)."
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:60
#, fuzzy
#| msgid ""
#| "For further tips, see [here](https://chris.beams.io/posts/git-commit/)."
msgid ""
"You can find more information about commit messages [on this blog post]"
"(https://chris.beams.io/posts/git-commit/)."
msgstr ""
"Pour d’autres conseils, voir [ici](https://chris.beams.io/posts/git-commit/)."

#. type: Title ##
#: en/./developers/04_Pull_requests.md:61
#, fuzzy, no-wrap
#| msgid "How to write a commit message"
msgid "How to write tests"
msgstr "Comment écrire un message de commit"

#. type: Plain text
#: en/./developers/04_Pull_requests.md:64
msgid ""
"FreshRSS has few tests for now, but we’re working on it. We added this point "
"to the checklist to help us to write more tests, and we would really "
"appreciate it if you wrote a test that ensures your patch is working."
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:66
msgid ""
"We use [PHPUnit](https://phpunit.de/) version 7.5 ([documentation](https://"
"phpunit.readthedocs.io/en/7.5/))."
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:68
msgid ""
"You’ll find more information on how to run tests [in this document]"
"(03_Running_tests.md)."
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:70
msgid ""
"Feel free to ask us for assistance. Not everything will be easy to test, so "
"don’t spend too much time on this."
msgstr ""

#. type: Title ##
#: en/./developers/04_Pull_requests.md:71
#, fuzzy, no-wrap
#| msgid "Contribute to documentation"
msgid "Why you should write documentation"
msgstr "Contribuer à la documentation"

#. type: Plain text
#: en/./developers/04_Pull_requests.md:74
msgid ""
"A friendly project should have correct and complete documentation, so "
"newcomers don’t have to ask too many questions, and users can find answers "
"to their problems. The documentation should not be written “later” or "
"chances are it’ll never be."
msgstr ""

#. type: Plain text
#: en/./developers/04_Pull_requests.md:75
msgid ""
"Our documentation can still be improved quite a bit, so you’re very welcome "
"if you want to help."
msgstr ""

#. type: Title #
#: en/./developers/05_Release_new_version.md:1
#, no-wrap
msgid "Preparing the release"
msgstr "Préparer la sortie"

#. type: Plain text
#: en/./developers/05_Release_new_version.md:5
#, fuzzy
#| msgid ""
#| "In order to get as much feedback as possible before a release, it’s "
#| "preferable to announce it on GitHub by creating a dedicated ticket ([see "
#| "examples] (https://github.com/FreshRSS/FreshRSS/search?"
#| "utf8=%E2%9C%93&q=Call+for+testing&type=Issues)). This should be done **at "
#| "least one week in advance**."
msgid ""
"In order to get as much feedback as possible before a release, it’s "
"preferable to announce it on GitHub by creating a dedicated ticket ([see "
"examples](https://github.com/FreshRSS/FreshRSS/search?"
"utf8=%E2%9C%93&q=Call+for+testing&type=Issues)). This should be done **at "
"least one week in advance**."
msgstr ""
"Afin d’avoir le plus de retour possible avant une sortie, il est préférable "
"de l’annoncer sur GitHub en créant un ticket dédié ([voir les exemples]"
"(https://github.com/FreshRSS/FreshRSS/search?"
"utf8=%E2%9C%93&q=Call+for+testing&type=Issues)). Ceci est à faire **au moins "
"une semaine à l’avance**."

#. type: Plain text
#: en/./developers/05_Release_new_version.md:7
msgid "It’s also recommended to make the <NAME_EMAIL>."
msgstr "Il est aussi recommandé de faire l’<NAME_EMAIL>."

#. type: Title ##
#: en/./developers/05_Release_new_version.md:8
#, no-wrap
msgid "Check the dev status"
msgstr "S’assurer de l’état de dev"

#. type: Plain text
#: en/./developers/05_Release_new_version.md:11
msgid ""
"Before releasing a new version of FreshRSS, you must ensure that the code is "
"stable and free of major bugs. Ideally, our tests should be automated and "
"executed before any publication."
msgstr ""
"Avant de sortir une nouvelle version de FreshRSS, il faut vous assurer que "
"le code est stable et ne présente pas de bugs majeurs. Idéalement, il "
"faudrait que nos tests soient automatisés et exécutés avant toute "
"publication."

#. type: Plain text
#: en/./developers/05_Release_new_version.md:13
msgid ""
"You must also **make sure that the CHANGELOG file is up to date** with the "
"updates of the version to be released."
msgstr ""
"Il faut aussi **vous assurer que le fichier CHANGELOG est à jour** avec les "
"mises à jour de la version à sortir."

#. type: Title ##
#: en/./developers/05_Release_new_version.md:14
#, no-wrap
msgid "Git process"
msgstr "Processus Git"

#. type: Fenced code block (console)
#: en/./developers/05_Release_new_version.md:16
#, no-wrap
msgid ""
"$ git checkout edge\n"
"$ git pull\n"
"$ vim constants.php\n"
"# Update version number x.y.y.z of FRESHRSS_VERSION\n"
"$ git commit -a\n"
"Version x.y.z\n"
"$ git tag -a x.y.z\n"
"Version x.y.z\n"
"$ git push && git push --tags\n"
msgstr ""
"$ git checkout edge\n"
"$ git pull\n"
"$ vim constants.php\n"
"# Mettre à jour le numéro de version x.y.z de FRESHRSS_VERSION\n"
"$ git commit -a\n"
"Version x.y.z\n"
"$ git tag -a x.y.z\n"
"Version x.y.z\n"
"$ git push && git push --tags\n"

#. type: Title ##
#: en/./developers/05_Release_new_version.md:28
#, no-wrap
msgid "Updating `update.freshrss.org`"
msgstr "Mise à jour de update.freshrss.org"

#. type: Plain text
#: en/./developers/05_Release_new_version.md:31
msgid ""
"It’s important to update update.freshrss.org since this is the default "
"service for automatic FreshRSS updates."
msgstr ""
"Il est important de mettre à jour update.freshrss.org puisqu’il s’agit du "
"service par défaut gérant les mises à jour automatiques de FreshRSS."

#. type: Plain text
#: en/./developers/05_Release_new_version.md:33
#, fuzzy
#| msgid ""
#| "The repository managing the code is located on GitHub: [FreshRSS/update."
#| "freshrss.org] (https://github.com/FreshRSS/update.freshrss.org/)."
msgid ""
"The repository managing the code is located on GitHub: [FreshRSS/update."
"freshrss.org](https://github.com/FreshRSS/update.freshrss.org/)."
msgstr ""
"Le dépot gérant le code se trouve sur GitHub : [FreshRSS/update.freshrss.org]"
"(https://github.com/FreshRSS/update.freshrss.org/)."

#. type: Title ##
#: en/./developers/05_Release_new_version.md:34
#, no-wrap
msgid "Writing the update script"
msgstr "Écriture du script de mise à jour"

#. type: Plain text
#: en/./developers/05_Release_new_version.md:37
msgid ""
"The scripts are located in the `./scripts/` directory and must take the form "
"`update_to_x.y.z.z.php`. This directory also contains `update_to_dev.php` "
"intended for updates of the `edge` branch (this script must not include code "
"specific to a particular version!) and `update_util.php`, which contains a "
"list of functions useful for all scripts."
msgstr ""
"Les scripts se trouvent dans le répertoire `./scripts/` et doivent être de "
"la forme `update_to_x.y.z.php`. On trouve aussi dans ce répertoire "
"`update_to_dev.php` destiné aux mises à jour de la branche `edge` (ce script "
"ne doit pas inclure de code spécifique à une version particulière !) et "
"`update_util.php` contenant une liste de fonctions utiles à tous les scripts."

#. type: Plain text
#: en/./developers/05_Release_new_version.md:39
msgid ""
"In order to write a new script, it’s better to copy/paste the last version "
"or to start from `update_to_dev.php`. The first thing to do is to define the "
"URL from which the FreshRSS package will be downloaded (`PACKAGE_URL`). The "
"URL is in the form of `https://codeload.github.com/FreshRSS/FreshRSS/zip/x.y."
"z`."
msgstr ""
"Afin d’écrire un nouveau script, il est préférable de copier / coller celui "
"de la dernière version ou de partir de `update_to_dev.php`. La première "
"chose à faire est de définir l’URL à partir de laquelle sera téléchargée le "
"package FreshRSS (`PACKAGE_URL`). L’URL est de la forme `https://codeload."
"github.com/FreshRSS/FreshRSS/zip/x.y.z`."

#. type: Plain text
#: en/./developers/05_Release_new_version.md:41
msgid "There are then 5 functions that have to be executed:"
msgstr "Il existe ensuite 5 fonctions à remplir :"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:47
msgid ""
"`apply_update()` takes care of saving the directory containing the data, "
"checking its structure, downloading the FreshRSS package, deploying it and "
"cleaning it all up. This function is pre-filled but adjustments can be made "
"if necessary (e.g., reorganization of the `./data` structure). It returns "
"`true` if no problem has occurred or a string indicating a problem;"
msgstr ""
"`apply_update()` qui se charge de sauvegarder le répertoire contenant les "
"données, de vérifier sa structure, de télécharger le package FreshRSS, de le "
"déployer et de tout nettoyer. Cette fonction est pré-remplie mais des "
"ajustements peuvent être faits si besoin est (ex. réorganisation de la "
"structure de `./data`). Elle retourne `true` si aucun problème n’est survenu "
"ou une chaîne de caractères indiquant un soucis ;"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:47
msgid ""
"`need_info_update()` returns `true` if the user must intervene during the "
"update or `false` if not;"
msgstr ""
"`need_info_update()` retourne `true` si l’utilisateur doit intervenir durant "
"la mise à jour ou `false` sinon ;"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:47
msgid ""
"`ask_info_update()` displays a form to the user if `need_info_update()` has "
"returned `true`;"
msgstr ""
"`ask_info_update()` affiche un formulaire à l’utilisateur si "
"`need_info_update()` a retourné `true` ;"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:47
msgid ""
"`save_info_update()` is responsible for saving the information filled out by "
"the user (from the `ask_info_update()` form);"
msgstr ""
"`save_info_update()` est chargée de sauvegarder les informations renseignées "
"par l’utilisateur (issues du formulaire de `ask_info_update()`) ;"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:47
msgid ""
"`do_post_update()` is executed at the end of the update and takes into "
"account the code of the new version (e.g., if the new version changes the "
"`Minz_Configuration` object, you will benefit from these improvements)."
msgstr ""
"`do_post_update()` est exécutée à la fin de la mise à jour et prend en "
"compte le code de la nouvelle version (ex. si la nouvelle version modifie "
"l’objet `Minz_Configuration`, vous bénéficierez de ces améliorations)."

#. type: Title ##
#: en/./developers/05_Release_new_version.md:48
#, no-wrap
msgid "Updating the versions file"
msgstr "Mise à jour du fichier de versions"

#. type: Plain text
#: en/./developers/05_Release_new_version.md:51
msgid ""
"Once the script has been written and versioned, it’s necessary to update the "
"`./versions.php' file which contains a mapping table indicating which "
"versions are updated to which other versions."
msgstr ""
"Lorsque le script a été écrit et versionné, il est nécessaire de mettre à "
"jour le fichier `./versions.php` qui contient une table de correspondances "
"indiquant quelles versions sont mises à jour vers quelles autres versions."

#. type: Plain text
#: en/./developers/05_Release_new_version.md:53
msgid "Here’s an example of a `versions.php` file:"
msgstr "Voici un exemple de fichier `versions.php` :"

#. type: Fenced code block (php)
#: en/./developers/05_Release_new_version.md:54
#, no-wrap
msgid ""
"<?php\n"
"return array(\n"
"\t// STABLE\n"
"\t'0.8.0' => '1.0.0',\n"
"\t'0.8.1' => '1.0.0',\n"
"\t'1.0.0' => '1.0.1',  // doesn’t exist (yet)\n"
"\t// DEV\n"
"\t'1.1.2-dev' => 'dev',\n"
"\t'1.1.3-dev' => 'dev',\n"
"\t'1.1.4-dev' => 'dev',\n"
");\n"
msgstr ""
"<?php\n"
"return array(\n"
"\t// STABLE\n"
"\t'0.8.0' => '1.0.0',\n"
"\t'0.8.1' => '1.0.0',\n"
"\t'1.0.0' => '1.0.1',  // doesn’t exist (yet)\n"
"\t// DEV\n"
"\t'1.1.2-dev' => 'dev',\n"
"\t'1.1.3-dev' => 'dev',\n"
"\t'1.1.4-dev' => 'dev',\n"
");\n"

#. type: Plain text
#: en/./developers/05_Release_new_version.md:69
msgid "And here’s how this table works:"
msgstr "Et voici comment fonctionne cette table :"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:75
msgid "on the left you can find the N version, on the right the N+1 version;"
msgstr "à gauche se trouve la version N, à droite la version N+1 ;"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:75
msgid "the `x.y.z.z-dev` versions are **all** updated to `edge`;"
msgstr "les versions `x.y.z-dev` sont **toutes** mises à jour vers `edge` ;"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:75
msgid "stable versions are updated to stable versions;"
msgstr "les versions stables sont mises à jour vers des versions stables ;"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:75
msgid ""
"it’s possible to skip several versions at once, provided that the update "
"scripts support it;"
msgstr ""
"il est possible de sauter plusieurs versions d’un coup à condition que les "
"scripts de mise à jour le prennent en charge ;"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:75
msgid ""
"it’s advisable to indicate the correspondence of the current version to its "
"potential future version by specifying that this version does not yet exist. "
"As long as the corresponding script does not exist, nothing will happen."
msgstr ""
"il est conseillé d’indiquer la correspondance de la version courante vers sa "
"potentielle future version en précisant que cette version n’existe pas "
"encore. Tant que le script correspondant n’existera pas, rien ne se passera."

#. type: Plain text
#: en/./developers/05_Release_new_version.md:77
#, fuzzy
#| msgid ""
#| "It’s **very strongly** recommended to keep this file organized according "
#| "to version numbers by separating stable and dev versions."
msgid ""
"It’s**very strongly** recommended to keep this file organized according to "
"version numbers by separating stable and dev versions."
msgstr ""
"Il est **très fortement** indiqué de garder ce fichier rangé selon les "
"numéros de versions en séparant les versions stables et de dev."

#. type: Title ##
#: en/./developers/05_Release_new_version.md:78
#, no-wrap
msgid "Deployment"
msgstr "Déploiement"

#. type: Plain text
#: en/./developers/05_Release_new_version.md:81
msgid ""
"Before updating update.freshrss.org, it’s better to test with dev.update."
"freshrss.org, which corresponds to pre-production. So update dev.update."
"freshrss.org and change the `FRESHRSS_UPDATE_WEBSITE` URL of your FreshRSS "
"instance. Start the update and check that it’s running correctly."
msgstr ""
"Avant de mettre à jour update.freshrss.org, il est préférable de tester avec "
"dev.update.freshrss.org qui correspond à la pré-production. Mettez donc à "
"jour dev.update.freshrss.org et changez l’URL `FRESHRSS_UPDATE_WEBSITE` de "
"votre instance FreshRSS. Lancez la mise à jour et vérifiez que celle-ci se "
"déroule correctement."

#. type: Plain text
#: en/./developers/05_Release_new_version.md:83
msgid ""
"When you’re satisfied, update update.freshrss.org with the new script, test "
"it again, and then move on."
msgstr ""
"Lorsque vous serez satisfait, mettez à jour update.freshrss.org avec le "
"nouveau script et en testant de nouveau puis passez à la suite."

#. type: Title ##
#: en/./developers/05_Release_new_version.md:84
#, no-wrap
msgid "Updating the FreshRSS services"
msgstr "Mise à jour des services FreshRSS"

#. type: Plain text
#: en/./developers/05_Release_new_version.md:87
msgid "Two services need to be updated immediately after the update."
msgstr ""
"Deux services sont à mettre à jour immédiatement après la mise à jour de "
"update.freshrss.org :"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:90
msgid "rss.freshrss.org;"
msgstr "rss.freshrss.org ;"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:90
msgid "demo.freshrss.org (public login: `demo` / `demodemo`)."
msgstr "demo.freshrss.org (identifiants publics : `demo` / `demodemo`)."

#. type: Title ##
#: en/./developers/05_Release_new_version.md:91
#, no-wrap
msgid "Publicly announce the release"
msgstr "Annoncer publiquement la sortie"

#. type: Plain text
#: en/./developers/05_Release_new_version.md:94
msgid ""
"When everything’s working, it’s time to announce the release to the world!"
msgstr ""
"Lorsque tout fonctionne, il est temps d’annoncer la sortie au monde entier !"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:99
msgid ""
"on GitHub by creating[a new release](https://github.com/FreshRSS/FreshRSS/"
"releases/new)"
msgstr ""
"sur GitHub en créant [une nouvelle release](https://github.com/FreshRSS/"
"FreshRSS/releases/new) ;"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:99
msgid ""
"on the freshrss.org blog, at least for stable versions (write the article "
"on[FreshRSS/freshrss.org](https://github.com/FreshRSS/freshrss.org))"
msgstr ""
"sur le blog de freshrss.org au minimum pour les versions stables (écrire "
"l’article sur [FreshRSS/freshrss.org](https://github.com/FreshRSS/freshrss."
"org))."

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:99
msgid "on Twitter ([@FreshRSS](https://twitter.com/FreshRSS) account)"
msgstr "sur Twitter (compte [@FreshRSS](https://twitter.com/FreshRSS)) ;"

#. type: Bullet: '* '
#: en/./developers/05_Release_new_version.md:99
msgid "<NAME_EMAIL>"
msgstr "<NAME_EMAIL> ;"

#. type: Title ##
#: en/./developers/05_Release_new_version.md:100
#, no-wrap
msgid "Starting the next development version"
msgstr "Lancer la prochaine version de développement"

#. type: Fenced code block (console)
#: en/./developers/05_Release_new_version.md:102
#, no-wrap
msgid ""
"$ git checkout edge\n"
"$ vim constants.php\n"
"# Update the FRESHRSS_VERSION\n"
"$ vim CHANGELOG.md\n"
"# Prepare the changelog for the next version\n"
"$ git add CHANGELOG.md && git commit && git push\n"
msgstr ""
"$ git checkout edge\n"
"$ vim constants.php\n"
"# Mettre à jour le numéro de version de FRESHRSS_VERSION\n"
"$ vim CHANGELOG.md\n"
"# Préparer la section pour la prochaine version\n"
"$ git add CHANGELOG.md && git commit && git push\n"

#. type: Plain text
#: en/./developers/05_Release_new_version.md:111
msgid ""
"Also remember to update update.freshrss.org so that it takes the current "
"development version into account."
msgstr ""
"Pensez aussi à mettre à jour update.freshrss.org pour qu’il prenne en compte "
"la version de développement actuelle."

#. type: Title #
#: en/./developers/06_Fever_API.md:1
#, no-wrap
msgid "FreshRSS - Fever API implementation"
msgstr "FreshRSS - API compatible Fever"

#. type: Plain text
#: en/./developers/06_Fever_API.md:5
msgid ""
"See [Mobile access](../users/06_Mobile_access.md) for general aspects of API "
"access.  Additionally [page about our Google Reader compatible API]"
"(06_GoogleReader_API.md) for another possibility."
msgstr ""

#. type: Title ##
#: en/./developers/06_Fever_API.md:7 en/./developers/06_GoogleReader_API.md:7
#, no-wrap
msgid "RSS clients"
msgstr "Clients compatibles"

#. type: Plain text
#: en/./developers/06_Fever_API.md:12
#, fuzzy
#| msgid ""
#| "There are many RSS clients that support the Fever API, but they seem to "
#| "understand the Fever API a bit differently.  If your favourite client "
#| "doesn’t work properly with this API, please create an issue and we will "
#| "have a look.  But we can **only** do that for free clients."
msgid ""
"There are many RSS clients that support the Fever API, but they seem to "
"understand the Fever API a bit differently.  If your favourite client "
"doesn’t work properly with this API, please create an issue and we’ll have a "
"look.  But we can **only** do that for free clients."
msgstr ""
"De nombreux clients RSS prennent en charge l’API Fever, mais ils semblent "
"comprendre l’API Fever un peu différemment. Si votre client préféré ne "
"fonctionne pas correctement avec cette API, veuiller créer un ticket et nous "
"y jetterons un oeil. Mais nous ne pouvons le faire que pour les clients "
"gratuits."

#. type: Title ##
#: en/./developers/06_Fever_API.md:13 en/./developers/06_GoogleReader_API.md:13
#, no-wrap
msgid "Usage & Authentication"
msgstr "Utilisation et authentification"

#. type: Plain text
#: en/./developers/06_Fever_API.md:17 en/./developers/06_GoogleReader_API.md:17
#, fuzzy
#| msgid ""
#| "Before you can start using this API, you have to enable and setup API "
#| "access, which is [documented here](https://freshrss.github.io/FreshRSS/en/"
#| "users/06_Mobile_access.html), and then reset the user’s API password."
msgid ""
"Before you can start using this API, you have to enable and setup API "
"access, which is [documented here](../users/06_Mobile_access.md), and then "
"reset the user’s API password."
msgstr ""
"Avant de pouvoir commencer à utiliser cette API, vvous devez activer et "
"configurer l’accès à l’API, qui est [documenté ici](https://freshrss.github."
"io/FreshRSS/en/users/06_Mobile_access.html), et réinitialisez ensuite le mot "
"de passe API de l’utilisateur."

#. type: Plain text
#: en/./developers/06_Fever_API.md:19
msgid ""
"Then point your mobile application to the `fever.php` address (e.g. `https://"
"freshrss.example.net/api/fever.php`)."
msgstr ""
"Connectez ensuite votre application mobile en utilisant l’adresse de l’API "
"(e.g. `https://freshrss.example.net/api/fever.php`)."

#. type: Title ##
#: en/./developers/06_Fever_API.md:20 en/./developers/06_GoogleReader_API.md:20
#, no-wrap
msgid "Compatible clients"
msgstr "Clients compatibles"

#. type: Plain text
#: en/./developers/06_Fever_API.md:31
#, no-wrap
msgid ""
"| App                                                                                | Platform            | License                                            |\n"
"|:----------------------------------------------------------------------------------:|:-------------------:|:--------------------------------------------------------:|\n"
"|[Fluent Reader](https://hyliu.me/fluent-reader/)                                    |Windows, Linux, macOS|[BSD-3-Clause](https://github.com/yang991178/fluent-reader/blob/master/LICENSE)|\n"
"|[Fluent Reader lite](https://hyliu.me/fluent-reader-lite/)                          |Android, iOS         |[BSD-3-Clause](https://github.com/yang991178/fluent-reader-lite)|\n"
"|[Fiery Feeds](https://apps.apple.com/app/fiery-feeds-rss-reader/id1158763303)       |iOS                  |Closed Source                                             |\n"
"|[Newsflash](https://gitlab.com/news-flash/news_flash_gtk/)                          |Linux                |[GPLv3](https://gitlab.com/news-flash/news_flash_gtk/)|\n"
"|[Unread](https://apps.apple.com/app/unread-rss-reader/id1252376153)                 |iOS                  |Closed Source                                             |\n"
"|[Reeder Classic](https://www.reederapp.com/classic/)                                |iOS                  |Closed Source                                              |\n"
"|[ReadKit](https://apps.apple.com/app/readkit/id588726889)                           |macOS                |Closed Source                                              |\n"
msgstr ""

#. type: Title ##
#: en/./developers/06_Fever_API.md:32 en/./index.md:7
#, no-wrap
msgid "Features"
msgstr "Fonctionnalités"

#. type: Plain text
#: en/./developers/06_Fever_API.md:35
msgid "The following features are implemented:"
msgstr "Les fonctionnalités suivantes sont implémentées :"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:45
msgid "fetching categories"
msgstr "récupération des catégories"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:45
msgid "fetching feeds"
msgstr "récupération des flux"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:45
msgid ""
"fetching RSS items (new, favorites, unread, by_id, by_feed, by_category, "
"since)"
msgstr ""
"récupération des entrées (new, favorites, unread, by_id, by_feed, "
"by_category,since)"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:45
msgid "fetching favicons"
msgstr "récupération des favicons"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:45
msgid "setting read marker for item(s)"
msgstr "marquage des entrées comme lues"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:45
msgid "setting starred marker for item(s)"
msgstr "marquage des entrées comme favoris"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:45
msgid "setting read marker for feed"
msgstr "marquage d’un flux comme lu"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:45
msgid "setting read marker for category"
msgstr "marquage d’une catégorie comme lue"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:45
msgid "supports FreshRSS extensions, which use the `entry_before_display` hook"
msgstr "support des extensions grace au hook `entry_before_display`"

#. type: Plain text
#: en/./developers/06_Fever_API.md:47
msgid "The following features are not supported:"
msgstr "Les fonctionnalités suivantes ne sont pas implémentées :"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:49
msgid ""
"**Hot Links** aka **hot** as there is nothing in FreshRSS yet that is "
"similar or could be used to simulate it."
msgstr ""
"« Hot Links » car il n’y a encore rien dans FreshRSS qui soit similaire ou "
"qui puisse être utilisé pour le simuler."

#. type: Title ##
#: en/./developers/06_Fever_API.md:50
#, no-wrap
msgid "Testing and debugging"
msgstr "Tester et déboguer"

#. type: Plain text
#: en/./developers/06_Fever_API.md:53
#, fuzzy
#| msgid ""
#| "If this API doesn’t work as expected in your RSS reader, you can test it "
#| "manually with a tool like [Postman](https://www.getpostman.com/)."
msgid ""
"If this API does not work as expected in your RSS reader, you can test it "
"manually with a tool like [Postman](https://www.getpostman.com/)."
msgstr ""
"Si l’API ne fonctionne pas comme attendu dans votre lecteur, il est possible "
"de la tester manuellement avec un outil tel que [Postman](https://www."
"getpostman.com/)."

#. type: Plain text
#: en/./developers/06_Fever_API.md:55
#, fuzzy
#| msgid ""
#| "Configure a POST request to the URL https://freshrss.example.net/api/"
#| "fever.php?api which should give you the result:"
msgid ""
"Configure a POST request to the URL <https://freshrss.example.net/api/fever."
"php?api> which should give you the result:"
msgstr ""
"Envoyer une requête POST à l’adresse https://freshrss.example.net/api/fever."
"php?api devrait vous renvoyer le résultat suivant :"

#. type: Fenced code block (json)
#: en/./developers/06_Fever_API.md:55
#, no-wrap
msgid ""
"{\n"
"\t\"api_version\": 3,\n"
"\t\"auth\": 0\n"
"}\n"
msgstr ""
"{\n"
"\t\"api_version\": 3,\n"
"\t\"auth\": 0\n"
"}\n"

#. type: Plain text
#: en/./developers/06_Fever_API.md:62
msgid "Great, the base setup seems to work!"
msgstr "Super, la configuration de base fonctionne !"

#. type: Plain text
#: en/./developers/06_Fever_API.md:65
msgid ""
"Now lets try an authenticated call. Fever uses an `api_key`, which is the "
"MD5 hash of `\"$username:$apiPassword\"`.  Assuming the user is `kevin` and "
"the password `freshrss`, here is a command-line example to compute the "
"resulting `api_key`"
msgstr ""
"Maintenant essayons de faire un appel authentifié. Fever utilise un "
"paramètre `api_key` qui contient le résultat de la fonction de hachage MD5 "
"de la valeur `\"$username:$apiPassword\"`. En considérant que l’utilisateur "
"est `kevin` et que son mot de passe est `freshrss`, voici la commande à "
"lancer pour calculer la valeur du paramètre `api_key` :"

#. type: Fenced code block (sh)
#: en/./developers/06_Fever_API.md:66
#, no-wrap
msgid "api_key=`echo -n \"kevin:freshrss\" | md5sum | cut -d' ' -f1`\n"
msgstr "api_key=`echo -n \"kevin:freshrss\" | md5sum | cut -d' ' -f1`\n"

#. type: Plain text
#: en/./developers/06_Fever_API.md:71
msgid ""
"Add a body to your POST request encoded as `form-data` and one key named "
"`api_key` with the value `your-password-hash`:"
msgstr ""
"Ajoutez un contenu sous forme de `form-data`à votre requête POST ainsi que "
"le paramètre `api_key` contenant la valeur calculée à l’étape précédente :"

#. type: Fenced code block (sh)
#: en/./developers/06_Fever_API.md:72
#, no-wrap
msgid "curl -s -F \"api_key=$api_key\" 'https://freshrss.example.net/api/fever.php?api'\n"
msgstr "curl -s -F \"api_key=$api_key\" 'https://freshrss.exemple.net/api/fever.php?api'\n"

#. type: Plain text
#: en/./developers/06_Fever_API.md:77
msgid "This should give:"
msgstr "Vous devriez obtenir le résultat suivant :"

#. type: Fenced code block (json)
#: en/./developers/06_Fever_API.md:77
#, no-wrap
msgid ""
"{\n"
"\t\"api_version\": 3,\n"
"\t\"auth\": 1,\n"
"\t\"last_refreshed_on_time\": \"1520013061\"\n"
"}\n"
msgstr ""
"{\n"
"\t\"api_version\": 3,\n"
"\t\"auth\": 1,\n"
"\t\"last_refreshed_on_time\": \"1520013061\"\n"
"}\n"

#. type: Plain text
#: en/./developers/06_Fever_API.md:85
msgid ""
"Perfect, you’re now authenticated and you can start testing the more "
"advanced features. To do so, change the URL and append the possible API "
"actions to your request parameters. Please refer to the [original Fever "
"documentation](https://feedafever.com/api) for more information."
msgstr ""
"Parfait, maintenant vous êtes autentifié et vous pouvez commencer à tester "
"les fonctions avancées. Pour cela, il suffit de changer l’adresse en lui "
"ajoutant les paramètres nécessaires à la réalisation des actions supportées. "
"Pour plus d’information, veuillez vous référer à la [documentation "
"officielle de Fever](https://feedafever.com/api)."

#. type: Plain text
#: en/./developers/06_Fever_API.md:87
msgid "Some basic calls are:"
msgstr "Voici quelques exemples simples d’appels réalisables :"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:97
#, fuzzy
#| msgid "https://freshrss.example.net/api/fever.php?api&items"
msgid "<https://freshrss.example.net/api/fever.php?api&items>"
msgstr "https://freshrss.example.net/api/fever.php?api&items"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:97
#, fuzzy
#| msgid "https://freshrss.example.net/api/fever.php?api&feeds"
msgid "<https://freshrss.example.net/api/fever.php?api&feeds>"
msgstr "https://freshrss.example.net/api/fever.php?api&feeds"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:97
#, fuzzy
#| msgid "https://freshrss.example.net/api/fever.php?api&groups"
msgid "<https://freshrss.example.net/api/fever.php?api&groups>"
msgstr "https://freshrss.example.net/api/fever.php?api&groups"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:97
#, fuzzy
#| msgid "https://freshrss.example.net/api/fever.php?api&unread_item_ids"
msgid "<https://freshrss.example.net/api/fever.php?api&unread_item_ids>"
msgstr "https://freshrss.example.net/api/fever.php?api&unread_item_ids"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:97
#, fuzzy
#| msgid "https://freshrss.example.net/api/fever.php?api&saved_item_ids"
msgid "<https://freshrss.example.net/api/fever.php?api&saved_item_ids>"
msgstr "https://freshrss.example.net/api/fever.php?api&saved_item_ids"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:97
#, fuzzy
#| msgid ""
#| "https://freshrss.example.net/api/fever.php?api&items&since_id=some_id"
msgid "<https://freshrss.example.net/api/fever.php?api&items&since_id=some_id>"
msgstr "https://freshrss.example.net/api/fever.php?api&items&since_id=some_id"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:97
#, fuzzy
#| msgid "https://freshrss.example.net/api/fever.php?api&items&max_id=some_id"
msgid "<https://freshrss.example.net/api/fever.php?api&items&max_id=some_id>"
msgstr "https://freshrss.example.net/api/fever.php?api&items&max_id=some_id"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:97
#, fuzzy
#| msgid ""
#| "https://freshrss.example.net/api/fever.php?"
#| "api&mark=item&as=read&id=some_id"
msgid ""
"<https://freshrss.example.net/api/fever.php?api&mark=item&as=read&id=some_id>"
msgstr ""
"https://freshrss.example.net/api/fever.php?api&mark=item&as=read&id=some_id"

#. type: Bullet: '* '
#: en/./developers/06_Fever_API.md:97
#, fuzzy
#| msgid ""
#| "https://freshrss.example.net/api/fever.php?"
#| "api&mark=item&as=unread&id=some_id"
msgid ""
"<https://freshrss.example.net/api/fever.php?"
"api&mark=item&as=unread&id=some_id>"
msgstr ""
"https://freshrss.example.net/api/fever.php?api&mark=item&as=unread&id=some_id"

#. type: Plain text
#: en/./developers/06_Fever_API.md:99
msgid ""
"Replace `some_id` with a real ID from your `freshrss_username_entry` "
"database."
msgstr ""
"Remplacez `some_id` par un identifiant réel de votre base de données "
"`freshrss_username_entry`."

#. type: Title ###
#: en/./developers/06_Fever_API.md:100
#, no-wrap
msgid "Debugging"
msgstr "Déboguer"

#. type: Plain text
#: en/./developers/06_Fever_API.md:103
msgid ""
"If nothing helps and your client is still misbehaving, you can add the "
"following lines to the beginning of the `fever.api` file to determine the "
"cause of the problems:"
msgstr ""
"Si rien ne fonctionne correctement et que votre client se comporte "
"étrangement, vous pouvez ajouter les quelques lignes suivantes au début du "
"fichier `fever.api` pour déterminer la cause des problèmes rencontrés :"

#. type: Fenced code block (php)
#: en/./developers/06_Fever_API.md:104
#, no-wrap
msgid "file_put_contents(__DIR__ . '/fever.log', $_SERVER['HTTP_USER_AGENT'] . ': ' . json_encode($_REQUEST) . PHP_EOL, FILE_APPEND);\n"
msgstr "file_put_contents(__DIR__ . '/fever.log', $_SERVER['HTTP_USER_AGENT'] . ': ' . json_encode($_REQUEST) . PHP_EOL, FILE_APPEND);\n"

#. type: Plain text
#: en/./developers/06_Fever_API.md:109
msgid ""
"Then use your RSS client to query the API and afterwards check the file "
"`fever.log`."
msgstr ""
"Utilisez ensuite votre client RSS pour interroger l’API et vérifier le "
"fichier `fever.log`."

#. type: Title ##
#: en/./developers/06_Fever_API.md:110
#, no-wrap
msgid "Credits"
msgstr "Remerciements"

#. type: Plain text
#: en/./developers/06_Fever_API.md:112
msgid ""
"This plugin was inspired by the [tinytinyrss-fever-plugin](https://github."
"com/dasmurphy/tinytinyrss-fever-plugin)."
msgstr ""
"Ce plugin a été inspiré par le [tinytinyrss-fever-plugin](https://github.com/"
"dasmurphy/tinytinyrss-fever-plugin)."

#. type: Title #
#: en/./developers/06_GoogleReader_API.md:1
#, fuzzy, no-wrap
#| msgid "FreshRSS - Fever API implementation"
msgid "FreshRSS - Google Reader compatible API implementation"
msgstr "FreshRSS - API compatible Fever"

#. type: Plain text
#: en/./developers/06_GoogleReader_API.md:4
#, fuzzy
#| msgid ""
#| "See the [page about our Google Reader compatible API](06_Mobile_access."
#| "md) for another possibility and general aspects of API access."
msgid ""
"See [Mobile access](../users/06_Mobile_access.md) for general aspects of API "
"access."
msgstr ""
"Voir la page [sur notre API compatible Google Reader](06_Mobile_access.md) "
"pour une autre possibilité et des généralités sur l’accès par API."

#. type: Plain text
#: en/./developers/06_GoogleReader_API.md:6
#, fuzzy
#| msgid ""
#| "See the [page about our Google Reader compatible API](06_Mobile_access."
#| "md) for another possibility and general aspects of API access."
msgid ""
"See also the [page about our Fever compatible API](06_Fever_API.md) for "
"another possibility (less powerful)."
msgstr ""
"Voir la page [sur notre API compatible Google Reader](06_Mobile_access.md) "
"pour une autre possibilité et des généralités sur l’accès par API."

#. type: Plain text
#: en/./developers/06_GoogleReader_API.md:12
#, fuzzy
#| msgid ""
#| "There are many RSS clients that support the Fever API, but they seem to "
#| "understand the Fever API a bit differently.  If your favourite client "
#| "doesn’t work properly with this API, please create an issue and we will "
#| "have a look.  But we can **only** do that for free clients."
msgid ""
"There are many RSS clients that support the Fever API, but they might "
"understand the API a bit differently.  If your favourite client doesn’t work "
"properly with this API, please create an issue and we’ll have a look.  But "
"we can **only** do that for free clients."
msgstr ""
"De nombreux clients RSS prennent en charge l’API Fever, mais ils semblent "
"comprendre l’API Fever un peu différemment. Si votre client préféré ne "
"fonctionne pas correctement avec cette API, veuiller créer un ticket et nous "
"y jetterons un oeil. Mais nous ne pouvons le faire que pour les clients "
"gratuits."

#. type: Plain text
#: en/./developers/06_GoogleReader_API.md:19
#, fuzzy
#| msgid ""
#| "Then point your mobile application to the `fever.php` address (e.g. "
#| "`https://freshrss.example.net/api/fever.php`)."
msgid ""
"Then point your mobile application to the `greader.php` address (e.g. "
"`https://freshrss.example.net/api/greader.php`)."
msgstr ""
"Connectez ensuite votre application mobile en utilisant l’adresse de l’API "
"(e.g. `https://freshrss.example.net/api/fever.php`)."

#. type: Bullet: '1. '
#: en/./developers/06_GoogleReader_API.md:24
#, fuzzy
#| msgid ""
#| "6. On the same FreshRSS API page, note the address given under “Your API "
#| "address”, like `https://freshrss.example.net/api/greader.php`\n"
#| "\t* Type the API address in a client, together with your FreshRSS "
#| "username, and the corresponding special API password.\n"
msgid ""
"On the same FreshRSS API page, note the address given under “Your API "
"address”, like `https://freshrss.example.net/api/greader.php`"
msgstr ""
"6. Sur la même page de l’API FreshRSS, notez l’adresse donnée sous \"Votre "
"adresse API\", comme `https://freshrss.example.net/api/greader.php`\n"
"\t* Saisissez l’adresse de l’API dans le client sélectionné puis votre nom "
"d’utilisateur et votre mot de passe spécialement créé pour l’API.\n"

#. type: Bullet: '2. '
#: en/./developers/06_GoogleReader_API.md:24
#, fuzzy
#| msgid ""
#| "6. On the same FreshRSS API page, note the address given under “Your API "
#| "address”, like `https://freshrss.example.net/api/greader.php`\n"
#| "\t* Type the API address in a client, together with your FreshRSS "
#| "username, and the corresponding special API password.\n"
msgid ""
"Type the API address in a client, together with your FreshRSS username, and "
"the corresponding special API password."
msgstr ""
"6. Sur la même page de l’API FreshRSS, notez l’adresse donnée sous \"Votre "
"adresse API\", comme `https://freshrss.example.net/api/greader.php`\n"
"\t* Saisissez l’adresse de l’API dans le client sélectionné puis votre nom "
"d’utilisateur et votre mot de passe spécialement créé pour l’API.\n"

#. type: Plain text
#: en/./developers/06_GoogleReader_API.md:39
#, no-wrap
msgid ""
"| App                                                                                | Platform            | License                                            |\n"
"|:----------------------------------------------------------------------------------:|:-------------------:|:--------------------------------------------------------:|\n"
"|[News+](https://github.com/noinnion/newsplus/blob/master/apk/NewsPlus_202.apk) with [News+ Google Reader extension](https://github.com/noinnion/newsplus/blob/master/apk/GoogleReaderCloneExtension_101.apk) |Android|Closed Source (Free), [partially open source](https://github.com/noinnion/newsplus/blob/master/extensions/GoogleReaderCloneExtension/src/com/noinnion/android/newsplus/extension/google_reader/GoogleReaderClient.java)|\n"
"|[FeedMe 3.5.3+](https://play.google.com/store/apps/details?id=com.seazon.feedme) |Android                  |Closed Source (Free)                                             |\n"
"|[EasyRSS](https://github.com/Alkarex/EasyRSS)                          |Android                |[GPLv3](https://github.com/Alkarex/EasyRSS/blob/master/license.txt) ([F-Droid](https://f-droid.org/packages/org.freshrss.easyrss/))|\n"
"|[Readrops](https://github.com/readrops/Readrops) |Android                  |[GPLv3](https://github.com/readrops/Readrops/blob/develop/LICENSE)                                             |\n"
"|[Fluent Reader Lite](https://hyliu.me/fluent-reader-lite/) |Android, iOS            |[BSD-3](https://github.com/yang991178/fluent-reader-lite)                                             |\n"
"|[FocusReader](https://play.google.com/store/apps/details?id=allen.town.focus.reader) |Android                  |Closed Source(Free)                                              |\n"
"|[Newsflash](https://gitlab.com/news-flash/news_flash_gtk/)                          |Linux                |[GPLv3](https://gitlab.com/news-flash/news_flash_gtk/) |\n"
"|[lire](https://lireapp.com/)                                                        |iOS, macOS           |Closed Source                                             |\n"
"|[Newsboat 2.24+](https://newsboat.org/)                           |Linux                |[MIT](https://github.com/newsboat/newsboat/blob/master/LICENSE)                                              |\n"
"|[Vienna RSS](http://www.vienna-rss.com/)                           |macOS                |[Apache-2.0](https://github.com/ViennaRSS/vienna-rss/blob/master/LICENCE.md)                                              |\n"
"|[Reeder Classic](https://www.reederapp.com/classic/)           |macOS, iOS                |Closed Source                                              |\n"
"|[FreshRSS-Notify](https://addons.mozilla.org/firefox/addon/freshrss-notify-webextension/)                           |Firefox                |Open Source                                              |\n"
msgstr ""

#. type: Plain text
#: en/./developers/06_GoogleReader_API.md:41
#, no-wrap
msgid "> ℹ️ See a [better table of compatible clients in our main Readme](https://github.com/FreshRSS/FreshRSS/blob/edge/README.md#apis--native-apps).\n"
msgstr ""

#. type: Title ##
#: en/./developers/06_GoogleReader_API.md:42
#, no-wrap
msgid "Google Reader compatible API"
msgstr "API compatible Google Reader"

#. type: Plain text
#: en/./developers/06_GoogleReader_API.md:45
msgid "Examples of basic queries:"
msgstr "Exemples de requêtes simples :"

#. type: Fenced code block (sh)
#: en/./developers/06_GoogleReader_API.md:46
#, no-wrap
msgid ""
"# Initial login, using API password (Email and Passwd can be given either as GET, or POST - better)\n"
"curl 'https://freshrss.example.net/api/greader.php/accounts/ClientLogin?Email=alice&Passwd=Abcdef123456'\n"
"SID=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\n"
"Auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\n"
"\n"
"# Examples of read-only requests\n"
"curl -s -H \"Authorization:GoogleLogin auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\" \\\n"
"  'https://freshrss.example.net/api/greader.php/reader/api/0/subscription/list?output=json'\n"
"\n"
"curl -s -H \"Authorization:GoogleLogin auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\" \\\n"
"  'https://freshrss.example.net/api/greader.php/reader/api/0/unread-count?output=json'\n"
"\n"
"curl -s -H \"Authorization:GoogleLogin auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\" \\\n"
"  'https://freshrss.example.net/api/greader.php/reader/api/0/tag/list?output=json'\n"
"\n"
"# Retrieve a token for requests making modifications\n"
"curl -H \"Authorization:GoogleLogin auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\" \\\n"
"  'https://freshrss.example.net/api/greader.php/reader/api/0/token'\n"
"8e6845e089457af25303abc6f53356eb60bdb5f8ZZZZZZZZZZZZZZZZZ\n"
"\n"
"# Get articles, piped to jq for easier JSON reading\n"
"curl -s -H \"Authorization:GoogleLogin auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\" \\\n"
"  'https://freshrss.example.net/api/greader.php/reader/api/0/stream/contents/reading-list' | jq .\n"
msgstr ""
"# Authentification utilisant le mot de passe API (Email et Passwd peuvent être passés en GET, ou POST - mieux)\n"
"curl 'https://freshrss.example.net/api/greader.php/accounts/ClientLogin?Email=alice&Passwd=Abcdef123456'\n"
"SID=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\n"
"Auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\n"
"\n"
"# Exemples de requêtes en lecture\n"
"curl -s -H \"Authorization:GoogleLogin auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\" \\\n"
"  'https://freshrss.example.net/api/greader.php/reader/api/0/subscription/list?output=json'\n"
"\n"
"curl -s -H \"Authorization:GoogleLogin auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\" \\\n"
"  'https://freshrss.example.net/api/greader.php/reader/api/0/unread-count?output=json'\n"
"\n"
"curl -s -H \"Authorization:GoogleLogin auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\" \\\n"
"  'https://freshrss.example.net/api/greader.php/reader/api/0/tag/list?output=json'\n"
"\n"
"# Demande de jeton pour faire de requêtes de modification\n"
"curl -H \"Authorization:GoogleLogin auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\" \\\n"
"  'https://freshrss.example.net/api/greader.php/reader/api/0/token'\n"
"8e6845e089457af25303abc6f53356eb60bdb5f8ZZZZZZZZZZZZZZZZZ\n"
"\n"
"# Récupère les articles, envoyés à jq pour une lecture JSON plus facile\n"
"curl -s -H \"Authorization:GoogleLogin auth=alice/8e6845e089457af25303abc6f53356eb60bdb5f8\" \\\n"
"  'https://freshrss.example.net/api/greader.php/reader/api/0/stream/contents/reading-list' | jq .\n"

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:73
#, fuzzy
#| msgid ""
#| "[Go to the bug ticket manager](https://github.com/FreshRSS/FreshRSS/"
#| "issues)"
msgid ""
"[Source code of our API implementation](https://github.com/FreshRSS/FreshRSS/"
"blob/edge/p/api/greader.php)"
msgstr ""
"[Rendez-vous sur le gestionnaire de tickets de bugs](https://github.com/"
"FreshRSS/FreshRSS/issues)"

#. type: Title ###
#: en/./developers/06_GoogleReader_API.md:74
#, no-wrap
msgid "API documentation from the original Google Reader"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:81
msgid ""
"[By Daniel Arowser](https://web.archive.org/web/20130710044440/http://undoc."
"in/api.html) ([source](https://github.com/arowser/google-reader-api))"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:81
msgid ""
"[By Martin Doms](https://web.archive.org/web/20210126115837/https://blog."
"martindoms.com/2009/10/16/using-the-google-reader-api-part-2/)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:81
msgid ""
"[By Nick Bradbury](https://inessential.com/2013/03/14/"
"google_reader_api_documentation)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:81
msgid ""
"[By Niall Kennedy](https://web.archive.org/web/20170426184845/http://www."
"niallkennedy.com/blog/2005/12/google-reader-api.html)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:81
msgid ""
"[By Mihai Parparita](https://web.archive.org/web/20140919042419/http://code."
"google.com/p/google-reader-api/w/list) ([source](https://github.com/mihaip/"
"google-reader-api))"
msgstr ""

#. type: Title ###
#: en/./developers/06_GoogleReader_API.md:82
#, no-wrap
msgid "API documentation from other compatible clients"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:89
msgid "[FeedHQ](https://feedhq.readthedocs.io/en/latest/api/index.html)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:89
msgid "[Inoreader](https://www.inoreader.com/developers/)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:89
msgid "[The Old Reader](https://github.com/theoldreader/api)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:89
msgid "[pyrfeed](http://code.google.com/p/pyrfeed/wiki/GoogleReaderAPI)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:89
msgid "[BazQux](https://github.com/bazqux/bazqux-api)"
msgstr ""

#. type: Title ###
#: en/./developers/06_GoogleReader_API.md:90
#, no-wrap
msgid "Synchronisation strategy"
msgstr ""

#. type: Plain text
#: en/./developers/06_GoogleReader_API.md:94
#, no-wrap
msgid ""
"> ℹ️ If you are maintaining a client or planning to develop a new one, please read carefully the following pieces of advice,\n"
"as many clients start by having a very inneficient synchronisation strategy.\n"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:96
#, fuzzy
#| msgid ""
#| "If your request is new, [open a new bug ticket](https://github.com/"
#| "FreshRSS/FreshRSS/issues/new)"
msgid ""
"[*Synchronisation recommendation* by Alkarex](https://github.com/FreshRSS/"
"FreshRSS/issues/2566#issuecomment-541317776)"
msgstr ""
"Si votre demande est nouvelle, [ouvrez un nouveau ticket de bug](https://"
"github.com/FreshRSS/FreshRSS/issues/new)"

#. type: Bullet: '* '
#: en/./developers/06_GoogleReader_API.md:96
msgid ""
"[*The Right Way to Sync* by BazQux](https://github.com/bazqux/bazqux-"
"api#user-content-the-right-way-to-sync)"
msgstr ""

#. type: Title #
#: en/./developers/06_Reporting_Bugs.md:1
#, no-wrap
msgid "Reporting a bug or a suggestion"
msgstr "Remonter un problème ou une suggestion"

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:4
#, fuzzy
#| msgid ""
#| "Despite the care given to FreshRSS, it’s still possible that bugs occur. "
#| "The project is young and development is dynamic, so it can be corrected "
#| "quickly. You might also have a feature in mind that doesn’t yet exist. "
#| "Regardless whether your idea seems silly, far-fetched, useless or too "
#| "specific, please don’t hesitate to propose it to us! “Ideas in the air” "
#| "often find an attentive ear. It’s new external perspectives that make the "
#| "project evolve the most."
msgid ""
"Despite the care given to FreshRSS, it’s still possible that bugs occur. "
"Development is dynamic, so issues can be corrected quickly. You might also "
"have a feature in mind that doesn’t yet exist. Regardless whether your idea "
"seems silly, far-fetched, useless or too specific, please don’t hesitate to "
"propose it to us! “Ideas in the air” often find an attentive ear. It’s new "
"external perspectives that make the project evolve the most."
msgstr ""
"Malgré le soin apporté à FreshRSS, il se peut que des bugs apparaissent "
"encore. Le projet est jeune et le développement dynamique, aussi celui-ci "
"pourra être corrigé rapidement. Il se peut aussi que vous ayez en tête une "
"fonctionnalité qui n’existe pas encore. Que celle-ci vous paraisse idiote, "
"farfelue, inutile ou trop spécifique, il ne faut surtout pas hésiter à nous "
"la proposer ! Très souvent des « idées en l’air » ont trouvé une oreille "
"attentive. Ce sont les regards externes qui font le plus évoluer le projet."

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:6
msgid ""
"If you’re convinced that you should be heard, here’s how you can go about it."
msgstr ""
"Si vous êtes convaincus qu’il faut vous faire entendre, voici la marche à "
"suivre."

#. type: Title ##
#: en/./developers/06_Reporting_Bugs.md:7
#, no-wrap
msgid "On GitHub"
msgstr "Sur GitHub"

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:10
msgid ""
"GitHub is the ideal platform to submit your requests. It allows us to "
"discuss a problem or suggestion with others and it often generates new "
"ideas. Let’s not neglect this “social” aspect!"
msgstr ""
"GitHub est la plate-forme à privilégier pour vos demandes. En effet, cela "
"nous permet de pouvoir discuter à plusieurs sur un problème ou une "
"suggestion et de faire émerger, souvent, des idées nouvelles. Ne négligeons "
"pas cet aspect « social » !"

#. type: Bullet: '1. '
#: en/./developers/06_Reporting_Bugs.md:16
msgid ""
"[Go to the bug ticket manager](https://github.com/FreshRSS/FreshRSS/issues)"
msgstr ""
"[Rendez-vous sur le gestionnaire de tickets de bugs](https://github.com/"
"FreshRSS/FreshRSS/issues)"

#. type: Bullet: '2. '
#: en/./developers/06_Reporting_Bugs.md:16
msgid ""
"Start by checking if a similar request hasn’t already been made. If so, "
"please feel free to add your voice to the request."
msgstr ""
"Commencez par rechercher si une demande similaire n’a pas déjà été faite. Si "
"oui, n’hésitez pas à ajouter votre voix à la demande."

#. type: Bullet: '3. '
#: en/./developers/06_Reporting_Bugs.md:16
msgid ""
"If your request is new, [open a new bug ticket](https://github.com/FreshRSS/"
"FreshRSS/issues/new)"
msgstr ""
"Si votre demande est nouvelle, [ouvrez un nouveau ticket de bug](https://"
"github.com/FreshRSS/FreshRSS/issues/new)"

#. type: Bullet: '4. '
#: en/./developers/06_Reporting_Bugs.md:16
msgid ""
"Finally, write your request. If you’re fluent in English, it’s the preferred "
"language because it allows for discussion with the largest number of people."
msgstr ""
"Rédigez enfin votre demande. Si vous maitrisez l’anglais, c’est la langue à "
"privilégier car cela permet d’ouvrir la discussion à un plus grand nombre de "
"personnes. Sinon, ce n’est pas grave, continuez en français :)"

#. type: Bullet: '5. '
#: en/./developers/06_Reporting_Bugs.md:16
msgid ""
"Please follow the tips below to make it easier to let your ticket be heard."
msgstr ""
"Merci de bien vouloir suivre les quelques conseils donnés plus bas pour "
"faciliter la prise en compte de votre ticket."

#. type: Title ##
#: en/./developers/06_Reporting_Bugs.md:17
#, no-wrap
msgid "Informal"
msgstr "De façon informelle"

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:20
msgid ""
"Not everyone likes or uses GitHub for a variety of legitimate reasons. That "
"is why you can also contact us in a more informal way."
msgstr ""
"Tout le monde n’aime pas ou n’utilise pas GitHub pour des raisons aussi "
"diverses que légitimes. C’est pourquoi vous pouvez aussi nous contacter de "
"façon plus informelle."

#. type: Bullet: '* '
#: en/./developers/06_Reporting_Bugs.md:26
msgid ""
"On [our Mattermost chat](https://framateam.org/signup_user_complete/?"
"id=e2680d3e3128b9fac8fdb3003b0024ee)"
msgstr ""
"Sur [notre chat Mattermost](https://framateam.org/signup_user_complete/?"
"id=e2680d3e3128b9fac8fdb3003b0024ee)"

#. type: Bullet: '* '
#: en/./developers/06_Reporting_Bugs.md:26
msgid "On [our subreddit](https://www.reddit.com/r/freshrss/)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/06_Reporting_Bugs.md:26
msgid "At events / meetings around Free Software"
msgstr "À des évènements / rencontres autour du Logiciel Libre"

#. type: Bullet: '* '
#: en/./developers/06_Reporting_Bugs.md:26
msgid "Over a beer in a bar"
msgstr "Autour d’une bière dans un bar"

#. type: Bullet: '* '
#: en/./developers/06_Reporting_Bugs.md:26
msgid "Etc."
msgstr "Etc."

#. type: Title ##
#: en/./developers/06_Reporting_Bugs.md:27
#, no-wrap
msgid "Tips"
msgstr "Conseils"

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:30
msgid "Here are some tips to help you present your bug report or suggestion:"
msgstr ""
"Voici quelques conseils pour bien présenter votre remontée de bug ou votre "
"suggestion :"

#. type: Bullet: '* '
#: en/./developers/06_Reporting_Bugs.md:41
msgid ""
"**Pay attention to spelling**. Even if it’s not always easy, try your best!"
msgstr ""
"**Faites attention à l’orthographe.** même si ce n’est pas toujours facile, "
"faites votre maximum. ;)"

#. type: Bullet: '* '
#: en/./developers/06_Reporting_Bugs.md:41
msgid ""
"**Give an explicit title to your request**, even if it’s a bit long. This "
"not only helps us understand your request, but also to find your ticket "
"later."
msgstr ""
"**Donnez un titre explicite à votre demande**, quitte à ce qu’il soit un peu "
"long. Cela nous aide non seulement à comprendre votre demande, mais aussi à "
"retrouver votre ticket plus tard."

#. type: Bullet: '* '
#: en/./developers/06_Reporting_Bugs.md:41
msgid ""
"**One request = one ticket.** You may have lots of ideas while being afraid "
"to spam the bug manager: it doesn’t matter. It’s better to have a few too "
"many tickets than too many requests in one. We’ll close and consolidate "
"requests when possible."
msgstr ""
"**Une demande = un ticket.** Vous pouvez avoir des tas d’idées mais vous "
"avez peur de spammer le gestionnaire de bugs : ça ne fait rien. Il vaut "
"mieux avoir un peu trop de tickets que trop de demandes dans un seul. On "
"s’occupera de fermer et regrouper les demandes qui le peuvent."

#. type: Bullet: '* '
#: en/./developers/06_Reporting_Bugs.md:41
msgid ""
"If you report a bug, think about **providing us with the FreshRSS logs** "
"(accessible in the FreshRSS `data/log/` folder) and the **PHP logs** (the "
"location may vary by distribution, but consider searching in `/var/log/"
"httpd` or `/var/log/apache`)."
msgstr ""
"Si vous remontez un bug, pensez à nous **fournir les logs de FreshRSS** "
"(accessibles dans les dossier ''data/log/'' de FreshRSS) **et PHP** "
"(l’emplacement peut varier selon les distributions, mais pensez à chercher "
"dans ''/var/log/httpd'' ou ''/var/log/apache'')."

#. type: Bullet: '  * '
#: en/./developers/06_Reporting_Bugs.md:41
msgid ""
"If you can’t find the log files, specify it in your ticket so we know you’ve "
"already searched."
msgstr ""
"Si vous ne trouvez pas les fichiers de logs, précisez-le dans votre ticket "
"afin que nous sachions que vous avez déjà cherché."

#. type: Bullet: '  * '
#: en/./developers/06_Reporting_Bugs.md:41
msgid ""
"Not all bugs require logs, but if you have any doubts, it is better to "
"provide them to us. Logs are important and very useful for debugging!"
msgstr ""
"Tous les bugs ne nécessitent pas les logs, mais si vous doutez, mieux vaut "
"nous les fournir. Les logs sont importants et très utiles pour débugguer !"

#. type: Bullet: '  * '
#: en/./developers/06_Reporting_Bugs.md:41
msgid ""
"The logs may reveal confidential information, so **be careful not to "
"disclose anything sensitive.**"
msgstr ""
"Il se peut que les logs puissent révéler des informations plus ou moins "
"confidentielles, **faites attention à ne rien divulguer de sensible.**"

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:41
#, no-wrap
msgid ""
"* If you report a feed problem, it will be easier if you could provide a snapshot of its content in a text file.\n"
"See [here](#how-to-provide-feed-data) for more information.\n"
msgstr ""

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:43
msgid ""
"In addition, when facing a bug, you’re encouraged to follow this message "
"format (from the [Sam & Max website](http://sametmax.com/template-de-demande-"
"daide-en-informatique/):"
msgstr ""
"De plus, face à un bug, je ne peux que vous encourager à suivre le format de "
"message suivant (tiré du [site de Sam & Max](http://sametmax.com/template-de-"
"demande-daide-en-informatique/)) :"

#. type: Title ###
#: en/./developers/06_Reporting_Bugs.md:44
#, no-wrap
msgid "What’s my goal?"
msgstr "Quel est mon objectif ?"

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:47
msgid "Give the general context of what you were trying to do."
msgstr "Donnez le contexte général de ce que vous essayiez de faire."

#. type: Title ###
#: en/./developers/06_Reporting_Bugs.md:48
#, no-wrap
msgid "What have I been trying to do?"
msgstr "Qu’est-ce que j’ai essayé de faire ?"

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:51
msgid ""
"Explain step by step what you have done so that we can reproduce the bug."
msgstr ""
"Expliquez pas à pas ce que vous avez fait afin que nous puissions reproduire "
"le bug."

#. type: Title ###
#: en/./developers/06_Reporting_Bugs.md:52
#, no-wrap
msgid "What results have I achieved?"
msgstr "Quels résultats ai-je obtenus ?"

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:55
msgid ""
"The bug: what you see that shouldn’t have happened. Here you can provide the "
"logs."
msgstr ""
"Le bug : ce que vous voyez qui n’aurez pas dû se passer. Ici vous pouvez "
"fournir les logs."

#. type: Title ###
#: en/./developers/06_Reporting_Bugs.md:56
#, no-wrap
msgid "What was the expected result?"
msgstr "Quel était le résultat attendu ?"

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:59
msgid "So that we understand what you consider to be the problem."
msgstr ""
"Afin que nous comprenions bien où est le problème… au moins selon vous :p"

#. type: Title ###
#: en/./developers/06_Reporting_Bugs.md:60
#, no-wrap
msgid "What are my circumstances?"
msgstr "Quelle est ma situation ?"

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:63
msgid "Remember to give the following information if you know it:"
msgstr "Pensez à donner les informations suivantes si vous les connaissez :"

#. type: Bullet: '1. '
#: en/./developers/06_Reporting_Bugs.md:69
msgid "Which browser? Which version?"
msgstr "Quel navigateur ? Quelle version ?"

#. type: Bullet: '2. '
#: en/./developers/06_Reporting_Bugs.md:69
msgid "Which server: Apache, Nginx? Which version?"
msgstr "Quel serveur : Apache, Nginx ? Quelle version ?"

#. type: Bullet: '3. '
#: en/./developers/06_Reporting_Bugs.md:69
msgid "Which version of PHP?"
msgstr "Quelle version de PHP ?"

#. type: Bullet: '4. '
#: en/./developers/06_Reporting_Bugs.md:69
msgid "Which database: SQLite, MySQL, MariaDB, PostgreSQL? Which version?"
msgstr ""
"Quelle base de données : SQLite, MySQL, MariaDB, PostgreSQL ? Quelle "
"version ?"

#. type: Bullet: '5. '
#: en/./developers/06_Reporting_Bugs.md:69
msgid "Which distribution runs on the server? And… which version?"
msgstr "Quelle distribution sur le serveur ? Et… quelle version ?"

#. type: Title ##
#: en/./developers/06_Reporting_Bugs.md:70
#, fuzzy, no-wrap
#| msgid "How to write a commit message"
msgid "How to provide feed data"
msgstr "Comment écrire un message de commit"

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:74
msgid ""
"If you need us to investigate a feed problem, it will be easier if you "
"provide a snapshot of the feed data.  To do that, you can launch the "
"following command:"
msgstr ""

#. type: Fenced code block (bash)
#: en/./developers/06_Reporting_Bugs.md:75
#, no-wrap
msgid "wget <feed url> -O output.rss.txt\n"
msgstr ""

#. type: Plain text
#: en/./developers/06_Reporting_Bugs.md:78
msgid "Then you can drag-and-drop the generated file into the issue."
msgstr ""

#. type: Plain text
#: en/./developers/Minz/index.md:4
msgid "Minz is the homemade PHP framework used by FreshRSS."
msgstr ""

#. type: Plain text
#: en/./developers/Minz/index.md:6
msgid ""
"This data sheet should refer to the official FreshRSS and Minz documentation "
"(the PHP framework on which FreshRSS is based). Unfortunately, this "
"documentation does not yet exist. In a few words, here are the main things "
"you should know. It is not necessary to read all the chapters in this "
"section if you don’t need to use a feature in your extension (if you don’t "
"need to translate your extension, no need to know more about the "
"`Minz_Translate` module for example)."
msgstr ""
"Cette fiche technique devrait renvoyer vers la documentation officielle de "
"FreshRSS et de Minz (le framework PHP sur lequel repose FreshRSS). "
"Malheureusement cette documentation n’existe pas encore. Voici donc en "
"quelques mots les principaux éléments à connaître. Il n’est pas nécessaire "
"de lire l’ensemble des chapitres de cette section si vous n’avez pas à "
"utiliser une fonctionnalité dans votre extension (si vous n’avez pas besoin "
"de traduire votre extension, pas besoin d’en savoir plus sur le module "
"`Minz_Translate` par exemple)."

#. type: Title ##
#: en/./developers/Minz/index.md:7
#, no-wrap
msgid "MVC Architecture"
msgstr "Architecture MVC"

#. type: Plain text
#: en/./developers/Minz/index.md:10
msgid ""
"Minz relies on and imposes an MVC architecture on projects using it. This "
"architecture consists of three main components:"
msgstr ""
"Minz repose et impose une architecture MVC pour les projets l’utilisant. On "
"distingue dans cette architecture trois composants principaux :"

#. type: Bullet: '* '
#: en/./developers/Minz/index.md:14
msgid ""
"The model: this is the base object that we will manipulate. In FreshRSS, "
"categories, flows and articles are templates. The part of the code that "
"makes it possible to manipulate them in a database is also part of the model "
"but is separated from the base model: we speak of DAO (for \"Data Access "
"Object\"). The templates are stored in a `Models` folder."
msgstr ""
"Le Modèle : c’est l’objet de base que l’on va manipuler. Dans FreshRSS, les "
"catégories, les flux et les articles sont des modèles. La partie du code qui "
"permet de les manipuler en base de données fait aussi partie du modèle mais "
"est séparée du modèle de base : on parle de DAO (pour « Data Access Object "
"»). Les modèles sont stockés dans un répertoire `Models`."

#. type: Bullet: '* '
#: en/./developers/Minz/index.md:14
msgid ""
"The view: this is what the user sees. The view is therefore simply HTML code "
"mixed with PHP to display dynamic information. The views are stored in a "
"`views` folder."
msgstr ""
"La Vue : c’est ce qui représente ce que verra l’utilisateur. La vue est donc "
"simplement du code HTML que l’on mixe avec du PHP pour afficher les "
"informations dynamiques. Les vues sont stockées dans un répertoire `views`."

#. type: Bullet: '* '
#: en/./developers/Minz/index.md:14
msgid ""
"The controller: this is what makes it possible to link models and views. "
"Typically, a controller will load templates from the database (like a list "
"of items) to \"pass\" them to a view for display. Controllers are stored in "
"a `Controllers` directory."
msgstr ""
"Le Contrôleur : c’est ce qui permet de lier modèles et vues entre eux. "
"Typiquement, un contrôleur va charger des modèles à partir de la base de "
"données (une liste d’articles par exemple) pour les « passer » à une vue "
"afin qu’elle les affiche. Les contrôleurs sont stockés dans un répertoire "
"`Controllers`."

#. type: Title ##
#: en/./developers/Minz/index.md:15
#, no-wrap
msgid "Routing"
msgstr "Routage"

#. type: Plain text
#: en/./developers/Minz/index.md:19
#, fuzzy
#| msgid ""
#| "In order to link a URL to a controller, first you have to go through a "
#| "\"routing\" phase. In FreshRSS, this is particularly simple because it "
#| "suffices to specify the name of the controller to load into the URL using "
#| "a `c` parameter. For example, the address http://exemple.com?c=hello will "
#| "execute the code contained in the `hello` controller."
msgid ""
"In order to link a URL to a controller, first you have to go through a "
"\"routing\" phase. In FreshRSS, this is particularly simple because it "
"suffices to specify the name of the controller to load into the URL using a "
"`c` parameter.  For example, the address <http://example.com?c=hello> will "
"execute the code contained in the `hello` controller."
msgstr ""
"Afin de lier une URL à un contrôleur, on doit passer par une phase dite de « "
"routage ». Dans FreshRSS, cela est particulièrement simple car il suffit "
"d’indiquer le nom du contrôleur à charger dans l’URL à l’aide d’un paramètre "
"`c`. Par exemple, l’adresse http://exemple.com?c=hello va exécuter le code "
"contenu dans le contrôleur `hello`."

#. type: Plain text
#: en/./developers/Minz/index.md:21
msgid ""
"One concept that has not yet been discussed is the \"actions\" system. An "
"action is executed *on* a controller. Concretely, a controller is "
"represented by a class and its actions by methods. To execute an action, it "
"is necessary to specify an `a` parameter in the URL."
msgstr ""
"Une notion qui n’a pas encore été évoquée est le système d'« actions ». Une "
"action est exécutée *sur* un contrôleur. Concrètement, un contrôleur va être "
"représenté par une classe et ses actions par des méthodes. Pour exécuter une "
"action, il est nécessaire d’indiquer un paramètre `a` dans l’URL."

#. type: Plain text
#: en/./developers/Minz/index.md:23 en/./developers/Minz/index.md:143
msgid "Code example:"
msgstr "Exemple de code :"

#. type: Fenced code block (php)
#: en/./developers/Minz/index.md:24
#, no-wrap
msgid ""
"<?php\n"
"\n"
"class FreshRSS_hello_Controller extends FreshRSS_ActionController {\n"
"\tpublic function indexAction() {\n"
"\t\t$this->view->a_variable = 'FooBar';\n"
"\t}\n"
"\n"
"\tpublic function worldAction() {\n"
"\t\t$this->view->a_variable = 'Hello World!';\n"
"\t}\n"
"}\n"
"\n"
"?>\n"
msgstr ""
"<?php\n"
"\n"
"class FreshRSS_hello_Controller extends FreshRSS_ActionController {\n"
"\tpublic function indexAction() {\n"
"\t\t$this->view->a_variable = 'FooBar';\n"
"\t}\n"
"\n"
"\tpublic function worldAction() {\n"
"\t\t$this->view->a_variable = 'Hello World!';\n"
"\t}\n"
"}\n"
"\n"
"?>\n"

#. type: Plain text
#: en/./developers/Minz/index.md:41
#, fuzzy
#| msgid ""
#| "When loading the address http://exemple.com?c=hello&a=world, the `world` "
#| "action is executed on the `hello` controller."
msgid ""
"When loading the address <http://example.com?c=hello&a=world>, the `world` "
"action is executed on the `hello` controller."
msgstr ""
"Si l’on charge l’adresse http://exemple.com?c=hello&a=world, l’action "
"`world` va donc être exécutée sur le contrôleur `hello`."

#. type: Plain text
#: en/./developers/Minz/index.md:44
#, fuzzy
#| msgid ""
#| "Note: if `c` or `a` is not specified, the default value for each of these "
#| "variables is `index`. So the address http://exemple.com?c=hello will "
#| "execute the `index` action of the `hello` controller."
msgid ""
"Note: if `c` or `a` is not specified, the default value for each of these "
"variables is `index`.  So the address <http://example.com?c=hello> will "
"execute the `index` action of the `hello` controller."
msgstr ""
"Note : si `c` ou `a` n’est pas précisée, la valeur par défaut de chacune de "
"ces variables est `index`. Ainsi l’adresse http://exemple.com?c=hello va "
"exécuter l’action `index` du contrôleur `hello`."

#. type: Plain text
#: en/./developers/Minz/index.md:46
msgid ""
"From now on, the `hello/world` naming convention will be used to refer to a "
"controller/action pair."
msgstr ""
"Plus loin, sera utilisée la convention `hello/world` pour évoquer un couple "
"contrôleur/action."

#. type: Title #
#: en/./developers/Minz/index.md:47 en/./users/03_Main_view.md:1
#, no-wrap
msgid "Views"
msgstr "Vues"

#. type: Plain text
#: en/./developers/Minz/index.md:50
msgid ""
"Each view is associated with a controller and an action. The view associated "
"with `hello/world` will be stored in a very specific file: `views/hello/"
"world. phtml`. This convention is imposed by Minz."
msgstr ""
"Chaque vue est associée à un contrôleur et à une action. La vue associée à "
"`hello/world` va être stockée dans un fichier bien spécifique : `views/hello/"
"world.phtml`. Cette convention est imposée par Minz."

#. type: Plain text
#: en/./developers/Minz/index.md:52
msgid ""
"As explained above, the views consist of HTML mixed with PHP. Code example:"
msgstr ""
"Comme expliqué plus haut, les vues sont du code HTML mixé à du PHP. Exemple "
"de code :"

#. type: Fenced code block (html)
#: en/./developers/Minz/index.md:53
#, no-wrap
msgid ""
"<p>\n"
"\tThis is a parameter passed from the controller: <?= $this->a_variable ?>\n"
"</p>\n"
msgstr ""
"<p>\n"
"\tPhrase passée en paramètre : <?= $this->a_variable ?>\n"
"</p>\n"

#. type: Plain text
#: en/./developers/Minz/index.md:60
#, no-wrap
msgid "The variable `$this->a_variable` is passed by the controller (see previous example). The difference is that in the controller it is necessary to pass `$this->view`, while in the view `$this` suffices.\n"
msgstr "La variable `$this->a_variable` a été passée précédemment par le contrôleur (voir exemple précédent). La différence est que dans le contrôleur il est nécessaire de passer par `$this->view` et que dans la vue `$this` suffit.\n"

#. type: Title ##
#: en/./developers/Minz/index.md:61
#, no-wrap
msgid "Working with GET / POST"
msgstr "Accéder aux paramètres GET / POST"

#. type: Plain text
#: en/./developers/Minz/index.md:65
msgid ""
"It is often necessary to take advantage of parameters passed by GET or POST. "
"In Minz, these parameters are accessible using the `Minz_Request` class.  "
"Code example:"
msgstr ""
"Il est souvent nécessaire de profiter des paramètres passés par GET ou par "
"POST. Dans Minz, ces paramètres sont accessibles de façon indistincts à "
"l’aide de la classe `Minz_Request`. Exemple de code :"

#. type: Fenced code block (php)
#: en/./developers/Minz/index.md:66
#, fuzzy, no-wrap
#| msgid ""
#| "<?php\n"
#| "\n"
#| "$default_value = 'foo';\n"
#| "$param = Minz_Request::param('bar', $default_value);\n"
#| "\n"
#| "// Display the value of the parameter `bar` (passed via GET or POST)\n"
#| "// or \"foo\" if the parameter does not exist.\n"
#| "echo $param;\n"
#| "\n"
#| "// Sets the value of the `bar` parameter\n"
#| "Minz_Request::_param('bar', 'baz');\n"
#| "\n"
#| "// Will necessarily display \"baz\" since we have just forced its value.\n"
#| "// Note that the second parameter (default) is optional.\n"
#| "echo Minz_Request::param('bar');\n"
#| "\n"
#| "?>\n"
msgid ""
"<?php\n"
"\n"
"$default_value = 'foo';\n"
"$param = Minz_Request::paramString('bar') ?: $default_value;\n"
"\n"
"// Display the value of the parameter `bar` (passed via GET or POST)\n"
"// or \"foo\" if the parameter does not exist.\n"
"echo $param;\n"
"\n"
"// Sets the value of the `bar` parameter\n"
"Minz_Request::_param('bar', 'baz');\n"
"\n"
"// Will necessarily display \"baz\" since we have just forced its value.\n"
"// Note that the second parameter (default) is optional.\n"
"echo Minz_Request::paramString('bar');\n"
"\n"
"?>\n"
msgstr ""
"<?php\n"
"\n"
"$default_value = 'foo';\n"
"$param = Minz_Request::param('bar', $default_value);\n"
"\n"
"// Affichera la valeur du paramètre `bar` (passé via GET ou POST)\n"
"// ou \"foo\" si le paramètre n’existe pas.\n"
"echo $param;\n"
"\n"
"// Force la valeur du paramètre `bar`\n"
"Minz_Request::_param('bar', 'baz');\n"
"\n"
"// Affichera forcément \"baz\" puisque nous venons de forcer sa valeur.\n"
"// Notez que le second paramètre (valeur par défaut) est facultatif.\n"
"echo Minz_Request::param('bar');\n"
"\n"
"?>\n"

#. type: Plain text
#: en/./developers/Minz/index.md:87
msgid ""
"The `Minz_Request::isPost()` method can be used to execute a piece of code "
"only if it is a POST request."
msgstr ""
"La méthode `Minz_Request::isPost()` peut être utile pour n’exécuter un "
"morceau de code que s’il s’agit d’une requête POST."

#. type: Plain text
#: en/./developers/Minz/index.md:89
msgid ""
"Note: it is preferable to use `Minz_Request` only in controllers. It is "
"likely that you will encounter this method in FreshRSS views, or even in "
"templates, but be aware that this is **not** good practice."
msgstr ""
"Note : il est préférable de n’utiliser `Minz_Request` que dans les "
"contrôleurs. Il est probable que vous rencontriez cette méthode dans les "
"vues de FreshRSS, voire dans les modèles, mais sachez qu’il ne s’agit "
"**pas** d’une bonne pratique."

#. type: Title ##
#: en/./developers/Minz/index.md:90
#, no-wrap
msgid "Access session settings"
msgstr "Accéder aux paramètres de session"

#. type: Plain text
#: en/./developers/Minz/index.md:93
msgid ""
"The access to session parameters is strangely similar to the GET / POST "
"parameters but passes through the `Minz_Session` class this time! There is "
"no example here because you can repeat the previous example by changing all "
"`Minz_Request` to `Minz_Session`."
msgstr ""
"L’accès aux paramètres de session est étrangement similaire aux paramètres "
"GET / POST mais passe par la classe `Minz_Session` cette fois-ci ! Il n’y a "
"pas d’exemple ici car vous pouvez reprendre le précédent en changeant tous "
"les `Minz_Request` par des `Minz_Session`."

#. type: Title ##
#: en/./developers/Minz/index.md:94
#, no-wrap
msgid "Working with URLs"
msgstr "Gestion des URL"

#. type: Plain text
#: en/./developers/Minz/index.md:97
msgid ""
"To take full advantage of the Minz routing system, it is strongly "
"discouraged to write hard URLs in your code. For example, the following view "
"should be avoided:"
msgstr ""
"Pour profiter pleinement du système de routage de Minz, il est fortement "
"déconseillé d’écrire les URL en dur dans votre code. Par exemple, la vue "
"suivante doit être évitée :"

#. type: Fenced code block (html)
#: en/./developers/Minz/index.md:98
#, no-wrap
msgid ""
"<p>\n"
"\tGo to page <a href=\"http://example.com?c=hello&amp;a=world\">Hello world</a>!\n"
"</p>\n"
msgstr ""
"<p>\n"
"\tAccéder à la page <a href=\"http://exemple.com?c=hello&amp;a=world\">Hello world</a>!\n"
"</p>\n"

#. type: Plain text
#: en/./developers/Minz/index.md:105
#, fuzzy
#| msgid ""
#| "If one day it was decided to use a \"url rewriting\" system to have "
#| "addresses in a http://exemple.com/controller/action format, all previous "
#| "addresses would become ineffective!"
msgid ""
"If one day it was decided to use a \"url rewriting\" system to have "
"addresses in a <http://example.com/controller/action> format, all previous "
"addresses would become ineffective!"
msgstr ""
"Si un jour il est décidé d’utiliser un système d'« url rewriting » pour "
"avoir des adresses au format http://exemple.com/controller/action, toutes "
"les adresses précédentes deviendraient ineffectives !"

#. type: Plain text
#: en/./developers/Minz/index.md:107
msgid ""
"So use the `Minz_Url` class and its `display()` method instead. `Minz_Url::"
"display()` takes an array of the following form as its argument:"
msgstr ""
"Préférez donc l’utilisation de la classe `Minz_Url` et de sa méthode "
"`display()`. `Minz_Url::display()` prend en paramètre un tableau de la forme "
"suivante :"

#. type: Fenced code block (php)
#: en/./developers/Minz/index.md:108
#, no-wrap
msgid ""
"<?php\n"
"\n"
"$url_array = [\n"
"\t'c' => 'hello',\n"
"\t'a' => 'world',\n"
"\t'params' => [\n"
"\t\t'foo' => 'bar',\n"
"\t],\n"
"];\n"
"\n"
"// Show something like .?c=hello&amp;a=world&amp;foo=bar\n"
"echo Minz_Url::display($url_array);\n"
"\n"
"?>\n"
msgstr ""
"<?php\n"
"\n"
"$url_array = [\n"
"\t'c' => 'hello',\n"
"\t'a' => 'world',\n"
"\t'params' => [\n"
"\t\t'foo' => 'bar',\n"
"\t],\n"
"];\n"
"\n"
"// Affichera quelque chose comme .?c=hello&amp;a=world&amp;foo=bar\n"
"echo Minz_Url::display($url_array);\n"
"\n"
"?>\n"

#. type: Plain text
#: en/./developers/Minz/index.md:126
msgid ""
"Since this can become a bit tedious to use in the long run, especially in "
"views, it is preferable to use the `_url()` shortcut:"
msgstr ""
"Comme cela peut devenir un peu pénible à utiliser à la longue, surtout dans "
"les vues, il est préférable d’utiliser le raccourci `_url()` :"

#. type: Fenced code block (php)
#: en/./developers/Minz/index.md:127
#, no-wrap
msgid ""
"<?php\n"
"\n"
"// Displays the same as above\n"
"echo _url('hello', 'world', 'foo', 'bar');\n"
"\n"
"?>\n"
msgstr ""
"<?php\n"
"\n"
"// Affichera la même chose que précédemment\n"
"echo _url('hello', 'world', 'foo', 'bar');\n"
"\n"
"?>\n"

#. type: Plain text
#: en/./developers/Minz/index.md:137
msgid ""
"Note: as a general rule, the shortened form (`_url()`) should be used in "
"views, while the long form (`Minz_Url::display()`) should be used in "
"controllers."
msgstr ""
"Note : en règle générale, la forme raccourcie (`_url()`) doit être utilisée "
"dans les vues tandis que la forme longue (`Minz_Url::display()`) doit être "
"utilisée dans les contrôleurs."

#. type: Title ##
#: en/./developers/Minz/index.md:138
#, no-wrap
msgid "Redirections"
msgstr "Redirections"

#. type: Plain text
#: en/./developers/Minz/index.md:141
msgid ""
"It is often necessary to redirect a user to another page. To do so, the "
"`Minz_Request` class offers another useful method: `forward()`. This method "
"takes the same URL format as the one seen just before as its argument."
msgstr ""
"Il est souvent nécessaire de rediriger un utilisateur vers une autre page. "
"Pour cela, la classe `Minz_Request` dispose d’une autre méthode utile : "
"`forward()`. Cette méthode prend en argument le même format d’URL que celui "
"vu juste avant."

#. type: Fenced code block (php)
#: en/./developers/Minz/index.md:144
#, no-wrap
msgid ""
"<?php\n"
"\n"
"$url_array = [\n"
"\t'c' => 'hello',\n"
"\t'a' => 'world',\n"
"];\n"
"\n"
"// Tells Minz to redirect the user to the hello / world page.\n"
"// Note that this is a redirection in the Minz sense of the term, not a redirection that the browser will have to manage (HTTP code 301 or 302)\n"
"// The code that follows forward() will thus be executed!\n"
"Minz_Request::forward($url_array);\n"
"\n"
"// To perform a type 302 redirect, add \"true\".\n"
"// The code that follows will never be executed.\n"
"Minz_Request::forward($url_array, true);\n"
"\n"
"?>\n"
msgstr ""
"<?php\n"
"\n"
"$url_array = [\n"
"\t'c' => 'hello',\n"
"\t'a' => 'world',\n"
"];\n"
"\n"
"// Indique à Minz de rediriger l’utilisateur vers la page hello/world.\n"
"// Notez qu’il s’agit d’une redirection au sens Minz du terme, pas d’une redirection que le navigateur va avoir à gérer (code HTTP 301 ou 302)\n"
"// Le code qui suit forward() va ainsi être exécuté !\n"
"Minz_Request::forward($url_array);\n"
"\n"
"// Pour effectuer une redirection type 302, ajoutez \"true\".\n"
"// Le code qui suivra ne sera alors jamais exécuté.\n"
"Minz_Request::forward($url_array, true);\n"
"\n"
"?>\n"

#. type: Plain text
#: en/./developers/Minz/index.md:165
msgid ""
"It is very common to want display a message to the user while performing a "
"redirect, to tell the user how the action was carried out (validation of a "
"form for example). Such a message is passed through a `notification` session "
"variable (note: we will talk about feedback from now on to avoid confusion "
"with a notification that can occur at any time). To facilitate this kind of "
"very frequent action, there are two shortcuts that both perform a 302 "
"redirect by assigning a feedback message:"
msgstr ""
"Il est très fréquent de vouloir effectuer une redirection tout en affichant "
"un message à l’utilisateur pour lui indiquer comment s’est déroulée l’action "
"effectuée juste avant (validation d’un formulaire par exemple). Un tel "
"message est passé par une variable de session `notification` (note : nous "
"parlerons plutôt de « feedback » désormais pour éviter la confusion avec une "
"notification qui peut survenir à tout moment). Pour faciliter ce genre "
"d’action très fréquente, il existe deux raccourcis qui effectuent tout deux "
"une redirection type 302 en affectant un message de feedback :"

#. type: Fenced code block (php)
#: en/./developers/Minz/index.md:166
#, no-wrap
msgid ""
"<?php\n"
"\n"
"$url_array = [\n"
"\t'c' => 'hello',\n"
"\t'a' => 'world',\n"
"];\n"
"$feedback_good = 'All went well!';\n"
"$feedback_bad = 'Oops, something went wrong.';\n"
"\n"
"Minz_Request::good($feedback_good, $url_array);\n"
"\n"
"// or\n"
"\n"
"Minz_Request::bad($feedback_bad, $url_array);\n"
"\n"
"?>\n"
msgstr ""
"<?php\n"
"\n"
"$url_array = [\n"
"\t'c' => 'hello',\n"
"\t'a' => 'world',\n"
"];\n"
"$feedback_good = 'Tout s’est bien passé !';\n"
"$feedback_bad = 'Oups, quelque chose n’a pas marché.';\n"
"\n"
"Minz_Request::good($feedback_good, $url_array);\n"
"\n"
"// ou\n"
"\n"
"Minz_Request::bad($feedback_bad, $url_array);\n"
"\n"
"?>\n"

#. type: Title ##
#: en/./developers/Minz/index.md:185
#, no-wrap
msgid "Translation Management"
msgstr "Gestion de la traduction"

#. type: Plain text
#: en/./developers/Minz/index.md:188
msgid "This part [is explained here](/docs/en/internationalization.md)."
msgstr ""

#. type: Title ##
#: en/./developers/Minz/index.md:189
#, fuzzy, no-wrap
#| msgid "Information"
msgid "Migration"
msgstr "Informations"

#. type: Plain text
#: en/./developers/Minz/index.md:192
msgid "Existing documentation includes:"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/Minz/index.md:193
msgid "[How to manage migrations](migrations.md)"
msgstr ""

#. type: Title #
#: en/./developers/Minz/migrations.md:1
#, no-wrap
msgid "How to manage migrations with Minz"
msgstr ""

#. type: Plain text
#: en/./developers/Minz/migrations.md:4
msgid ""
"Migrations are the way to modify the database or the structure of files "
"under the `data/` path."
msgstr ""

#. type: Title ##
#: en/./developers/Minz/migrations.md:5
#, fuzzy, no-wrap
#| msgid "How to write a commit message"
msgid "How to write a migration?"
msgstr "Comment écrire un message de commit"

#. type: Plain text
#: en/./developers/Minz/migrations.md:8
msgid "Migrations are placed under the `app/migrations` folder."
msgstr ""

#. type: Plain text
#: en/./developers/Minz/migrations.md:10
msgid ""
"Good practice is to prepend the filename by the current date and explain "
"what does the migration do in few words (e.g. `2020_01_11_CreateFooTable."
"php`)."
msgstr ""

#. type: Plain text
#: en/./developers/Minz/migrations.md:12
msgid ""
"The files must contain a class which name starts with `FreshRSS_Migration_`, "
"followed by the basename of the file (e.g. "
"`FreshRSS_Migration_2020_01_11_CreateFooTable`)."
msgstr ""

#. type: Plain text
#: en/./developers/Minz/migrations.md:14
msgid ""
"The class must declare a `migrate` static function. It must return `true` or "
"a string to indicate the migration is applied, or `false` otherwise. It can "
"also raise an exception: the message will be used to detail the error."
msgstr ""

#. type: Plain text
#: en/./developers/Minz/migrations.md:16
#, fuzzy
#| msgid "Example: Rue89"
msgid "Example:"
msgstr "Exemple : Rue89"

#. type: Fenced code block (php)
#: en/./developers/Minz/migrations.md:17
#, no-wrap
msgid ""
"// File: app/migrations/2020_01_11_CreateFooTable.php\n"
"class FreshRSS_Migration_2020_01_11_CreateFooTable {\n"
"\tpublic static function migrate() {\n"
"\t\t$pdo = new Minz_PdoSqlite('sqlite:/some/path/db.sqlite');\n"
"\t\t$result = $pdo->exec('CREATE TABLE foos (bar TEXT)');\n"
"\t\tif ($result === false) {\n"
"\t\t\t$error = $pdo->errorInfo();\n"
"\t\t\traise Exception('Error in SQL statement: ' . $error[2]);\n"
"\t\t}\n"
"\n"
"\t\treturn true;\n"
"\t}\n"
"}\n"
msgstr ""

#. type: Title ##
#: en/./developers/Minz/migrations.md:33
#, no-wrap
msgid "How to apply migrations?"
msgstr ""

#. type: Plain text
#: en/./developers/Minz/migrations.md:36
msgid ""
"They are automatically applied one by one when a user accesses FreshRSS."
msgstr ""

#. type: Plain text
#: en/./developers/Minz/migrations.md:38
msgid ""
"Before being applied, migrations are sorted by filenames (see the "
"[`strnatcmp`](https://php.net/strnatcmp) function). Already applied "
"migrations are skipped (the list can be found in the `data/"
"applied_migrations.txt` file)."
msgstr ""

#. type: Plain text
#: en/./developers/Minz/migrations.md:39
msgid ""
"To ensure migrations are not applied several times if two users access "
"FreshRSS at the same time, a folder named `data/applied_migrations.txt.lock` "
"is created, then deleted at the end of the process."
msgstr ""

#. type: Title #
#: en/./developers/OPML.md:1
#, fuzzy, no-wrap
#| msgid "Enable the API in FreshRSS"
msgid "OPML in FreshRSS"
msgstr "Activer l’API dans FreshRSS"

#. type: Plain text
#: en/./developers/OPML.md:4
msgid ""
"FreshRSS supports the [OPML](https://en.wikipedia.org/wiki/OPML) format to "
"export and import lists of RSS/Atom feeds in a standard way, compatible with "
"several other RSS aggregators."
msgstr ""

#. type: Plain text
#: en/./developers/OPML.md:7
msgid ""
"However, FreshRSS also supports several additional features not covered by "
"the basic OPML specification.  Luckily, the [OPML specification](http://opml."
"org/spec2.opml) allows extensions:"
msgstr ""

#. type: Plain text
#: en/./developers/OPML.md:9
#, no-wrap
msgid "> *An OPML file may contain elements and attributes not described on this page, only if those elements are defined in a namespace.*\n"
msgstr ""

#. type: Plain text
#: en/./developers/OPML.md:11
msgid "and:"
msgstr ""

#. type: Plain text
#: en/./developers/OPML.md:13
#, no-wrap
msgid "> *OPML can also be extended by the addition of new values for the type attribute.*\n"
msgstr ""

#. type: Title ##
#: en/./developers/OPML.md:14
#, no-wrap
msgid "FreshRSS OPML extension"
msgstr ""

#. type: Plain text
#: en/./developers/OPML.md:17
msgid ""
"FreshRSS uses the XML namespace <https://freshrss.org/opml> to export/import "
"extended information not covered by the basic OPML specification."
msgstr ""

#. type: Plain text
#: en/./developers/OPML.md:19
msgid ""
"The list of the custom FreshRSS attributes can be seen in [the source code]"
"(https://github.com/FreshRSS/FreshRSS/blob/edge/app/views/helpers/export/"
"opml.phtml), and here is an overview:"
msgstr ""

#. type: Title ###
#: en/./developers/OPML.md:20
#, no-wrap
msgid "HTML+XPath or XML+XPath"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:23
msgid ""
"`<outline type=\"HTML+XPath\" ...`: Additional type of source, which is not "
"RSS/Atom, but HTML Web Scraping using [XPath](https://www.w3.org/TR/"
"xpath-10/) 1.0."
msgstr ""

#. type: Plain text
#: en/./developers/OPML.md:25
#, no-wrap
msgid "> ℹ️ [XPath 1.0](https://en.wikipedia.org/wiki/XPath) is a standard query language, which FreshRSS supports to enable [Web scraping](https://en.wikipedia.org/wiki/Web_scraping).\n"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:27
msgid ""
"`<outline type=\"XML+XPath\" ...`: Same than `HTML+XPath` but using an XML "
"parser."
msgstr ""

#. type: Plain text
#: en/./developers/OPML.md:29
msgid ""
"The following attributes are using similar naming conventions than [RSS-"
"Bridge](https://rss-bridge.github.io/rss-bridge/Bridge_API/XPathAbstract."
"html)."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:46
msgid ""
"`frss:xPathItem`: XPath expression for extracting the feed items from the "
"source page."
msgstr ""

#. type: Bullet: '	* '
#: en/./developers/OPML.md:46
msgid "Example: `//div[@class=\"news-item\"]`"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:46
msgid ""
"`frss:xPathItemTitle`: XPath expression for extracting the item’s title from "
"the item context."
msgstr ""

#. type: Bullet: '	* '
#: en/./developers/OPML.md:46
msgid "Example: `descendant::h2`"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:46
msgid ""
"`frss:xPathItemContent`: XPath expression for extracting an item’s content "
"from the item context."
msgstr ""

#. type: Bullet: '	* '
#: en/./developers/OPML.md:46
#, fuzzy
#| msgid "Example: Rue89"
msgid "Example: `.`"
msgstr "Exemple : Rue89"

#. type: Bullet: '* '
#: en/./developers/OPML.md:46
msgid ""
"`frss:xPathItemUri`: XPath expression for extracting an item link from the "
"item context."
msgstr ""

#. type: Bullet: '	* '
#: en/./developers/OPML.md:46
msgid "Example: `descendant::a/@href`"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:46
msgid ""
"`frss:xPathItemAuthor`: XPath expression for extracting an item author from "
"the item context."
msgstr ""

#. type: Bullet: '	* '
#: en/./developers/OPML.md:46
msgid "Example: `\"Anonymous\"`"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:46
msgid ""
"`frss:xPathItemTimestamp`: XPath expression for extracting an item timestamp "
"from the item context. The result will be parsed by [`strtotime()`](https://"
"php.net/strtotime)."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:46
msgid ""
"`frss:xPathItemTimeFormat`: Date/Time format to parse the timestamp, "
"according to [`DateTime::createFromFormat()`](https://php.net/datetime."
"createfromformat)."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:46
msgid ""
"`frss:xPathItemThumbnail`: XPath expression for extracting an item’s "
"thumbnail (image) URL from the item context."
msgstr ""

#. type: Bullet: '	* '
#: en/./developers/OPML.md:46
msgid "Example: `descendant::img/@src`"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:46
msgid ""
"`frss:xPathItemCategories`: XPath expression for extracting a list of "
"categories (tags) from the item context."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:46
msgid ""
"`frss:xPathItemUid`: XPath expression for extracting an item’s unique ID "
"from the item context. If left empty, a hash is computed automatically."
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:54
msgid ""
"`frss:cssFullContent`: [CSS Selector](https://developer.mozilla.org/en-US/"
"docs/Web/CSS/CSS_Selectors) to enable the download and extraction of the "
"matching HTML section of each articles’ Web address."
msgstr ""

#. type: Bullet: '	* '
#: en/./developers/OPML.md:54
msgid "Example: `div.main, .summary`"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:54
msgid ""
"`frss:cssContentFilter`: [CSS Selector](https://developer.mozilla.org/en-"
"US/docs/Web/CSS/CSS_Selectors) to remove the matching HTML elements from the "
"full content retrieved by `frss:cssFullContent`."
msgstr ""

#. type: Bullet: '	* '
#: en/./developers/OPML.md:54
msgid "Example: `.footer, .aside`"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:54
msgid ""
"`frss:filtersActionRead`: List (separated by a new line) of search queries "
"to automatically mark a new article as read."
msgstr ""

#. type: Title ###
#: en/./developers/OPML.md:55
#, no-wrap
msgid "Dynamic OPML (reading lists)"
msgstr ""

#. type: Bullet: '* '
#: en/./developers/OPML.md:58
msgid ""
"`frss:opmlUrl`: If non-empty, indicates that this outline (category) should "
"be dynamically populated from a remote OPML at the specified URL."
msgstr ""

#. type: Title ###
#: en/./developers/OPML.md:59
#, fuzzy, no-wrap
#| msgid "Example: Rue89"
msgid "Example"
msgstr "Exemple : Rue89"

#. type: Fenced code block (xml)
#: en/./developers/OPML.md:61
#, no-wrap
msgid ""
"<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
"<opml version=\"2.0\">\n"
"\t<head>\n"
"\t\t<title>FreshRSS OPML extension example</title>\n"
"\t</head>\n"
"\t<body>\n"
"\t\t<outline xmlns:frss=\"https://freshrss.org/opml\"\n"
"\t\t\ttext=\"Example\"\n"
"\t\t\ttype=\"HTML+XPath\"\n"
"\t\t\txmlUrl=\"https://www.example.net/page.html\"\n"
"\t\t\thtmlUrl=\"https://www.example.net/page.html\"\n"
"\t\t\tdescription=\"Example of Web scraping\"\n"
"\t\t\tfrss:xPathItem=\"//a[contains(@href, '/interesting/')]/ancestor::article\"\n"
"\t\t\tfrss:xPathItemTitle=\"descendant::h2\"\n"
"\t\t\tfrss:xPathItemContent=\".\"\n"
"\t\t\tfrss:xPathItemUri=\"descendant::a[string-length(@href)&gt;0]/@href\"\n"
"\t\t\tfrss:xPathItemThumbnail=\"descendant::img/@src\"\n"
"\t\t\tfrss:cssFullContent=\"article\"\n"
"\t\t\tfrss:filtersActionRead=\"intitle:⚡️ OR intitle:🔥&#10;something\"\n"
"\t\t/>\n"
"\t</body>\n"
"</opml>\n"
msgstr ""

#. type: Plain text
#: en/./index.md:2
msgid "![FreshRSS logo](img/logo_freshrss.png)"
msgstr "![Logo de FreshRSS](img/logo_freshrss.png)"

#. type: Title #
#: en/./index.md:3
#, no-wrap
msgid "FreshRSS manual (English)"
msgstr ""

#. type: Plain text
#: en/./index.md:6
msgid ""
"FreshRSS is an RSS aggregator and reader. It allows you to read and follow "
"several news websites at a glance without the need to browse from one "
"website to another."
msgstr ""
"FreshRSS est un agrégateur et lecteur de flux RSS. Il permet de regrouper "
"l’actualité de plusieurs sites différents dans un endroit unique pour que "
"vous puissiez la lire sans devoir aller de site en site."

#. type: Plain text
#: en/./index.md:10
msgid "FreshRSS has a lot of features including:"
msgstr ""
"FreshRSS a été conçu comme un agrégateur puissant et propose des tas de "
"fonctionnalités :"

#. type: Bullet: '* '
#: en/./index.md:24
msgid "RSS and Atom aggregation"
msgstr "Agrégation des flux RSS et Atom."

#. type: Bullet: '* '
#: en/./index.md:24
msgid ""
"Mark article as favorite if you liked it or if you want to read it later"
msgstr ""
"Utilisez les favoris pour marquer les articles qui vous ont plu ou que vous "
"souhaitez lire plus tard."

#. type: Bullet: '* '
#: en/./index.md:24
msgid "Filter and search functionality helps to easily find articles"
msgstr ""
"Le système de filtrage et de recherche permettent de cibler exactement les "
"articles que vous souhaitez lire."

#. type: Bullet: '* '
#: en/./index.md:24
msgid ""
"Statistics to show you the publishing frequency all the websites you follow"
msgstr ""
"Les statistiques permettent de savoir en un coup d’œil quels sont les sites "
"qui publient le plus, ou à l’inverse, le moins."

#. type: Bullet: '* '
#: en/./index.md:24
msgid "Import/export of your feeds into OPML format"
msgstr "Importation / exportation des flux au format OPML."

#. type: Bullet: '* '
#: en/./index.md:24
msgid "Several themes created by the community"
msgstr "Multi-thèmes pour changer l’habillage de FreshRSS."

#. type: Bullet: '* '
#: en/./index.md:24
#, fuzzy
#| msgid "Several themes created by the community"
msgid "Several extensions created by the community"
msgstr "Multi-thèmes pour changer l’habillage de FreshRSS."

#. type: Bullet: '* '
#: en/./index.md:24
msgid "\"Google Reader\"-like API to connect Android applications"
msgstr "API Google Reader pour pouvoir y brancher des applications Android."

#. type: Bullet: '* '
#: en/./index.md:24
msgid ""
"The application is \"responsive,\" which means it adapts to small screens so "
"you can bring articles in your pocket"
msgstr ""
"« *Responsive design* » : l’application s’adapte aux petits écrans pour "
"emporter FreshRSS dans votre poche."

#. type: Bullet: '* '
#: en/./index.md:24
msgid ""
"Self-hosted: the code is free (under AGPL3 licence), so you can host your "
"own instance of FreshRSS"
msgstr ""
"Auto-hébergeable : le code source est libre (AGPL3) et vous pouvez donc "
"l’héberger sur votre propre serveur."

#. type: Bullet: '* '
#: en/./index.md:24
msgid "Multi-user, so you can also host for your friends and family"
msgstr ""
"Multi-utilisateurs pour héberger plusieurs personnes sur une même "
"installation."

#. type: Bullet: '* '
#: en/./index.md:24
msgid "share article links with a bunch of services"
msgstr ""

#. type: Bullet: '* '
#: en/./index.md:24
msgid "And a lot more!"
msgstr "Et bien d’autres !"

#. type: Title ##
#: en/./index.md:25
#, fuzzy, no-wrap
#| msgid "Manual update"
msgid "Manual Chapters"
msgstr "Mise à jour manuelle"

#. type: Plain text
#: en/./index.md:28
msgid "This documentation is split into different sections:"
msgstr "Cette documentation est divisée en plusieurs parties :"

#. type: Bullet: '* '
#: en/./index.md:33
#, fuzzy
#| msgid ""
#| "[User documentation](./users/02_First_steps.html), where you can discover "
#| "all the possibilities offered by FreshRSS"
msgid ""
"[User documentation](./users/02_First_steps.md), where you can discover all "
"the possibilities offered by FreshRSS"
msgstr ""
"La [documentation utilisateurs](./users/02_First_steps.md) pour découvrir "
"les fonctionnalités de FreshRSS."

#. type: Bullet: '* '
#: en/./index.md:33
#, fuzzy
#| msgid ""
#| "[Administrator documentation](./admins/01_Index.html) for detailed "
#| "installation and maintenance related tasks"
msgid ""
"[Administrator documentation](./admins/01_Index.md) for detailed "
"installation and maintenance related tasks"
msgstr ""
"La [documentation administrateurs](../en/admins/01_Installation.md) (en "
"anglais) pour l’installation et la maintenance de FreshRSS."

#. type: Bullet: '* '
#: en/./index.md:33
#, fuzzy
#| msgid ""
#| "[Developer documentation](./developers/01_First_steps.html) to guide you "
#| "in the source code of FreshRSS and to help you if you want to contribute"
msgid ""
"[Developer documentation](./developers/01_Index.md) to guide you in the "
"source code of FreshRSS and to help you if you want to contribute"
msgstr ""
"La [documentation développeurs](./developers/01_First_steps.md) pour savoir "
"comment contribuer et mieux comprendre le code source de FreshRSS."

#. type: Bullet: '* '
#: en/./index.md:33
msgid ""
"[Contributor guidelines](./contributing.md) for those who want to help "
"improve FreshRSS"
msgstr ""
"Le [guide de contribution](./contributing.md) pour nous aider à développer "
"FreshRSS."

#. type: Title ##
#: en/./index.md:34
#, no-wrap
msgid "Demo"
msgstr ""

#. type: Plain text
#: en/./index.md:37
msgid ""
"The official demo of FreshRSS is available under [https://demo.freshrss.org/]"
"(https://demo.freshrss.org/)."
msgstr ""

#. type: Plain text
#: en/./index.md:39
msgid "Login credentials:"
msgstr ""

#. type: Bullet: '* '
#: en/./index.md:42
msgid "Username: demo"
msgstr ""

#. type: Bullet: '* '
#: en/./index.md:42
msgid "Password: demodemo"
msgstr ""

#. type: Plain text
#: en/./index.md:44
msgid ""
"Another chance to try out, but not official supported by FreshRSS: The "
"application is listed on Softaculous [https://www.softaculous.com/apps/rss/"
"FreshRSS](https://www.softaculous.com/apps/rss/FreshRSS)."
msgstr ""

#. type: Title ##
#: en/./index.md:45
#, no-wrap
msgid "Licence"
msgstr ""

#. type: Plain text
#: en/./index.md:47
msgid "FreshRSS is licensed under the GNU Affero General Public License v3.0."
msgstr ""

#. type: Title #
#: en/./internationalization.md:1
#, fuzzy, no-wrap
#| msgid "Contribute to internationalization (i18n)"
msgid "Contributing to internationalization (i18n)"
msgstr "Contribuer à l’internationalisation (i18n)"

#. type: Plain text
#: en/./internationalization.md:4
msgid ""
"Thanks to our contributors, FreshRSS is translated into [more than 20 "
"languages](./users/05_Configuration.md#language). This section will explain "
"the basics of internationalization in FreshRSS, from translating the "
"application to your own language to making a specific change."
msgstr ""

#. type: Title ##
#: en/./internationalization.md:5
#, fuzzy, no-wrap
#| msgid "Reader view"
msgid "Overview"
msgstr "La vue lecture"

#. type: Plain text
#: en/./internationalization.md:8
#, fuzzy
#| msgid ""
#| "It is common (and that’s an understatement) to want to show some text to "
#| "the user. In the previous example, for example, we display feedback to "
#| "the user based on the result of form validation. The problem is that "
#| "FreshRSS has users of different nationalities. It is therefore necessary "
#| "to be able to manage different languages in order not to remain confined "
#| "to English or French."
msgid ""
"It is common (and that’s an understatement) to want to show some text to the "
"user. The problem is that FreshRSS has users of different nationalities. It "
"is therefore necessary to be able to manage different languages in order not "
"to remain confined to English or French."
msgstr ""
"Il est fréquent (et c’est un euphémisme) de vouloir afficher des phrases à "
"l’utilisateur. Dans l’exemple précédent par exemple, nous affichions un "
"feedback à l’utilisateur en fonction du résultat d’une validation de "
"formulaire. Le problème est que FreshRSS possède des utilisateurs de "
"différentes nationalités. Il est donc nécessaire de pouvoir gérer "
"différentes langues pour ne pas rester cantonné à l’Anglais ou au Français."

#. type: Plain text
#: en/./internationalization.md:10
#, fuzzy
#| msgid ""
#| "The solution is to use the `Minz_Translate` class, which allows dynamic "
#| "translation of FreshRSS (or any Minz-based application). Before using "
#| "this module, it is necessary to know where to find the strings to be "
#| "translated. Each language has its own subdirectory in a parent directory "
#| "named `i18n`. For example, English language files are located in `i18n/fr/"
#| "`. There are seven different files:"
msgid ""
"The solution is to use the `Minz_Translate` module, which allows dynamic "
"translation of FreshRSS. Before using this module, it is necessary to know "
"where to find the strings to be translated. Each language has its own "
"subdirectory in a parent directory named `app/i18n/`. For example, English "
"language files are located in [`app/i18n/en/`](/app/i18n/en/). There are "
"seven different files:"
msgstr ""
"La solution consiste à utiliser la classe `Minz_Translate` qui permet de "
"traduire dynamiquement FreshRSS (ou toute application basée sur Minz). Avant "
"d’utiliser ce module, il est nécessaire de savoir où trouver les chaînes de "
"caractères à traduire. Chaque langue possède son propre sous-répertoire dans "
"un répertoire parent nommé `i18n`. Par exemple, les fichiers de langue en "
"Français sont situés dans `i18n/fr/`. Il existe sept fichiers différents :"

#. type: Bullet: '* '
#: en/./internationalization.md:19
msgid "`admin.php` for anything related to FreshRSS administration"
msgstr ""
"`admin.php` pour tout ce qui est relatif à l’administration de FreshRSS ;"

#. type: Bullet: '* '
#: en/./internationalization.md:19
msgid "`conf.php` for configuration"
msgstr "`conf.php` pour l’aspect configuration ;"

#. type: Bullet: '* '
#: en/./internationalization.md:19
msgid "`feedback.php` contains translations of feedback messages"
msgstr "`feedback.php` contient les traductions des messages de feedback ;"

#. type: Bullet: '* '
#: en/./internationalization.md:19
#, fuzzy
#| msgid "`gen.php` stores what is global to FreshRSS (gen for \"general\")"
msgid ""
"`gen.php` stores what is global to FreshRSS (`gen` stands for “general”)"
msgstr "`gen.php` stocke ce qui est global à FreshRSS (gen pour « general ») ;"

#. type: Bullet: '* '
#: en/./internationalization.md:19
msgid "`index.php` for the main page that lists feeds and the About page"
msgstr ""
"`index.php` pour la page principale qui liste les flux et la page « À propos "
"» ;"

#. type: Bullet: '* '
#: en/./internationalization.md:19
#, fuzzy
#| msgid "`install.php` contains strings related FreshRSS installation"
msgid "`install.php` contains strings related to the installation"
msgstr ""
"`install.php` contient les phrases relatives à l’installation de FreshRSS ;"

#. type: Bullet: '* '
#: en/./internationalization.md:19
#, fuzzy
#| msgid "`sub.php` for subscription management (sub for \"subscription\")"
msgid "`sub.php` for subscription management (`sub` stands for “subscription”)"
msgstr ""
"`sub.php` pour l’aspect gestion des abonnements (sub pour « subscription »)."

#. type: Bullet: '* '
#: en/./internationalization.md:19
#, fuzzy
#| msgid "`install.php` contains strings related FreshRSS installation"
msgid "`user.php` contains some strings related to the User model"
msgstr ""
"`install.php` contient les phrases relatives à l’installation de FreshRSS ;"

#. type: Plain text
#: en/./internationalization.md:21
msgid ""
"This organization makes it possible to avoid a single huge translation file."
msgstr ""
"Cette organisation permet de ne pas avoir un unique énorme fichier de "
"traduction."

#. type: Plain text
#: en/./internationalization.md:23
#, fuzzy
#| msgid ""
#| "The translation files are quite simple: it’s only a matter of returning a "
#| "PHP table containing the translations. As an example, here’s an extract "
#| "from `app/i18n/fr/gen.php`:"
msgid ""
"The translation files are quite simple: it’s only a matter of returning a "
"PHP array containing the translations. As an example, here’s an extract from "
"[`app/i18n/fr/gen.php`](/app/i18n/fr/gen.php):"
msgstr ""
"Les fichiers de traduction sont assez simples : il s’agit seulement de "
"retourner un tableau PHP contenant les traductions. Extrait du fichier `app/"
"i18n/fr/gen.php` :"

#. type: Fenced code block (php)
#: en/./internationalization.md:24
#, fuzzy, no-wrap
#| msgid ""
#| "<?php\n"
#| "\n"
#| "return array(\n"
#| "\t'action' => [\n"
#| "\t\t'actualize' => 'Actualiser',\n"
#| "\t\t'back_to_rss_feeds' => '← Retour à vos flux RSS',\n"
#| "\t\t'cancel' => 'Annuler',\n"
#| "\t\t'create' => 'Créer',\n"
#| "\t\t'disable' => 'Désactiver',\n"
#| "\t),\n"
#| "\t'freshrss' => array(\n"
#| "\t\t'_' => 'FreshRSS',\n"
#| "\t\t'about' => 'À propos de FreshRSS',\n"
#| "\t),\n"
#| "];\n"
#| "\n"
#| "?>\n"
msgid ""
"<?php\n"
"return array(\n"
"\t'action' => [\n"
"\t\t'actualize' => 'Actualiser',\n"
"\t\t'back_to_rss_feeds' => '← Retour à vos flux RSS',\n"
"\t\t'cancel' => 'Annuler',\n"
"\t\t'create' => 'Créer',\n"
"\t\t'disable' => 'Désactiver',\n"
"\t),\n"
"\t'freshrss' => array(\n"
"\t\t'_' => 'FreshRSS',\n"
"\t\t'about' => 'À propos de FreshRSS',\n"
"\t),\n"
"\t// ...\n"
");\n"
msgstr ""
"<?php\n"
"\n"
"return array(\n"
"\t'action' => [\n"
"\t\t'actualize' => 'Actualiser',\n"
"\t\t'back_to_rss_feeds' => '← Retour à vos flux RSS',\n"
"\t\t'cancel' => 'Annuler',\n"
"\t\t'create' => 'Créer',\n"
"\t\t'disable' => 'Désactiver',\n"
"\t),\n"
"\t'freshrss' => array(\n"
"\t\t'_' => 'FreshRSS',\n"
"\t\t'about' => 'À propos de FreshRSS',\n"
"\t),\n"
"];\n"
"\n"
"?>\n"

#. type: Plain text
#: en/./internationalization.md:43
#, fuzzy
#| msgid ""
#| "The string to pass to the `_t()` function consists of a series of "
#| "identifiers separated by dots. The first identifier indicates from which "
#| "file to extract the translation (in this case, `gen.php`), while the "
#| "following ones indicate table entries. Thus `action` is an entry of the "
#| "main array and `back_to_rss_feeds` is an entry of the `action` array. "
#| "This allows us to further organize our translation files."
msgid ""
"Each value can be referenced by a key: it consists of a series of "
"identifiers separated by dots. The first identifier indicates from which "
"file to extract the translation, while the following ones indicate array "
"entries. Thus, the `gen.freshrss.about` key is referencing the `about` entry "
"from the `freshrss` entry which is part of the main array returned by the "
"`gen.php` file. This allows us to further organize our translation files."
msgstr ""
"La chaîne à passer à la fonction `_t()` consiste en une série d’identifiants "
"séparés par des points. Le premier identifiant indique de quel fichier on "
"veut extraire la traduction (dans notre cas présent, de `gen.php`), tandis "
"que les suivantes indiquent des entrées de tableaux. Ainsi `action` est une "
"entrée du tableau principal et `back_to_rss_feeds` est une entrée du tableau "
"`action`. Cela permet d’organiser encore un peu plus nos fichiers de "
"traduction."

#. type: Plain text
#: en/./internationalization.md:45
msgid ""
"You should not have to write the array by yourself and we provide several "
"commands to ease the manipulation of these files. Let’s see some common use "
"cases."
msgstr ""

#. type: Title ##
#: en/./internationalization.md:46
#, no-wrap
msgid "Add support for a new language"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:49
msgid ""
"If you want to add support for a language which isn’t supported by FreshRSS "
"yet, you can run this command:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./internationalization.md:50
#, no-wrap
msgid "make i18n-add-language lang=[your language code]\n"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:55
msgid ""
"You must replace `[your language code]` by the language tag of your "
"language. It must follow the [IETF BCP 47 standard](https://en.wikipedia.org/"
"wiki/IETF_language_tag). For instance, English is `en` and French is `fr`. "
"You can target a specific region with a subtag, for instance `pt-br` for "
"Brazilian Portuguese. If you’re not sure of the code, Wikipedia might be a "
"good start to find it or you can ask us for help too."
msgstr ""

#. type: Plain text
#: en/./internationalization.md:57
msgid ""
"The command will create a new subfolder under `app/i18n/` and copy the "
"strings from the reference language (i.e. English). It will also mark all "
"the translations with a special tag represented by a comment: `// TODO - "
"Translation`. We’ll see in the next section how to translate the strings."
msgstr ""

#. type: Title ##
#: en/./internationalization.md:58
#, no-wrap
msgid "Translate the interface"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:61
msgid ""
"You might have noticed some strings are not yet translated from English even "
"though you’ve selected a different language. This is because we mostly speak "
"English or French and it’s pretty difficult to us to speak all the different "
"languages!"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:63
msgid ""
"To update a string, you just have to open its file, find the string, and "
"change it (without removing the quotes around it!) You might want to remove "
"the comment at the end of the line, but you should prefer to use the "
"following command:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./internationalization.md:64
#, no-wrap
msgid "make i18n-format\n"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:69
msgid ""
"It will remove the comments on the lines that you’ve changed, and will "
"reformat the file correctly. If you’ve made any mistakes, it will fix them "
"automatically or it will tell you it can’t (well… the command will "
"dramatically fail without any damage, don’t worry)."
msgstr ""

#. type: Plain text
#: en/./internationalization.md:71
msgid ""
"The strings to translate can be easily found in the translations files "
"thanks to the tag we spoke about at the end of the previous section. Indeed, "
"it indicates to our tools that the strings are not translated yet. This "
"means you can find them with Git. For instance for the Greek language:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./internationalization.md:72
#, no-wrap
msgid "git grep TODO app/i18n/he\n"
msgstr ""

#. type: Title ##
#: en/./internationalization.md:76
#, no-wrap
msgid "Acknowledge a false-positive"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:79
msgid ""
"Our tool detects if a string needs to be translated if it equals to the "
"English version. For instance, the word “version” is the same in English and "
"French. Thus, our tool would mark the French word to be translated. This is, "
"in fact, the case for the `index.about.version` key. This case is considered "
"as a false-positive because the word _is_ actually translated. To acknowledge "
"such translations, you can run:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./internationalization.md:80
#, no-wrap
msgid "make i18n-ignore-key lang=fr key=index.about.version\n"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:85
msgid ""
"This command adds an IGNORE comment on the translation so the key can be "
"considered as translated."
msgstr ""

#. type: Title ##
#: en/./internationalization.md:86
#, no-wrap
msgid "Add/remove/update a key"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:89
msgid ""
"If you’re developing a new part of the application, you might want to "
"declare a new translation key. Your first impulse would be to add the key to "
"each file manually: don’t do that, it’s very painful. We provide another "
"command:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./internationalization.md:90
#, no-wrap
msgid "make i18n-add-key key=the.key.to.add value='Your string in English'\n"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:95
msgid ""
"This adds the key to all the files. It’ll be in English, waiting for other "
"translators."
msgstr ""

#. type: Plain text
#: en/./internationalization.md:97
msgid ""
"Conversely, you may want to remove a key that is no longer used in the "
"application with:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./internationalization.md:98
#, no-wrap
msgid "make i18n-remove-key key=the.key.to.remove\n"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:103
msgid ""
"Finally, if the English version of a string needs to be changed, you need to "
"consider two cases. If the change doesn’t impact the meaning of the "
"sentence, and therefore other languages don’t need to change (e.g. to fix a "
"typo), you should make the change manually in the file. In any other case, "
"you should use the following command:"
msgstr ""

#. type: Fenced code block (sh)
#: en/./internationalization.md:104
#, no-wrap
msgid "make i18n-update-key key=the.key.to.change value='The new string in English'\n"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:109
msgid "The key will simply be removed and added back with the new value."
msgstr ""

#. type: Title ##
#: en/./internationalization.md:110
#, no-wrap
msgid "How to access a translation programmatically"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:113
msgid ""
"To access these translations, you must use the `_t()` function (which is a "
"shortcut for `Minz_Translate::t()`). Code example:"
msgstr ""

#. type: Fenced code block (html)
#: en/./internationalization.md:114
#, no-wrap
msgid ""
"<p>\n"
"\t<?= _t('gen.freshrss.about') ?>\n"
"</p>\n"
msgstr ""

#. type: Plain text
#: en/./internationalization.md:121
#, fuzzy
#| msgid ""
#| "There is a small special case that sometimes makes life easier: the `_` "
#| "identifier. This must necessarily be present at the end of the chain and "
#| "gives a value to the higher-level identifier. It’s pretty hard to explain "
#| "but very simple to understand. In the example given above, a `_` is "
#| "associated with the value `FreshRSS`: this means that there is no need to "
#| "write `_t('gen.freshrss._')` but `_t('gen.freshrss')` suffices."
msgid ""
"The function expects a translation key, but there’s a special case that "
"sometimes makes life easier: the `_` identifier. This must necessarily be "
"present at the end of the chain and gives a value to the higher-level "
"identifier. It’s pretty hard to explain but very simple to understand. In "
"the example given above, an `_` is associated with the value `FreshRSS`: "
"this means that there is no need to write `_t('gen.freshrss._')` but "
"`_t('gen.freshrss')` suffices."
msgstr ""
"Il existe un petit cas particulier qui permet parfois de se simplifier la "
"vie : le cas de l’identifiant `_`. Celui-ci doit nécessairement être présent "
"en bout de chaîne et permet de donner une valeur à l’identifiant de niveau "
"supérieur. C’est assez dur à expliquer mais très simple à comprendre. Dans "
"l’exemple donné plus haut, un `_` est associé à la valeur `FreshRSS` : cela "
"signifie qu’il n’y a pas besoin d’écrire `_t('gen.freshrss._')` mais "
"`_t('gen.freshrss')` suffit."

#. type: Plain text
#: en/./internationalization.md:123
msgid ""
"`_t()` can take any number of variables. The variables will then be replaced "
"in the translation if it contains some “conversion specifications” (usually "
"`%s` or `%d`). You can learn more about these specifications in the "
"[`sprintf()` PHP function documentation](https://www.php.net/manual/function."
"sprintf)."
msgstr ""

#. type: Plain text
#: en/./internationalization.md:125
#, no-wrap
msgid "For instance, the English translation for `gen.auth.keep_logged_in` is `Keep me logged in <small>(%s days)</small>`. It means this translation expects a string to be passed as an argument to the `t()` function (well, it should be a `%d` because we want a number here, but it doesn’t matter). For instance:\n"
msgstr ""

#. type: Fenced code block (php)
#: en/./internationalization.md:126
#, no-wrap
msgid ""
"<label>\n"
"\t<input type=\"checkbox\" name=\"keep_logged_in\" />\n"
"\t<?= _t('gen.auth.keep_logged_in', 30) ?>\n"
"</label>\n"
msgstr ""

#. type: Plain text
#: en/./users/02_First_steps.md:2
msgid ""
"Learning how to handle a new application is not always easy. We’ve tried to "
"make FreshRSS as intuitive as possible, but you might still need a little "
"help to master the program."
msgstr ""
"Découvrir un nouveau logiciel n’est pas toujours facile. Si nous avons voulu "
"FreshRSS le plus intuitif possible, vous aurez peut-être besoin d’un coup de "
"main pour le maîtriser."

#. type: Plain text
#: en/./users/02_First_steps.md:4
msgid ""
"This section will guide you to the pages you need to get started. The order "
"is tailored to newcomers."
msgstr ""
"Cette section se propose de vous aider dans la prise en main de l’outil. Il "
"ne s’agit que de liens menant vers les autres pages de la documentation mais "
"ordonnées dans un ordre spécifique aux nouveaux arrivants."

#. type: Plain text
#: en/./users/02_First_steps.md:6
msgid ""
"[After installing the application](../admins/03_Installation.md), the first "
"step is to add some feeds. You have a few options:"
msgstr ""
"[Après l’installation](../../en/admins/03_Installation.md), la première "
"chose à faire est d’ajouter un ou plusieurs sites à suivre. Pour cela "
"plusieurs choix s’offrent à vous :"

#. type: Bullet: '1. '
#: en/./users/02_First_steps.md:10
msgid "[Add a feed manually](04_Subscriptions.md#adding-a-feed)"
msgstr "[Ajouter un flux manuellement](04_Subscriptions.md#ajouter-un-flux)"

#. type: Bullet: '2. '
#: en/./users/02_First_steps.md:10
msgid "[Import an OPML or JSON file](04_Subscriptions.md#import-and-export)"
msgstr ""
"[Importer un fichier OPML ou JSON](04_Subscriptions.md#import-et-export)"

#. type: Bullet: '3. '
#: en/./users/02_First_steps.md:10
msgid "[Use the bookmarklet](04_Subscriptions.md#use-bookmarklet)"
msgstr "[Utiliser le bookmark dédié](04_Subscriptions.md#utiliser-le-bookmark)"

#. type: Plain text
#: en/./users/02_First_steps.md:12
#, fuzzy
#| msgid ""
#| "Once you have added your feeds to FreshRSS, it is time to read them. "
#| "There are three availalbe reading modes:"
msgid ""
"Once you have added your feeds to FreshRSS, it is time to read them. There "
"are three available reading modes:"
msgstr ""
"Une fois que vous avez ajouté vos flux à FreshRSS, il est temps de les lire. "
"Pour cela, trois modes de lecture s’offrent à vous :"

#. type: Bullet: '1. '
#: en/./users/02_First_steps.md:16
msgid ""
"[The normal view](03_Main_view.md#normal-view) enables you to quickly read "
"new articles"
msgstr ""
"[La vue normale](03_Main_view.md#la-vue-normale) qui permet de voir et de "
"lire rapidement les nouveaux articles"

#. type: Bullet: '2. '
#: en/./users/02_First_steps.md:16
msgid ""
"[The global view](03_Main_view.md#global-view) shows you an overview of the "
"status of your feeds in one glance"
msgstr ""
"[La vue globale](03_Main_view.md#la-vue-globale) est destinée à vous offrir "
"un panorama de l’état de vos flux"

#. type: Bullet: '3. '
#: en/./users/02_First_steps.md:16
msgid ""
"[The reader view](03_Main_view.md#reader-view) offers you a comfortable "
"reading experience"
msgstr ""
"[La vue lecture](03_Main_view.md#la-vue-lecture) est pensée pour vous offrir "
"un meilleur confort de lecture"

#. type: Plain text
#: en/./users/02_First_steps.md:18
msgid ""
"Now that you’ve mastered basic use, it’s time to configure FreshRSS to "
"improve your reading experience. It’s highly configurable, so it’s "
"recommended to play around with them to find a configuration that suits you "
"well. Here are a few resources to help you improve your daily FreshRSS "
"experience:"
msgstr ""
"Bien, vous maitrisez maintenant la vue que vous préférez ? Il est temps de "
"vous offrir un peu plus de confort de lecture. FreshRSS est grandement "
"configurable et c’est à vous de trouver la configuration qui vous conviendra "
"le plus. Voici tout de même quelques pistes pour améliorer votre quotidien "
"sur FreshRSS :"

#. type: Bullet: '* '
#: en/./users/02_First_steps.md:27
msgid ""
"[Organize your feeds in categories](04_Subscriptions.md#feed-management)"
msgstr ""
"[Rangez vos flux dans des catégories](04_Subscriptions."
"md#organisation_des_flux)"

#. type: Bullet: '* '
#: en/./users/02_First_steps.md:27
msgid "[Change the home page](05_Configuration.md#changing-the-view)"
msgstr ""
"[Configurez votre page d’accueil](05_Configuration.md#personnaliser-la-vue)"

#. type: Bullet: '* '
#: en/./users/02_First_steps.md:27
msgid "[Choose the reading options](05_Configuration.md#reading-options)"
msgstr ""
"[Configurez vos options de lecture](05_Configuration.md#options-de-lecture)"

#. type: Bullet: '* '
#: en/./users/02_First_steps.md:27
#, fuzzy
#| msgid "[Refresh feeds](03_Main_view.md#refreshing-feeds)"
msgid "[Refresh feeds](09_refreshing_feeds.md)"
msgstr "[Mettez à jour vos flux](03_Main_view.md#rafraichir-les-flux)"

#. type: Bullet: '* '
#: en/./users/02_First_steps.md:27
#, fuzzy
#| msgid ""
#| "[Filter articles](03_Main_view.md#filtering-articles) for a fast access "
#| "to a selection"
msgid "[Filter articles](10_filter.md) for a fast access to a selection"
msgstr ""
"[Filtrez les articles](03_Main_view.md#filtrer-les-articles) pour accéder "
"rapidement à ceux que vous voulez lire en priorité"

#. type: Bullet: '* '
#: en/./users/02_First_steps.md:27
#, fuzzy
#| msgid ""
#| "[search for an article](03_Main_view.md#with-the-search-field) published "
#| "some time ago"
msgid ""
"[search for an article](10_filter.md#with-the-search-field) published some "
"time ago"
msgstr ""
"[Retrouvez un article](03_Main_view.md#rechercher-des-articles) qui a été "
"publié il y a quelques jours ou mois"

#. type: Bullet: '* '
#: en/./users/02_First_steps.md:27
msgid "[Access your feeds on a mobile device](06_Mobile_access.md)"
msgstr "[Accédez à vos flux même sur mobile](06_Mobile_access.md)"

#. type: Bullet: '* '
#: en/./users/02_First_steps.md:27
msgid "[Add some extensions](https://github.com/FreshRSS/Extensions)"
msgstr "[Ajoutez quelques extensions](https://github.com/FreshRSS/Extensions)"

#. type: Bullet: '* '
#: en/./users/02_First_steps.md:27
msgid "[Frequently asked questions](07_Frequently_Asked_Questions.md)"
msgstr "[Foire aux questions](07_Frequently_Asked_Questions.md)"

#. type: Plain text
#: en/./users/03_Main_view.md:4
msgid ""
"FreshRSS has three primary viewing modes: Normal, Global, and Reader view."
msgstr ""

#. type: Title ##
#: en/./users/03_Main_view.md:5
#, no-wrap
msgid "Normal view"
msgstr "La vue normale"

#. type: Plain text
#: en/./users/03_Main_view.md:8
msgid ""
"Normal view will allow you to view articles in a compressed view. They can "
"be separated by category or individual feed, or viewed in the \"main "
"stream\" containing all feeds. Clicking a feed in the sidebar (mobile users "
"will need to click the folder icon to open it) will open that feed’s view."
msgstr ""

#. type: Title ###
#: en/./users/03_Main_view.md:9
#, fuzzy, no-wrap
#| msgid "Article icons"
msgid "Article List"
msgstr "Icônes d’article"

#. type: Plain text
#: en/./users/03_Main_view.md:18
msgid ""
"By default, the normal view includes six items per article. From left to "
"right: * **Read status:** An envelope icon to show if the article has been "
"read or not. Closed envelopes are unread, open envelopes are read. Clicking "
"on the icon will toggle the read status.  * **Favourite status:** A star "
"icon to show if the article has been favourited or not. Filled stars are "
"favourited, empty stars are not. Clicking on the icon will toggle the "
"favourite status.  * **Feed name:** The name of the feed that the article is "
"from. Clicking the feed name will move to that feed’s view in normal view.  "
"* **Article title:** The title of the article. Clicking will open the "
"article for viewing within FreshRSS.  * **Article date/time:** The time the "
"article was posted.  * **Link to original article:** A globe icon that can "
"be clicked to go to the article on the original website."
msgstr ""

#. type: Title ###
#: en/./users/03_Main_view.md:19
#, fuzzy, no-wrap
#| msgid "Normal view"
msgid "Normal View Sidebar"
msgstr "La vue normale"

#. type: Plain text
#: en/./users/03_Main_view.md:28
msgid ""
"Clicking the gear icon next to an individual feed will display additional "
"options for that feed.  * **Filter:** Run the defined filter to mark "
"articles as read * **Statistics:** View statistics about the feed * **See "
"website:** Open the feed’s website in another tab * **Manage:** Configure "
"the feed * **Actualize:** Force-update the feed * **Mark as read:** Mark all "
"items in the feed as read"
msgstr ""

#. type: Title ##
#: en/./users/03_Main_view.md:29
#, no-wrap
msgid "Global view"
msgstr "La vue globale"

#. type: Plain text
#: en/./users/03_Main_view.md:32
msgid ""
"Global view allows quick views of feed’s statuses at once. Feeds and "
"categories are shown with the number of unread articles next to them. "
"Clicking a feed’s name will open it in a view similar to normal view."
msgstr ""

#. type: Title ##
#: en/./users/03_Main_view.md:33
#, no-wrap
msgid "Reader view"
msgstr "La vue lecture"

#. type: Plain text
#: en/./users/03_Main_view.md:36
msgid ""
"Reader view will display a feed will all articles already open for reading. "
"Feeds can be switched by clicking the folder icon at the top to bring up the "
"category/feed sidebar."
msgstr ""

#. type: Plain text
#: en/./users/03_Main_view.md:40
msgid ""
"Read more: * [Refreshing the feeds](./09_refreshing_feeds.md)  * [Filter the "
"feeds and search](./10_filter.md)"
msgstr ""

#. type: Title #
#: en/./users/04_Subscriptions.md:1
#, no-wrap
msgid "Adding a feed"
msgstr "Ajouter un flux"

#. type: Bullet: '1. '
#: en/./users/04_Subscriptions.md:10
msgid ""
"To add a feed, copy the URL of its RSS or Atom file (for instance, the "
"Framablog RSS URL is `https://framablog.org/feed/`). FreshRSS is able to "
"automatically find the address of the feed for websites that are declaring "
"it in a standard way."
msgstr ""

#. type: Bullet: '2. '
#: en/./users/04_Subscriptions.md:10
msgid ""
"In FreshRSS, click the \"**+**\" button next to “Subscriptions management”."
msgstr ""

#. type: Bullet: '3. '
#: en/./users/04_Subscriptions.md:10
msgid "Paste the URL in the “Feed URL” field."
msgstr ""

#. type: Bullet: '4. '
#: en/./users/04_Subscriptions.md:10
msgid ""
"(optional): You can select the category for your feed. By default, it will "
"be in “Uncategorized”."
msgstr ""

#. type: Bullet: '5. '
#: en/./users/04_Subscriptions.md:10
msgid ""
"(optional): If the subscription requires credentials, you can enter them in "
"the \"HTTP username\" and \"HTTP password\" fields."
msgstr ""

#. type: Bullet: '6. '
#: en/./users/04_Subscriptions.md:10
msgid "(optional): You can set a timeout for the feed request."
msgstr ""

#. type: Bullet: '7. '
#: en/./users/04_Subscriptions.md:10
msgid ""
"(optional): You can choose to ignore SSL certificate errors (such as with "
"self-signed certificates) by setting \"Verify SSL security\" to \"No\". This "
"is not recommended, and it is better to either add the root certificate to "
"the FreshRSS server or to fix the SSL certificate problems on the feed "
"hosting server."
msgstr ""

#. type: Title ##
#: en/./users/04_Subscriptions.md:11
#, no-wrap
msgid "Subscription management"
msgstr "Gestion des flux"

#. type: Plain text
#: en/./users/04_Subscriptions.md:14
msgid ""
"The \"Subscription management\" submenu allows categories and feeds to be "
"configured. Feeds can be moved between categories by drag-and-drop, or in "
"the individual feed’s settings. Hovering over a feed/category will cause a "
"gear icon to appear. Clicking the icon will bring up the settings for that "
"item."
msgstr ""

#. type: Title ##
#: en/./users/04_Subscriptions.md:15
#, no-wrap
msgid "Category Settings"
msgstr ""

#. type: Title ###
#: en/./users/04_Subscriptions.md:17
#, no-wrap
msgid "Information"
msgstr "Informations"

#. type: Bullet: '* '
#: en/./users/04_Subscriptions.md:21
msgid "**Title:** Name of category"
msgstr ""

#. type: Bullet: '* '
#: en/./users/04_Subscriptions.md:21
msgid ""
"**Display position:** Defines the order of categories. Lower numbers get "
"priority, non-numbered items come last, and equally numbered items will sort "
"by alphabetical order."
msgstr ""

#. type: Title ##
#: en/./users/04_Subscriptions.md:22 en/./users/05_Configuration.md:115
#, fuzzy, no-wrap
#| msgid "Archival"
msgid "Archiving"
msgstr "Archivage"

#. type: Plain text
#: en/./users/04_Subscriptions.md:25
msgid ""
"If \"Purge Policy\" has \"By default\" selected, then the [default purge "
"policy](./05_Configuration.md) is used and the other options are not "
"displayed. Category options will override the default policy, but they will "
"not override feed-specific options."
msgstr ""

#. type: Title ##
#: en/./users/04_Subscriptions.md:26
#, no-wrap
msgid "Feed Settings"
msgstr ""

#. type: Plain text
#: en/./users/04_Subscriptions.md:29
msgid ""
"These fields will be auto-filled when adding a feed, but they can be "
"modified later. **Visibility** will define if the feed is displayed in the "
"main feed, only in specific categories, or not at all."
msgstr ""

#. type: Title #
#: en/./users/04_Subscriptions.md:30 en/./users/05_Configuration.md:113
#, no-wrap
msgid "Archival"
msgstr "Archivage"

#. type: Plain text
#: en/./users/04_Subscriptions.md:33
msgid ""
"This section will let you override the default settings for feed archiving "
"and update frequency."
msgstr ""

#. type: Title ###
#: en/./users/04_Subscriptions.md:34
#, no-wrap
msgid "Login"
msgstr "Identification"

#. type: Plain text
#: en/./users/04_Subscriptions.md:37
msgid ""
"Some feeds require a username/password submitted over HTTP. These usually "
"aren’t needed for feeds."
msgstr ""

#. type: Title ###
#: en/./users/04_Subscriptions.md:38
#, no-wrap
msgid "Advanced"
msgstr "Avancé"

#. type: Title ####
#: en/./users/04_Subscriptions.md:40
#, fuzzy, no-wrap
#| msgid "Retrieve a truncated stream from within FreshRSS"
msgid "Retrieve a truncated feed from within FreshRSS"
msgstr "Récupérer un flux tronqué à partir de FreshRSS"

#. type: Plain text
#: en/./users/04_Subscriptions.md:43
#, fuzzy
#| msgid ""
#| "This question comes up regularly, so we will try to clarify how one can "
#| "retrieve a truncated RSS feed with FreshRSS. Please note that the process "
#| "is absolutely not user friendly, but it works. :)"
msgid ""
"This question comes up regularly, so we’ll try to clarify how one can "
"retrieve a truncated RSS feed with FreshRSS. Please note that the process is "
"absolutely not user friendly, but it works. :)"
msgstr ""
"La question revient régulièrement, je vais essayer de clarifier ici comment "
"on peut récupérer un flux RSS tronqué avec FreshRSS. Sachez avant tout que "
"la manière de s’y prendre n’est absolument pas \"user friendly\", mais elle "
"fonctionne. :)"

#. type: Plain text
#: en/./users/04_Subscriptions.md:45
msgid ""
"Please be aware that this way you’ll generate much more traffic to the "
"originating sites, and they might block you accordingly. FreshRSS "
"performance is also negatively affected, because you’ll have to fetch the "
"full article content one by one. So it’s a feature to use sparingly!"
msgstr ""
"Sachez aussi que par cette manière vous générez beaucoup plus de trafic vers "
"les sites d’origines et qu’ils peuvent vous bloquer par conséquent. Les "
"performances de FreshRSS sont aussi moins bonnes car vous devez alors aller "
"chercher le contenu des articles un par un. C’est donc une fonctionnalité à "
"utiliser avec parcimonie !"

#. type: Plain text
#: en/./users/04_Subscriptions.md:47
#, fuzzy
#| msgid ""
#| "What’s meant by \"CSS path of articles on the original site\" actually "
#| "corresponds to the \"path\" consisting of IDs and classes (which in HTML, "
#| "matches the id and class attributes) to retrieve only the interesting "
#| "part that corresponds to the article. Ideally, this path starts with an "
#| "id (which is unique to the page)."
msgid ""
"The \"Article CSS selector on original website\" corresponds to the \"path\" "
"consisting of IDs and classes (which in HTML, matches the id and class "
"attributes) to retrieve only the interesting part that corresponds to the "
"article. Ideally, this path starts with an id (which is unique to the page). "
"The basics are explained [here](https://developer.mozilla.org/en-US/docs/"
"Learn/CSS/Building_blocks/Selectors)."
msgstr ""
"Ce que j’entends par \"Chemin CSS des articles sur le site d’origine\" "
"correspond en fait au \"chemin\" constitué par les IDs et les classes (en "
"html, correspond aux attributs id et class) pour récupérer uniquement la "
"partie intéressante qui correspond à l’article. L’idéal est que ce chemin "
"commence par un id (qui est unique pour la page)."

#. type: Title #####
#: en/./users/04_Subscriptions.md:48
#, no-wrap
msgid "Example: Rue89"
msgstr "Exemple : Rue89"

#. type: Plain text
#: en/./users/04_Subscriptions.md:52
#, fuzzy
#| msgid ""
#| "To find this path, you have to go to the address of one of the truncated "
#| "articles (for example, http://www.rue89.com/2013/10/15/prof-maths-jai-"
#| "atteint-lextase-dihn-pedagogie-inversee-246635). You look have to look "
#| "for the \"block\" of HTML that corresponds to article content (in the "
#| "source code!)."
msgid ""
"To find this path, you have to go to the address of one of the truncated "
"articles.  You look have to look for the \"block\" of HTML that corresponds "
"to article content (in the source code!)."
msgstr ""
"Pour trouver ce chemin, il faut se rendre à l’adresse d’un des articles "
"tronqués (par exemple http://www.rue89.com/2013/10/15/prof-maths-jai-atteint-"
"lextase-dihn-pedagogie-inversee-246635). Il faut alors chercher le \"bloc\" "
"HTML correspondant au contenu de l’article (dans le code source !)"

#. type: Plain text
#: en/./users/04_Subscriptions.md:54
#, no-wrap
msgid "Here we find that the block that encompasses nothing but the content of the article is ```<div class=\"content clearfix\">```. We’ll only use the `.content` class here. Nevertheless, as said above, it’s best to start the path with an id. If we go back to the parent block, we find ```<div id=\"article\">``` and that’s perfect! The path will be ```#article .content```.\n"
msgstr "On trouve ici que le bloc qui englobe uniquement le contenu de l’article est ```<div class=\"content clearfix\">```. On ne va garder que la classe `.content` ici. Néanmoins, comme je le disais plus haut, il est préférable de commencer le chemin avec un id. Si on remonte au bloc parent, il s’agit du bloc ```<div id=\"article\">``` et c’est parfait ! Le chemin sera donc ```#article .content```.\n"

#. type: Title #####
#: en/./users/04_Subscriptions.md:55
#, fuzzy, no-wrap
#| msgid "Add the corresponding classes to the article CSS path on the feed configuration page. Examples:"
msgid "Add the corresponding classes to the article CSS path on the feed configuration page"
msgstr "Liste de correspondances site → chemin css"

#. type: Plain text
#: en/./users/04_Subscriptions.md:58
#, fuzzy
#| msgid "Example: Rue89"
msgid "Examples:"
msgstr "Exemple : Rue89"

#. type: Bullet: '* '
#: en/./users/04_Subscriptions.md:63
msgid "Rue89: ```#article .content```"
msgstr "Rue89 : ```#article .content```"

#. type: Bullet: '* '
#: en/./users/04_Subscriptions.md:63
msgid "PCINpact: ```#actu_content```"
msgstr "PCINpact : ```#actu_content```"

#. type: Bullet: '* '
#: en/./users/04_Subscriptions.md:63
msgid "Lesnumériques: ```article#body div.text.clearfix```"
msgstr "Lesnumériques : ```article#body div.text.clearfix```"

#. type: Bullet: '* '
#: en/./users/04_Subscriptions.md:63
msgid "Phoronix: ```#main .content```"
msgstr "Phoronix : ```#main .content```"

#. type: Title #####
#: en/./users/04_Subscriptions.md:64
#, no-wrap
msgid "Combining CSS Classes"
msgstr ""

#. type: Plain text
#: en/./users/04_Subscriptions.md:67
msgid ""
"Let’s say we have an article which contains ads, and we do not want to have "
"those ads retrieved by FreshRSS. Example HTML:"
msgstr ""

#. type: Fenced code block (html)
#: en/./users/04_Subscriptions.md:68
#, no-wrap
msgid ""
"<div id=\"article\">\n"
"<h2>wanted</h2>\n"
"<p class=\"content\">wanted content</p>\n"
"<p class=\"ad\">unwanted content</p>\n"
"<h2>wanted</h2>\n"
"<p class=\"content\">wanted content</p>\n"
"<h2>wanted</h2>\n"
"<p class=\"ad\">unwanted content</p>\n"
"<p class=\"content\">wanted content</p>\n"
"</div>\n"
msgstr ""

#. type: Plain text
#: en/./users/04_Subscriptions.md:82
msgid ""
"In this case it’s possible to combine multiple CSS selectors with a comma: "
"```#article p.content, #article h2```"
msgstr ""

#. type: Title ####
#: en/./users/04_Subscriptions.md:83
#, fuzzy, no-wrap
#| msgid "Retrieve a truncated stream with external tools"
msgid "Retrieve a truncated feed with external tools"
msgstr "Récupérer un flux tronqué à l’aide d’outils externes"

#. type: Plain text
#: en/./users/04_Subscriptions.md:86
#, fuzzy
#| msgid ""
#| "Complimentary tools can be used to retrieve full article content, such as:"
msgid ""
"Complementary tools can be used to retrieve full article content, such as:"
msgstr ""
"Des outils complémentaires peuvent être utilisés pour récupérer le contenu "
"complet d’un article, comme :"

#. type: Bullet: '* '
#: en/./users/04_Subscriptions.md:89
msgid "[RSS-Bridge](https://github.com/RSS-Bridge/rss-bridge)"
msgstr "[RSS-Bridge](https://github.com/RSS-Bridge/rss-bridge)"

#. type: Bullet: '* '
#: en/./users/04_Subscriptions.md:89
msgid "[Full-Text RSS](https://bitbucket.org/fivefilters/full-text-rss)"
msgstr "[Full-Text RSS](https://bitbucket.org/fivefilters/full-text-rss)"

#. type: Title ###
#: en/./users/04_Subscriptions.md:90
#, no-wrap
msgid "Filter"
msgstr ""

#. type: Plain text
#: en/./users/04_Subscriptions.md:93
msgid ""
"Articles can be automatically marked as read based on some search terms. See "
"[filtering](./10_filter.md) for more information on how to create these "
"filters."
msgstr ""

#. type: Title ##
#: en/./users/04_Subscriptions.md:94
#, fuzzy, no-wrap
#| msgid "Import and export"
msgid "Import / export"
msgstr "Import et export"

#. type: Plain text
#: en/./users/04_Subscriptions.md:97
msgid ""
"See [SQLite export/import]( https://github.com/FreshRSS/FreshRSS/tree/edge/"
"cli) as an alternative."
msgstr ""

#. type: Title ##
#: en/./users/04_Subscriptions.md:98
#, no-wrap
msgid "Export"
msgstr ""

#. type: Bullet: '1. '
#: en/./users/04_Subscriptions.md:108
msgid "To export your list of feeds, go to “Subscriptions management”."
msgstr ""

#. type: Bullet: '2. '
#: en/./users/04_Subscriptions.md:108
msgid "Click on “Import / export”"
msgstr ""

#. type: Bullet: '3. '
#: en/./users/04_Subscriptions.md:108
msgid "You can select for your export:"
msgstr ""

#. type: Bullet: '	1. '
#: en/./users/04_Subscriptions.md:108
msgid "the list of feeds"
msgstr ""

#. type: Bullet: '	2. '
#: en/./users/04_Subscriptions.md:108
#, fuzzy
#| msgid "Filtering articles"
msgid "labelled articles"
msgstr "Filtrer les articles"

#. type: Bullet: '	3. '
#: en/./users/04_Subscriptions.md:108
#, fuzzy
#| msgid "Filtering articles"
msgid "favourite articles"
msgstr "Filtrer les articles"

#. type: Bullet: '	4. '
#: en/./users/04_Subscriptions.md:108
msgid ""
"and finally, you can select feeds you want to export (by default, all feeds "
"are selected)"
msgstr ""

#. type: Bullet: '4. '
#: en/./users/04_Subscriptions.md:108
msgid "Click on “export”."
msgstr ""

#. type: Title ##
#: en/./users/04_Subscriptions.md:109
#, no-wrap
msgid "Import"
msgstr ""

#. type: Bullet: '1. '
#: en/./users/04_Subscriptions.md:114
msgid "Go to the page “Import / export”."
msgstr ""

#. type: Bullet: '2. '
#: en/./users/04_Subscriptions.md:114
msgid ""
"Click on “Browse” and select your OPML or archive file on your computer."
msgstr ""

#. type: Bullet: '3. '
#: en/./users/04_Subscriptions.md:114
msgid "Click on “Import”"
msgstr ""

#. type: Plain text
#: en/./users/04_Subscriptions.md:122
#, no-wrap
msgid ""
"> **Important**: you can not import directly a list of feeds from a text file.\n"
"> You need to convert it beforehand to _OPML_.\n"
"> Here is some tools you could use :\n"
">\n"
"> * [Pandoc](https://pandoc.org/) available for most systems,\n"
"> * [OPML generator](https://opml-gen.ovh/) available online,\n"
"> * [txt2opml](https://alterfiles.com/convert/txt/opml) available online.\n"
msgstr ""

#. type: Title ##
#: en/./users/04_Subscriptions.md:123
#, no-wrap
msgid "Use bookmarklet"
msgstr "Utiliser le « bookmarklet »"

#. type: Plain text
#: en/./users/04_Subscriptions.md:126
msgid ""
"Bookmarklets are little scripts that you can execute to perform various "
"tasks. FreshRSS offers a bookmarklet for subscribing to newsfeeds."
msgstr ""
"Les « bookmarklets » sont de petits scripts que vous pouvez exécuter pour "
"effectuer des tâches diverses et variées. FreshRSS offre un signet "
"( « bookmark » ) pour s’abonner aux fils de nouvelles."

#. type: Bullet: '1. '
#: en/./users/04_Subscriptions.md:129
#, fuzzy
#| msgid "Open \"Subscriptions management\"."
msgid "Open “Subscriptions management”."
msgstr "Ouvrez \"Gestion des abonnements\"."

#. type: Bullet: '2. '
#: en/./users/04_Subscriptions.md:129
#, fuzzy
#| msgid "Click on \"Subscription tools\"."
msgid "Click on “Subscription tools”."
msgstr "Cliquez sur \"Outils d’abonnement\"."

#. type: Bullet: '3. '
#: en/./users/04_Subscriptions.md:129
#, fuzzy
#| msgid ""
#| "Drag the \"Subscribe\" button to your bookmark toolbar or right click and "
#| "choose your browser’s \"Bookmark link\" action."
msgid ""
"Drag the “Subscribe” button to your bookmark toolbar or right click and "
"choose your browser’s “Bookmark link” action."
msgstr ""
"Glissez le bouton \"S’abonner\" dans la barre d’outils des signets ou "
"cliquez droit et choisissez l’action \"Lien vers les signets\" de votre "
"navigateur."

#. type: Title #
#: en/./users/05_Configuration.md:2
#, no-wrap
msgid "Display"
msgstr "Personnaliser la vue"

#. type: Title ##
#: en/./users/05_Configuration.md:4
#, no-wrap
msgid "Language"
msgstr "Langue"

#. type: Plain text
#: en/./users/05_Configuration.md:9
#, fuzzy
#| msgid ""
#| "FreshRSS is currently available in 14 languages. After confirming your "
#| "choice, the interface will be displayed in your preferred language.  "
#| "Depending on the language chosen, parts of the interface may not be not "
#| "translated yet. If you’re willing to help translate the missing bits or "
#| "would like to add a new language, please take a look at how you can "
#| "[contribute to the project](../contributing.md#contribute-to-"
#| "internationalization-i18n)."
msgid ""
"FreshRSS is currently available in 22 languages. After confirming your "
"choice, the interface will be displayed in your preferred language.  "
"Depending on the language chosen, parts of the interface may not be not "
"translated yet. If you’re willing to help translate the missing bits or "
"would like to add a new language, please take a look at how you can "
"[contribute to the project](../contributing.md#contribute-to-"
"internationalization-i18n)."
msgstr ""
"À l’heure actuelle, FreshRSS est disponible en 13 langues. Après validation "
"de ce choix, l’interface sera affichée dans la langue choisie, même si "
"certaines parties de l’interface peuvent ne pas encore avoir été traduites. "
"Si vous voulez aider à la traduction, regardez comment vous pouvez "
"[contribuer au projet](../contributing.md#contribute-to-internationalization-"
"i18n)."

#. type: Plain text
#: en/./users/05_Configuration.md:11
#, fuzzy
#| msgid ""
#| "Some parts of FreshRSS aren’t translated and aren’t intended to be "
#| "translated either. For now, this includes the logs visible in the "
#| "application as well as the log generated by automatic update scripts."
msgid ""
"Some parts of FreshRSS are not translated and are not intended to be "
"translated either. For now, this includes the logs visible in the "
"application as well as the log generated by automatic update scripts."
msgstr ""
"Il y a des parties de FreshRSS qui ne sont pas traduites et qui n’ont pas "
"vocation à l’être. Pour le moment, les logs visibles dans l’application "
"ainsi que celle générées par le script de mise à jour automatique en font "
"partie."

#. type: Plain text
#: en/./users/05_Configuration.md:13
msgid "Available languages are:"
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:38
#, no-wrap
msgid ""
"| Language (English name) | Language (Endonym) | Ordered by language code (ISO-639-1) |\n"
"|:------------------------|:-----------------------|:-------------------------------------|\n"
"| Czech                   | Čeština                | cs                                   |\n"
"| German                  | Deutsch                | de                                   |\n"
"| Greek                   | Ελληνικά               | el                                   |\n"
"| English                 | English                | en                                   |\n"
"| English (United States) | English (United States) | en-us                               |\n"
"| Spanish                 | Español                | es                                   |\n"
"| French                  | Français               | fr                                   |\n"
"| Hebrew                  | עברית                  | he                                   |\n"
"| Indonesian              | Bahasa Indonesia       | id                                   |\n"
"| Italian                 | Italiano               | it                                   |\n"
"| Japanese                | 日本語                  | ja                                   |\n"
"| Korean                  | 한국어                  | ko                                   |\n"
"| Latvian                 | Latviešu               | lv                                   |\n"
"| Dutch                   | Nederlands             | nl                                   |\n"
"| Occitan                 | Occitan                | oc                                   |\n"
"| Polish                  | Polski                 | pl                                   |\n"
"| Brazilian Portuguese    | Português (Brasil)     | pt-br                                |\n"
"| Russian                 | Русский                | ru                                   |\n"
"| Slovak                  | Slovenčina             | sk                                   |\n"
"| Turkish                 | Türkçe                 | tr                                   |\n"
"| Chinese (Simplified, People’s Republic of China) | 简体中文      | zh-cn                 |\n"
"| Chinese (Traditional, Taiwan) | 正體中文          | zh-tw                                |\n"
msgstr ""

#. type: Title ##
#: en/./users/05_Configuration.md:39
#, no-wrap
msgid "Theme"
msgstr "Thème"

#. type: Plain text
#: en/./users/05_Configuration.md:42
#, fuzzy
#| msgid ""
#| "There’s no accounting for tastes, which is why FreshRSS offers eight "
#| "official themes:"
msgid ""
"There’s no accounting for tastes, which is why FreshRSS offers 13 official "
"themes:"
msgstr ""
"Les goûts et les couleurs, ça ne se discute pas. C’est pourquoi FreshRSS "
"propose huit thèmes officiels :"

#. type: Plain text
#: en/./users/05_Configuration.md:58
#, no-wrap
msgid ""
"| Theme       | designed by                                   | Notes                                                         |\n"
"|:--------------|:-------------------------------------------------------|:--------------------------------------------------------------|\n"
"| Alternative Dark | Ghost | |\n"
"| Ansum | Thomas Guesnon  | |\n"
"| Dark | AD | |\n"
"| Dark pink | Miicat_47 | |\n"
"| Flat design | Marien Fressinaud | |\n"
"| Mapco | Thomas Guesnon  | |\n"
"| Nord theme | joelchrono12 | |\n"
"| Origine | Marien Fressinaud | (default theme) |\n"
"| Origine-compact | Kevin Papst | |\n"
"| Pafat | Plopoyop | |\n"
"| Swage | Patrick Crandol | |\n"
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:60
msgid ""
"If you can’t find any themes you like, it’s always possible to [create your "
"own](../developers/04_Frontend/02_Design.md)."
msgstr ""
"Si aucun de ceux proposés ne convient, il est toujours possible de [créer "
"son propre thème](../developers/04_Frontend/02_Design.md)."

#. type: Plain text
#: en/./users/05_Configuration.md:62
msgid ""
"To select a theme, simply scroll through the themes and select one that "
"strikes your fancy. After confirmation, the theme will be applied to the "
"interface."
msgstr ""
"Pour sélectionner un thème, il suffit de faire défiler les thèmes jusqu’à "
"l’apparition du thème choisi. Après validation, le thème sera appliqué à "
"l’interface."

#. type: Title ##
#: en/./users/05_Configuration.md:63
#, no-wrap
msgid "Content width"
msgstr "Largeur du contenu"

#. type: Plain text
#: en/./users/05_Configuration.md:66
msgid ""
"Some people prefer short lines of text, while others prefer to maximize the "
"available screen space. To satisfy the maximum number of people, it’s "
"possible to customize the width of the displayed content. There are four "
"settings available:"
msgstr ""
"Il y en a qui préfère des lignes de texte courtes, d’autres qui préfèrent "
"maximiser l’espace disponible sur l’écran. Pour satisfaire le maximum de "
"personne, il est possible de choisir la largeur du contenu affiché. Il y a "
"quatre réglages disponibles :"

#. type: Bullet: '* '
#: en/./users/05_Configuration.md:71
msgid "**Fine** displays content up to a maximum width of 550 pixels"
msgstr "**Fine** qui affiche le contenu jusqu’à 550 pixels"

#. type: Bullet: '* '
#: en/./users/05_Configuration.md:71
msgid "**Medium** displays content up to a maximum width of 800 pixels"
msgstr "**Moyenne** qui affiche le contenu jusqu’à 800 pixels"

#. type: Bullet: '* '
#: en/./users/05_Configuration.md:71
msgid "**Large** displays content up to a maximum width of 1000 pixels"
msgstr "**Large** qui affiche le contenu jusqu’à 1000 pixels"

#. type: Bullet: '* '
#: en/./users/05_Configuration.md:71
msgid "**No limit** displays the content on 100% of the available space"
msgstr ""
"**Pas de limite** qui affiche le contenu sur 100% de la place disponible"

#. type: Title ##
#: en/./users/05_Configuration.md:72
#, no-wrap
msgid "Article icons"
msgstr "Icônes d’article"

#. type: Plain text
#: en/./users/05_Configuration.md:75
msgid "Please note that this section only affects normal view."
msgstr "Veuillez noter que cette section n’affecte que la vue normale."

#. type: Plain text
#: en/./users/05_Configuration.md:77
msgid ""
"![Article icons configuration](../img/users/configuration.article.icons.png)"
msgstr ""
"![Configuration des icônes d’article](../img/users/configuration.article."
"icons.png)"

#. type: Plain text
#: en/./users/05_Configuration.md:80
msgid ""
"Each article is rendered with a header (top line) and a footer (bottom "
"line).  In that section, you can choose what will be displayed in those."
msgstr ""
"Chaque article est rendu avec un en-tête (ligne supérieure) et un pied de "
"page (ligne inférieure). Dans cette section, vous pouvez choisir ce qui sera "
"affiché dans ceux-ci."

#. type: Plain text
#: en/./users/05_Configuration.md:84
msgid ""
"If you disable every item in the top line, you’ll still be able to see it "
"since it contains the feed name and the article title. But if you do the "
"same thing for the bottom line, it will be empty."
msgstr ""
"Si vous désactivez tous les éléments de la ligne supérieure, vous pourrez "
"toujours les voir, puisqu’il contient le nom du flux et le titre de "
"l’article. Mais si vous faites le même chose pour la ligne inférieure, elle "
"sera vide."

#. type: Title ##
#: en/./users/05_Configuration.md:85
#, fuzzy, no-wrap
#| msgid "HTML5 notification timout"
msgid "HTML5 notification timeout"
msgstr "Temps d’affichage de la notification HTML5"

#. type: Plain text
#: en/./users/05_Configuration.md:88
msgid ""
"After automatically updating the feeds, FreshRSS can pop up a notification "
"using the HTML5 notification API."
msgstr ""
"Après la mise à jour automatique des flux, FreshRSS utilise l’API de "
"notification de HTML5 pour avertir de l’arrivée de nouveaux articles."

#. type: Plain text
#: en/./users/05_Configuration.md:90
msgid ""
"The duration of this notification can be set. By default, the value is 0."
msgstr ""
"Il est possible de régler la durée d’affichage de cette notification. Par "
"défaut, la valeur est 0."

#. type: Title ##
#: en/./users/05_Configuration.md:91
#, no-wrap
msgid "Show the navigation button"
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:94
msgid ""
"By default, FreshRSS displays buttons to ease the article navigation when "
"browsing on mobile. The drawback is that they eat up some precious space."
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:96
msgid ""
"![navigation button configuration](../img/users/configuration.navigation."
"button.png)"
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:98
msgid ""
"If you don’t use those buttons because you never browse on mobile or because "
"you browse with gestures, you can disable them from the interface."
msgstr ""

#. type: Title #
#: en/./users/05_Configuration.md:99
#, no-wrap
msgid "Reading"
msgstr ""

#. type: Title ##
#: en/./users/05_Configuration.md:101
#, no-wrap
msgid "Number of articles per page"
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:104
msgid ""
"This setting defines the number of articles to display at once before "
"needing to load more. In normal and reading view, more articles are loaded "
"automatically. In global view, a button will appear at the bottom of the "
"list."
msgstr ""

#. type: Title ##
#: en/./users/05_Configuration.md:105
#, fuzzy, no-wrap
#| msgid "Article icons"
msgid "Articles to display"
msgstr "Icônes d’article"

#. type: Plain text
#: en/./users/05_Configuration.md:108
msgid ""
"The status of articles to display when loading FreshRSS. \"Adjust showing\" "
"will display only unread articles by default, but will display all articles "
"when there are no unread articles to show."
msgstr ""

#. type: Title ##
#: en/./users/05_Configuration.md:109
#, no-wrap
msgid "Use “lazy load” mode to load images"
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:112
msgid ""
"This will set images to load as they are viewed. This can save data, but "
"will can cause images to load in later."
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:118
msgid ""
"These are the global options for fetching and retaining articles from feeds. "
"They can be overridden by individual feed’s settings."
msgstr ""

#. type: Title ##
#: en/./users/05_Configuration.md:119
#, no-wrap
msgid "Maintenance"
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:122
msgid ""
"This allows for purging/optimizing the current user’s articles in the "
"database."
msgstr ""

#. type: Title #
#: en/./users/05_Configuration.md:123
#, no-wrap
msgid "Sharing"
msgstr "Partage"

#. type: Plain text
#: en/./users/05_Configuration.md:126
msgid ""
"To make your life easier, you can share articles straight from FreshRSS."
msgstr ""
"Pour vous faciliter la vie, vous pouvez partager des articles directement "
"via FreshRSS."

#. type: Plain text
#: en/./users/05_Configuration.md:128
msgid ""
"At the moment, FreshRSS supports [20+ sharing services](08_sharing_services."
"md), ranging from self-hosted services (Shaarli, etc.) to proprietary "
"services (Facebook, etc.)."
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:131
msgid ""
"By default, the sharing list is empty.  ![Sharing configuration](../img/"
"users/configuration.sharing.png)"
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:133
msgid ""
"To add a new item to the list, please follow the following simple steps:"
msgstr ""
"Pour ajouter un nouvel élément à la liste, veuillez suivre les étapes "
"simples ci-dessous :"

#. type: Bullet: '1. '
#: en/./users/05_Configuration.md:138
msgid "Select the desired sharing method in the drop-down list."
msgstr ""

#. type: Bullet: '1. '
#: en/./users/05_Configuration.md:138
msgid "Press the ```✚``` button to add it to the list."
msgstr ""

#. type: Bullet: '1. '
#: en/./users/05_Configuration.md:138
msgid ""
"Configure the method in the list. All names can be modified in the display. "
"Some methods need the sharing URL to be able to work properly (ex: Shaarli)."
msgstr ""

#. type: Bullet: '1. '
#: en/./users/05_Configuration.md:138 en/./users/05_Configuration.md:143
msgid "Submit your changes."
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:140
msgid "To remove an item from the list, follow those simple steps:"
msgstr ""

#. type: Bullet: '1. '
#: en/./users/05_Configuration.md:143
msgid "Press the ```❌``` button next to the share method you want to remove."
msgstr ""

#. type: Title #
#: en/./users/05_Configuration.md:145
#, no-wrap
msgid "Shortcuts"
msgstr "Raccourcis"

#. type: Plain text
#: en/./users/05_Configuration.md:149
msgid ""
"To ease the use of the application, FreshRSS comes with a lot of predefined "
"keyboard shortcuts.  They allow actions to improve the user experience with "
"a keyboard."
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:151
msgid ""
"Of course, if you’re not satisfied with the key mapping, you can change you "
"configuration to fit your needs."
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:153
msgid "There are 4 types of shortcuts:"
msgstr ""

#. type: Bullet: '1. '
#: en/./users/05_Configuration.md:158
msgid "Views: they allow switching views with ease."
msgstr ""

#. type: Bullet: '1. '
#: en/./users/05_Configuration.md:158
msgid ""
"Navigation: they allow navigation through articles, feeds, and categories."
msgstr ""

#. type: Bullet: '1. '
#: en/./users/05_Configuration.md:158
msgid ""
"Article actions: they allow interactions with an article, like sharing or "
"opening it on the original web-site."
msgstr ""

#. type: Bullet: '1. '
#: en/./users/05_Configuration.md:158
msgid ""
"Other actions: they allow other interactions with the application, like "
"opening the user queries menu or accessing the documentation."
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:161
msgid ""
"It’s worth noting that the share article action has two levels. Once you "
"press the shortcut, a menu containing all the share options opens.  To "
"choose one share option, you need to select it by its number. When there is "
"only one option, it’s selected automatically though."
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:163
msgid "The same process applies to the user queries."
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:166
msgid ""
"Be aware that there is no validation on the selected shortcuts.  This means "
"that if you assign a shortcut to more than one action, you’ll end up with "
"some unexpected behavior."
msgstr ""

#. type: Title #
#: en/./users/05_Configuration.md:167
#, no-wrap
msgid "User queries"
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:171
msgid ""
"You can configure your [user queries](./03_Main_view.md) in that section. "
"There is not much to say here as it is pretty straightforward.  You can only "
"change user query titles or drop them."
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:173
msgid "At the moment, there is no helper to build a user query from here."
msgstr ""

#. type: Title #
#: en/./users/05_Configuration.md:174
#, no-wrap
msgid "Profile"
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:177
msgid ""
"You can change your email address or password here. The authentication token "
"is required for accessing the aggregated RSS feed for a user. A blank token "
"will disable accessing the RSS feed without being logged in."
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:181
msgid ""
"Extensions can be managed from this menu. Note that while extensions can be "
"removed from the web interface, they cannot be added from it."
msgstr ""

#. type: Title #
#: en/./users/05_Configuration.md:182
#, no-wrap
msgid "Users"
msgstr ""

#. type: Title ##
#: en/./users/05_Configuration.md:186
#, no-wrap
msgid "Authentication methods"
msgstr ""

#. type: Title ###
#: en/./users/05_Configuration.md:188
#, no-wrap
msgid "HTTP Authentication (Apache)"
msgstr ""

#. type: Bullet: '1. '
#: en/./users/05_Configuration.md:194
msgid "User control is based on the `.htaccess` file."
msgstr ""

#. type: Bullet: '2. '
#: en/./users/05_Configuration.md:194
msgid ""
"It is best practice to place the `.htaccess` file in the `./i/` subdirectory "
"so the API and other third party services can work."
msgstr ""

#. type: Bullet: '3. '
#: en/./users/05_Configuration.md:194
msgid ""
"If you want to limit all access to registered users only, place the file in "
"the FreshRSS directory itself or in a parent directory. Note that WebSub and "
"API will not work!"
msgstr ""

#. type: Bullet: '4. '
#: en/./users/05_Configuration.md:194
msgid "Example `.htaccess` file for a user \"marie\":"
msgstr ""

#. type: Fenced code block (apache)
#: en/./users/05_Configuration.md:195
#, no-wrap
msgid ""
"AuthUserFile /home/<USER>/repertoire/.htpasswd\n"
"AuthGroupFile /dev/null\n"
"AuthName \"Chez Marie\"\n"
"AuthType Basic\n"
"Require user marie\n"
msgstr ""

#. type: Plain text
#: en/./users/05_Configuration.md:204
msgid ""
"More information can be found in the [Apache documentation](http://httpd."
"apache.org/docs/trunk/howto/auth.html#gettingitworking)."
msgstr ""
"Plus d’informations dans [la documentation d’Apache.](http://httpd.apache."
"org/docs/trunk/howto/auth.html#gettingitworking)"

#. type: Plain text
#: en/./users/06_Mobile_access.md:2
#, fuzzy
#| msgid ""
#| "This page assumes you have completed the [server setup](../"
#| "admins/02_Installation.md)."
msgid ""
"This page assumes you have completed the [server setup](../"
"admins/03_Installation.md)."
msgstr ""
"Cette page suppose que vous ayez fini [l’installation du serveur]"
"(01_Installation.md)."

#. type: Title #
#: en/./users/06_Mobile_access.md:3
#, no-wrap
msgid "Mobile Access"
msgstr ""

#. type: Plain text
#: en/./users/06_Mobile_access.md:6
msgid ""
"You can access FreshRSS on mobile devices via browser and via mobile apps."
msgstr ""

#. type: Title ##
#: en/./users/06_Mobile_access.md:8
#, no-wrap
msgid "Access via Browser"
msgstr ""

#. type: Plain text
#: en/./users/06_Mobile_access.md:11
msgid ""
"The FreshRSS user interface is optimized for both small and large screens. "
"The content will fit nicely on small mobile device screens as well."
msgstr ""

#. type: Title ##
#: en/./users/06_Mobile_access.md:13
#, no-wrap
msgid "Access via Mobile App"
msgstr ""

#. type: Plain text
#: en/./users/06_Mobile_access.md:16
msgid ""
"FreshRSS supports access from mobile / native apps for Linux, Android, iOS, "
"Windows and macOS, via two distinct APIs: Google Reader API (best), and "
"Fever API (limited features and less efficient)."
msgstr ""

#. type: Plain text
#: en/./users/06_Mobile_access.md:18
msgid ""
"A list of known apps is available on the [FreshRSS GitHub page](https://"
"github.com/FreshRSS/FreshRSS#apis--native-apps)."
msgstr ""

#. type: Title ###
#: en/./users/06_Mobile_access.md:20
#, no-wrap
msgid "Enable the API in FreshRSS"
msgstr "Activer l’API dans FreshRSS"

#. type: Bullet: '1. '
#: en/./users/06_Mobile_access.md:26
msgid ""
"Under the section “Authentication”, enable the option “Allow API access "
"(required for mobile apps)”."
msgstr ""
"Dans la section “Authentification”, cocher l’option “Autoriser l’accès par "
"API (nécessaire pour les applis mobiles)”."

#. type: Bullet: '2. '
#: en/./users/06_Mobile_access.md:26
#, fuzzy
#| msgid ""
#| "Under the section “Authentication”, enable the option “Allow API access "
#| "(required for mobile apps)”."
msgid ""
"Under the section “Profile”, fill-in the field “API password (e.g., for "
"mobile apps)”."
msgstr ""
"Dans la section “Authentification”, cocher l’option “Autoriser l’accès par "
"API (nécessaire pour les applis mobiles)”."

#. type: Bullet: '	* '
#: en/./users/06_Mobile_access.md:26
msgid "Every user must define an API password."
msgstr ""

#. type: Bullet: '	* '
#: en/./users/06_Mobile_access.md:26
#, fuzzy
#| msgid ""
#| "2. Under the section “Profile”, fill-in the field “API password (e.g., "
#| "for mobile apps)”.\n"
#| "\t* Every user must define an API password.\n"
#| "\t* The reason for an API-specific password is that it may be used in "
#| "less safe situations than the main password, and does not grant access to "
#| "as many things.\n"
msgid ""
"The reason for an API-specific password is that it may be used in less safe "
"situations than the main password, and does not grant access to as many "
"things."
msgstr ""
"2. Dans la section “Profil”, remplir le champ “Mot de passe API (ex. : pour "
"applis mobiles)”.\n"
"\t* Chaque utilisateur doit choisir son mot de passe API.\n"
"\t* La raison d’être d’un mot de passe API\n"
" différent du mot de passe principal est que le mot de passe API est "
"potentiellement utilisé de manière moins sûre, mais il permet aussi moins de "
"choses.\n"

#. type: Plain text
#: en/./users/06_Mobile_access.md:29
#, fuzzy
#| msgid ""
#| "The rest of this page is about the Google Reader compatible API.  See the "
#| "[page about the Fever compatible API](06_Fever_API.md) for another "
#| "possibility."
msgid ""
"See the [page about the Google Reader compatible API](../"
"developers/06_GoogleReader_API.md) for more details.  See the [page about "
"the Fever compatible API](../developers/06_Fever_API.md) for more details."
msgstr ""
"Le reste de cette page concerne l’API compatible Google Reader. Voir la "
"[page sur l’API compatible Fever](06_Fever_API.md) pour une autre "
"possibilité."

#. type: Title ###
#: en/./users/06_Mobile_access.md:31
#, no-wrap
msgid "Testing"
msgstr "Tester"

#. type: Bullet: '1. '
#: en/./users/06_Mobile_access.md:38
msgid ""
"Under the section “Profile”, click on the link like `https://rss.example.net/"
"api/` next to the field “API password”."
msgstr ""
"Dans la section “Profil”, cliquer sur le lien de la forme `https://rss."
"example.net/api/` à côté du champ “Mot de passe API”."

#. type: Bullet: '2. '
#: en/./users/06_Mobile_access.md:38
msgid "Click on first link “Check full server configuration”:"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/06_Mobile_access.md:38
msgid "If you get *PASS* then you are done; all is well."
msgstr ""

#. type: Bullet: '	* '
#: en/./users/06_Mobile_access.md:38
msgid ""
"If you get *Bad Request!* or *Not Found*, then your server probably does not "
"accept slashes `/` that are escaped `%2F`, see the next section \"Fix server "
"configuration\"."
msgstr ""

#. type: Bullet: '	* '
#: en/./users/06_Mobile_access.md:38
msgid ""
"If you receive any other error message, see the next section “Fix server "
"configuration”."
msgstr ""

#. type: Title ###
#: en/./users/06_Mobile_access.md:39
#, no-wrap
msgid "Fix server configuration"
msgstr "Déboguer la configuration du serveur"

#. type: Bullet: '* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"Click on the second link “Check partial server configuration (without `%2F` "
"support)”:"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"If you get `PASS`, then the problem is indeed that your server does not "
"accept slashes `/` that are escaped `%2F`."
msgstr ""

#. type: Bullet: '		* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"With Apache, remember the directive [`AllowEncodedSlashes On`](http://httpd."
"apache.org/docs/trunk/mod/core.html#allowencodedslashes)"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"Or use a client that does not escape slashes (such as EasyRSS) ([`check "
"client list`](https://github.com/FreshRSS/FreshRSS#apis--native-apps))."
msgstr ""

#. type: Bullet: '	* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"If you get *Service Unavailable!*, then check the preceding section “Enable "
"the API in FreshRSS” again."
msgstr ""

#. type: Bullet: '	* '
#: en/./users/06_Mobile_access.md:57
msgid "With __Apache__:"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"If you get *FAIL getallheaders!*, the combination of your PHP version and "
"your Web server does not provide access to [`getallheaders`](http://php.net/"
"getallheaders)"
msgstr ""

#. type: Bullet: '			* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"Turn on Apache `mod_setenvif` (often enabled by default), or `mod_rewrite` "
"with the following procedure:"
msgstr ""

#. type: Bullet: '				* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"Allow [`FileInfo` in `.htaccess`](http://httpd.apache.org/docs/trunk/mod/"
"core.html#allowoverride): see the [server setup](../admins/03_Installation."
"md) again."
msgstr ""

#. type: Bullet: '				* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"Enable [`mod_rewrite`](http://httpd.apache.org/docs/trunk/mod/mod_rewrite."
"html):"
msgstr ""

#. type: Bullet: '					* '
#: en/./users/06_Mobile_access.md:57
msgid "With Debian / Ubuntu: `sudo a2enmod rewrite`"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/06_Mobile_access.md:57
msgid "With __nginx__:"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/06_Mobile_access.md:57
msgid "If you get *Bad Request!*, check your server `PATH_INFO` configuration."
msgstr ""

#. type: Bullet: '		* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"If you get *File not found!*, check your server `fastcgi_split_path_info`."
msgstr ""

#. type: Bullet: '	* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"If you get *FAIL 64-bit or GMP extension!*, then your PHP version does not "
"pass the requirement of being 64-bit and/or have PHP [GMP](http://php.net/"
"gmp) extension."
msgstr ""

#. type: Bullet: '		* '
#: en/./users/06_Mobile_access.md:57
msgid ""
"The easiest is to add the GMP extension. On Debian / Ubuntu: `sudo apt "
"install php-gmp`"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/06_Mobile_access.md:57
msgid "Update and try again from the preceding section “Testing”."
msgstr ""

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:2
msgid ""
"We may not have answered all of your questions in the previous sections. The "
"FAQ contains some questions that have not been answered elsewhere."
msgstr ""
"Il est possible que nous n’ayons pas répondu à toutes vos questions dans les "
"parties précédentes. La FAQ regroupe certaines interrogations qui n’ont pas "
"trouvé leur réponse ailleurs."

#. type: Title ##
#: en/./users/07_Frequently_Asked_Questions.md:3
#, no-wrap
msgid "What is `/i` at the end of the application URL?"
msgstr "C’est quoi ce `/i` à la fin de l’URL ?"

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:6
msgid ""
"Of course, ```/i``` has a purpose! It’s used for performance and usability:"
msgstr ""
"Bien entendu, le ```/i``` n’est pas là pour faire joli ! Il s’agit d’une "
"question de performances et de praticité :"

#. type: Bullet: '* '
#: en/./users/07_Frequently_Asked_Questions.md:11
msgid ""
"It allows for serving icons, images, styles and scripts without cookies. "
"Without that trick, those files would be downloaded more often, especially "
"when form authentication is used. Also, HTTP requests would be heavier."
msgstr ""
"Cela permet de servir les icônes, images, styles, scripts sans cookie. Sans "
"cela, ces fichiers seraient souvent re-téléchargés, en particulier lorsque "
"le formulaire de connexion est utilisé. De plus, les requêtes vers ces "
"ressources seraient plus lourdes."

#. type: Bullet: '* '
#: en/./users/07_Frequently_Asked_Questions.md:11
msgid ""
"The ```./p/``` public root can be served without any HTTP access "
"restrictions. Whereas it could be implemented in ```./p/i/```."
msgstr ""
"La racine publique ```./p/``` peut être servie sans restriction d’accès HTTP "
"(qui peut avantageusement être mise en place dans ```./p/i/```)."

#. type: Bullet: '* '
#: en/./users/07_Frequently_Asked_Questions.md:11
msgid ""
"It avoids problems while serving public resources like ```favicon.ico```, "
"```robots.txt```, etc."
msgstr ""
"Cela permet d’éviter des problèmes pour des fichiers qui doivent être "
"publics pour bien fonctionner, comme ```favicon.ico```, ```robots.txt```, "
"etc."

#. type: Bullet: '* '
#: en/./users/07_Frequently_Asked_Questions.md:11
msgid ""
"It allows the logo to be displayed instead of a white page while hitting a "
"restriction or a delay during the loading process."
msgstr ""
"Cela permet aussi d’avoir un logo FreshRSS plutôt qu’une page blanche pour "
"accueillir l’utilisateur par exemple dans le cas de la restriction d’accès "
"HTTP ou lors de l’attente du chargement plus lourd du reste de l’interface."

#. type: Title ##
#: en/./users/07_Frequently_Asked_Questions.md:12
#, no-wrap
msgid "Why is `robots.txt` located in a sub-folder?"
msgstr "Pourquoi le ```robots.txt``` se trouve dans un sous-répertoire ?"

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:15
msgid ""
"To increase security, FreshRSS is hosted in two sections. The first section "
"is public (the `./p` folder) and the second section is private (everything "
"else). Therefore the `robots.txt` file is located in the `./p` sub-folder."
msgstr ""
"Afin d’améliorer la sécurité, FreshRSS est découpé en deux parties : une "
"partie publique (le répertoire ```./p```) et une partie privée (tout le "
"reste !). Le ```robots.txt``` se trouve donc dans le sous-répertoire ```./"
"p```."

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:18
#, fuzzy
#| msgid ""
#| "As explained in the [security section](/en/User_documentation/"
#| "Installation/Security), it’s highly recommended to make only the public "
#| "section available at the domain level. With that configuration, `./p` is "
#| "the root folder for http://demo.freshrss.org/, thus making `robots.txt` "
#| "available at the root of the application."
msgid ""
"As explained in the [security section](../admins/09_AccessControl.html), "
"it’s highly recommended to make only the public section available at the "
"domain level.  With that configuration, `./p` is the root folder for "
"<https://demo.freshrss.org/>, thus making `robots.txt` available at the root "
"of the application."
msgstr ""
"Comme expliqué dans les [conseils de sécurité](01_Installation.md#conseils-"
"de-securite), il est recommandé de faire pointer un nom de domaine vers ce "
"sous-répertoire afin que seule la partie publique ne soit accessible par un "
"navigateur web. De cette manière https://demo.freshrss.org/ pointe vers le "
"répertoire ```./p``` et le ```robots.txt``` se trouve bien à la racine du "
"site : https://demo.freshrss.org/robots.txt."

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:20
msgid "The same principle applies to `favicon.ico` and `.htaccess`."
msgstr ""
"L’explication est la même pour les fichiers ```favicon.ico``` et ```."
"htaccess```."

#. type: Title ##
#: en/./users/07_Frequently_Asked_Questions.md:21
#, no-wrap
msgid "Why do I have errors while registering a feed?"
msgstr "Pourquoi j’ai des erreurs quand j’essaye d’enregistrer un flux ?"

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:27
msgid ""
"There can be different origins for that problem.  The feed syntax can be "
"invalid, it can be unrecognized by the SimplePie library, the hosting server "
"can be the root of the problem, or FreshRSS can be buggy.  The first step is "
"to identify what causes the problem.  Here are the steps to follow:"
msgstr ""
"Il peut y avoir différentes origines à ce problème. Le flux peut avoir une "
"syntaxe invalide, il peut ne pas être reconnu par la bibliothèque SimplePie, "
"l’hébergement peut avoir des problèmes, FreshRSS peut être boggué. Il faut "
"dans un premier temps déterminer la cause du problème.Voici la liste des "
"étapes à suivre pour la déterminer :"

#. type: Bullet: '1. '
#: en/./users/07_Frequently_Asked_Questions.md:31
msgid ""
"__Verify if the feed syntax is valid__ with the [W3C on-line tool](https://"
"validator.w3.org/feed/ \"RSS and Atom feed validator\"). If it’s not valid, "
"there’s nothing we can do."
msgstr ""
"__Vérifier la validité du flux__ grâce à l’[outil en ligne du W3C](https://"
"validator.w3.org/feed/ \"Validateur en ligne de flux RSS et Atom\"). Si ça "
"ne fonctionne pas, nous ne pouvons rien faire."

#. type: Bullet: '1. '
#: en/./users/07_Frequently_Asked_Questions.md:31
msgid ""
"__Verify SimplePie validation__ with the [SimplePie on-line tool](https://"
"simplepie.org/demo/ \"SimplePie official demo\"). If it’s not recognized, "
"there’s nothing we can do."
msgstr ""
"__Vérifier la reconnaissance par SimplePie__ grâce à l’[outil en ligne de "
"SimplePie](https://simplepie.org/demo/ \"Démo officielle de SimplePie\"). Si "
"ça ne fonctionne pas, nous ne pouvons rien faire."

#. type: Bullet: '1. '
#: en/./users/07_Frequently_Asked_Questions.md:31
msgid ""
"__Verify FreshRSS integration__ with the [demo](https://demo.freshrss.org "
"\"FreshRSS official demo\"). If it’s not working, you need to [create an "
"issue on GitHub](https://github.com/FreshRSS/FreshRSS/issues/new \"Create an "
"issue for FreshRSS\") so we can have a look at it. If it’s working, there’s "
"probably something fishy with the hosting server."
msgstr ""
"__Vérifier l’intégration dans FreshRSS__ grâce à la [démo](https://demo."
"freshrss.org \"Démo officielle de FreshRSS\"). Si ça ne fonctionne pas, il "
"faut [créer un ticket sur GitHub](https://github.com/FreshRSS/FreshRSS/"
"issues/new \"Créer un ticket pour FreshRSS\") pour que l’on puisse regarder "
"ce qui se passe. Si ça fonctionne, il y a probablement un problème avec "
"l’hébergement."

#. type: Title ##
#: en/./users/07_Frequently_Asked_Questions.md:32
#, no-wrap
msgid "How can you change a forgotten password?"
msgstr "Comment changer un mot de passe oublié ?"

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:36
msgid ""
"Since the [1.10.0](https://github.com/FreshRSS/FreshRSS/releases/tag/1.10.0) "
"release, admins can change user passwords directly from the interface. This "
"interface is available under ```Administration → Manage users```.  Select a "
"user, enter a password, and validate."
msgstr ""
"Depuis la version [1.10.0](https://github.com/FreshRSS/FreshRSS/releases/"
"tag/1.10.0), l’administrateur peut modifier le mot de passe d’un utilisateur "
"depuis l’interface. Cette interface est disponible dans le menu "
"```Administration → Gestion des utilisateurs```. Il suffit de sélectionner "
"l’utilisateur, de saisir un mot de passe et de valider."

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:38
msgid ""
"Since the [1.8.0](https://github.com/FreshRSS/FreshRSS/releases/tag/1.8.0) "
"release, admins can change user passwords using a terminal. It worth "
"mentioning that you must have access to PHP CLI. Open a terminal, and type "
"the following command:"
msgstr ""
"Depuis la version [1.8.0](https://github.com/FreshRSS/FreshRSS/releases/"
"tag/1.8.0), l’administrateur peut modifier le mot de passe d’un utilisateur "
"depuis un terminal. Il est bon de noter que celui-ci doit avoir un accès à "
"PHP en ligne de commande. Pour cela, il suffit d’ouvrir son terminal et de "
"saisir la commande suivante :"

#. type: Fenced code block (sh)
#: en/./users/07_Frequently_Asked_Questions.md:39
#, no-wrap
msgid "./cli/update_user.php --user <username> --password <password>\n"
msgstr ""
"./cli/update_user.php --user <username> --password <password>\n"
"\n"

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:44
#, fuzzy
#| msgid ""
#| "For more information on that matter, please refer to the [dedicated "
#| "documentation](../../cli/README.md)."
msgid ""
"For more information on that matter, please refer to the [dedicated "
"documentation](https://github.com/FreshRSS/FreshRSS/blob/edge/cli/README.md)."
msgstr ""
"Pour plus d’information à ce sujet, il existe la [documentation dédiée]"
"(../../cli/README.md)."

#. type: Title ##
#: en/./users/07_Frequently_Asked_Questions.md:45
#, no-wrap
msgid "Permissions under SELinux"
msgstr "Gérer les permissions sous SELinux"

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:48
#, fuzzy
#| msgid ""
#| "Some Linux distribution, like Fedora or RedHat Enterprise Linux, have "
#| "SELinux enabled. This acts similar to a firewall application, so that "
#| "applications can’t write or modify files under certain conditions. While "
#| "installing FreshRSS, step 2 can fail if the httpd process cannot write to "
#| "some data sub-directories. The following command should be executed as "
#| "root to fix this problem:"
msgid ""
"Some Linux distribution, like Fedora or RedHat Enterprise Linux, have "
"SELinux enabled. This acts similar to a firewall application, so that "
"applications can’t write or modify files under certain conditions. While "
"installing FreshRSS, step 2 can fail if the httpd process can’t write to "
"some data sub-directories. The following command should be executed as root "
"to fix this problem:"
msgstr ""
"Certaines distributions Linux comme Fedora ou RedHat Enterprise Linux (RHEL) "
"activent par défaut le système SELinux. Celui-ci permet de gérer des "
"permissions au niveau des processus. Lors de l’installation de FreshRSS, "
"l’étape 2 procède à la vérification des droits sur certains répertoires, il "
"faut donc exécuter la commande suivante en tant que root:"

#. type: Fenced code block (sh)
#: en/./users/07_Frequently_Asked_Questions.md:49
#, no-wrap
msgid ""
"semanage fcontext -a -t httpd_sys_rw_content_t '/usr/share/FreshRSS/data(/.*)?'\n"
"restorecon -Rv /usr/share/FreshRSS/data\n"
msgstr ""
"semanage fcontext -a -t httpd_sys_rw_content_t '/usr/share/FreshRSS/data(/.*)?'\n"
"restorecon -Rv /usr/share/FreshRSS/data\n"

#. type: Title ##
#: en/./users/07_Frequently_Asked_Questions.md:54
#, no-wrap
msgid "Why do I have a blank page while trying to configure the sharing options?"
msgstr "Pourquoi y a-t-il une page blanche lorsque je configure les options de partage ?"

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:57
msgid ""
"The `sharing` word in the URL is a trigger word for some ad-blocker rules. "
"Starting with version 1.16, `sharing` has been replaced by `integration` in "
"the faulty URL while keeping the exact same wording throughout the "
"application."
msgstr ""
"Le mot `sharing` dans l’URL est un mot déclencheur pour certaines règles des "
"bloqueurs de publicités. À partir de la version 1.16, `sharing` a été "
"remplacé par `integration` dans l’URL posant problème tout en conservant "
"exactement la même dénomination à travers l’application."

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:59
msgid ""
"If you are using a version prior to 1.16, you can disable your ad-blocker "
"for FreshRSS or you can add a rule to allow the `sharing` page to be "
"accessed."
msgstr ""
"Si vous utilisez une version antérieure à 1.16, vous pouvez désactiver votre "
"bloqueur de publicité pour FreshRSS ou vous pouvez ajouter une règle pour "
"permettre la consultation de la page de configuration « partage »."

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:61
msgid "Examples with _uBlock_:"
msgstr "Exemples avec _uBlock_ :"

#. type: Bullet: '* '
#: en/./users/07_Frequently_Asked_Questions.md:64
msgid ""
"Whitelist your FreshRSS instance by adding it in _uBlock > Open the "
"dashboard > Whitelist_."
msgstr ""
"Ajoutez votre instance FreshRSS à la liste blanche de en l’ajoutant dans "
"_uBlock > Ouvrir le tableau de bord > Liste blanche_."

#. type: Bullet: '* '
#: en/./users/07_Frequently_Asked_Questions.md:64
msgid ""
"Authorize your FreshRSS instance to call `sharing` configuration page by "
"adding the rule `*sharing,domain=~yourdomain.com` in _uBlock > Open the "
"dashboard > My filters_"
msgstr ""
"Autorisez votre instance FreshRSS à appeler la page de configuration "
"`sharing` en ajoutant la règle `*sharing,domain=~votredomaine.com` dans "
"_uBlock > Ouvrir le fichier tableau de bord > Mes filtres_"

#. type: Title ##
#: en/./users/07_Frequently_Asked_Questions.md:65
#, no-wrap
msgid "Problems with firewalls"
msgstr ""

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:68
msgid ""
"If you have the error \"Blast! This feed has encountered a problem. Please "
"verify that it is always reachable then update it.\", it might be because of "
"a firewall misconfiguration."
msgstr ""

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:70
msgid "To identify the problem, here are the steps to follow:"
msgstr ""

#. type: Bullet: '* '
#: en/./users/07_Frequently_Asked_Questions.md:73
msgid ""
"step 1: Try to reach the feed locally to discard a problem with the feed "
"itself. You can use your browser to this purpose."
msgstr ""

#. type: Bullet: '* '
#: en/./users/07_Frequently_Asked_Questions.md:73
msgid ""
"step 2: Try to reach the feed from the host in which FreshRSS is installed. "
"Something like `time curl -v 'https://github.com/FreshRSS/FreshRSS/commits/"
"edge.atom'` should make the deal. If you are running FreshRSS within a "
"Docker container, then you can check connectivity from within the container "
"itself with something similar to `sudo docker exec freshrss php -r "
"\"readfile('https://github.com/FreshRSS/FreshRSS/commits/edge.atom');\"`. If "
"none of this works, then it might be a problem with your firewall."
msgstr ""

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:75
msgid ""
"Then to fix it, you need to do check your firewall configuration and ensure "
"that you are not blocking connections to IPs and/or ports in which your "
"feeds are located. If using iptables and you are blocking inbound "
"connections to ports 80/443, check that the rules are properly configured "
"and you are not also blocking outbound connections to the very same ports."
msgstr ""

#. type: Plain text
#: en/./users/07_Frequently_Asked_Questions.md:76
msgid ""
"For example, when using the firewall provided by Synology, you can block "
"traffic for certain applications (i.e., ports). One could think that these "
"rules would be applied only to incoming connections but specifying * for the "
"originating host of the requests will also include your local networks. To "
"deal with this issue, you will have to add exceptions for your local "
"networks to be able to access those ports with a higher priority than the "
"one blocking incoming connections. This could be similar for other frontends "
"to iptables. Please check the following discussion about a [similar issue]"
"(https://www.reddit.com/r/synology/comments/8fo2sj/"
"ds918_firewall_blocking_outgoing_traffic_from/)."
msgstr ""

#. type: Title #
#: en/./users/08_sharing_services.md:1
#, fuzzy, no-wrap
#| msgid "Sharing"
msgid "Sharing Services"
msgstr "Partage"

#. type: Plain text
#: en/./users/08_sharing_services.md:4
msgid "FreshRSS has the option to share links with a bunch of services."
msgstr ""

#. type: Title ##
#: en/./users/08_sharing_services.md:5
#, no-wrap
msgid "Available Services: Simple Sharing"
msgstr ""

#. type: Plain text
#: en/./users/08_sharing_services.md:12
#, no-wrap
msgid ""
"| Service       | Short description                                      | Notes                                                         |\n"
"|:--------------|:-------------------------------------------------------|:--------------------------------------------------------------|\n"
"| Clipboard     | Copy article link into the operation system clipboard | |\n"
"| Email         | Open the email app to send the article link            | |\n"
"| Print         | Open browser’s print dialog to print out the article   | |\n"
msgstr ""

#. type: Title ##
#: en/./users/08_sharing_services.md:13
#, no-wrap
msgid "Available Services: Hosted Services"
msgstr ""

#. type: Plain text
#: en/./users/08_sharing_services.md:39
#, no-wrap
msgid ""
"| Service           | Short description                                    | Links                                            | Notes                                                         |\n"
"|:------------------|:-----------------------------------------------------|:-------------------------------------------------|:--------------------------------------------------------------|\n"
"| Blogotext         | A little more than a lightweight SQLite Blog-Engine. | [GitHub](https://github.com/BlogoText/blogotext) | Deprecated since FreshRSS V1.20.0 (2022). Will be deleted in 2023 (scheduled to FreshRSS V1.22.0) |\n"
"| Buffer         | Buffer.com is a social media management platform for scheduling, publishing, and analyzing content. | [Website](https://buffer.com) ||\n"
"| Diaspora*         | The online social world where you are in control     | [Website](https://diasporafoundation.org/), [Wikipedia](https://en.wikipedia.org/wiki/Diaspora_(social_network)) |  |\n"
"| Facebook          | Worldwide social network (by Meta Platforms)         | [Website](https://facebook.com), [Wikipedia](https://en.wikipedia.org/wiki/Facebook)\n"
"| GNU social        | Social communication software for both public and private communications | [Website](https://gnu.io/social/) | |\n"
"| Journal du hacker | Le Journal du hacker s'inspire directement du site anglophone Hacker News | [Website](https://www.journalduhacker.net/) |\n"
"| Known based sites | Its robust open source framework can be used to build fully-fledged community sites, or a blog for a single user. | [Website](https://withknown.com/) | |\n"
"| Lemmy             | Selfhosted social link aggregation and discussion platform | [Website](https://join-lemmy.org/) | |\n"
"| Linkding          | Selfhosted bookmark service | [Website](https://github.com/sissbruecker/linkding) | |\n"
"| LinkedIn          | Business and employment-oriented online service      | [Website](https://www.linkedin.com/), [Wikipedia](https://en.wikipedia.org/wiki/LinkedIn)| |\n"
"| Mastodon          | Self-hosted social networking & microblogging services | [Website](https://joinmastodon.org/), [Wikipedia](https://en.wikipedia.org/wiki/Mastodon_(software)) | |\n"
"| Movim             | A powerful web frontend for XMPP                     | [Website](https://movim.eu/) | |\n"
"| Pinboard          | Social Bookmarking for Introverts                    | [Website](https://pinboard.in/) | |\n"
"| Pinterest         | Is an image sharing and social media service designed to enable saving and discovery of information| [Website](https://pinterest.com/), [Wikipedia](https://en.wikipedia.org/wiki/Pinterest) | |\n"
"| Pocket            | Social bookmarking (previous \"Read it Later\", owned by Mozilla) | [Website](https://getpocket.com), [Wikipedia](https://en.wikipedia.org/wiki/Pocket_(service)) | |\n"
"| Raindrop.io       | All-in-one bookmark manager                          | [Website](https://raindrop.io/)| |\n"
"| Reddit            | A network of communities where people can dive into their interests, hobbies and passions| [Website](https://www.reddit.com/), [Wikipedia](https://en.wikipedia.org/wiki/Reddit)| |\n"
"| Shaarli           | Self-hosted minimalist bookmark manager and link sharing service | [Website](https://shaarli.readthedocs.io/) | |\n"
"| Twitter           | Microblogging social network                         | [Website](https://twitter.com), [Wikipedia](https://de.wikipedia.org/wiki/Twitter) | |\n"
"| wallabag          | Save and classify articles. Read them later. Freely  | [Website](https://www.wallabag.org) | Compatible to version 1 and 2\n"
"| Whatsapp          | Instant messaging and voice-over-IP service owned by Meta Platforms| [Website](https://www.whatsapp.com), [Wikipedia](https://en.wikipedia.org/wiki/WhatsApp) | |\n"
"| XING              | Career-oriented social networking site, operated by New Work SE | [Website](https://www.xing.com/), [Wikipedia](https://en.wikipedia.org/wiki/XING) | |\n"
msgstr ""

#. type: Title ##
#: en/./users/08_sharing_services.md:40
#, fuzzy, no-wrap
#| msgid "Configuration management"
msgid "Configuration"
msgstr "Gestion de la configuration"

#. type: Plain text
#: en/./users/08_sharing_services.md:43
msgid ""
"Select the needed sharing services in the configuration menu "
"(Configuration / Sharing)."
msgstr ""

#. type: Title ##
#: en/./users/08_sharing_services.md:44
#, no-wrap
msgid "Usage"
msgstr ""

#. type: Plain text
#: en/./users/08_sharing_services.md:47
msgid ""
"Activate the sharing menu in configuration menu (Configuration / Display). "
"It is only available for the bottom line."
msgstr ""

#. type: Plain text
#: en/./users/08_sharing_services.md:49
msgid ""
"The menu with the selected services is available in the footer of article."
msgstr ""

#. type: Title ##
#: en/./users/08_sharing_services.md:50
#, no-wrap
msgid "Add More Sharing Services"
msgstr ""

#. type: Plain text
#: en/./users/08_sharing_services.md:52
#, fuzzy
#| msgid ""
#| "Search for it on [the bug tracker](https://github.com/FreshRSS/FreshRSS/"
#| "issues) (don’t forget to use the search bar)."
msgid ""
"Please open a new issue on [GitHub](https://github.com/FreshRSS/FreshRSS/"
"issues) and support us with information."
msgstr ""
"Cherche sur [le bug tracker](https://github.com/FreshRSS/FreshRSS/issues) "
"(n’oubliez pas d’utiliser la barre de recherche)."

#. type: Title #
#: en/./users/09_refreshing_feeds.md:1
#, no-wrap
msgid "Refreshing feeds"
msgstr "Rafraîchir les flux"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:4
#, fuzzy
#| msgid ""
#| "To take full advantage of FreshRSS, it needs to retrieve new items from "
#| "the feeds you have subscribed to. There are several ways to do this."
msgid ""
"To take full advantage of FreshRSS, it needs to retrieve new items from the "
"feeds you have subscribed to. There are several ways to do this:"
msgstr ""
"Pour profiter pleinement de FreshRSS, il faut qu’il récupère les nouveaux "
"articles disponibles des flux auxquels vous avez souscrit. Pour cela, il "
"existe plusieurs méthodes."

#. type: Bullet: '- '
#: en/./users/09_refreshing_feeds.md:17
msgid "[Manual update](#manual-update)"
msgstr ""

#. type: Bullet: '    - '
#: en/./users/09_refreshing_feeds.md:17
#, fuzzy
#| msgid "Complete update"
msgid "[Complete update](#complete-update)"
msgstr "Mise à jour complète"

#. type: Bullet: '    - '
#: en/./users/09_refreshing_feeds.md:17
msgid "[Partial update](#partial-update)"
msgstr ""

#. type: Bullet: '- '
#: en/./users/09_refreshing_feeds.md:17
msgid "[Automatic update with cron](#automatic-update-with-cron)"
msgstr ""

#. type: Bullet: '- '
#: en/./users/09_refreshing_feeds.md:17
msgid "[Online cron](#online-cron)"
msgstr ""

#. type: Bullet: '    - '
#: en/./users/09_refreshing_feeds.md:17
msgid "[For Form Authentication](#for-form-authentication)"
msgstr ""

#. type: Bullet: '    - '
#: en/./users/09_refreshing_feeds.md:17
msgid "[For HTTP authentication](#for-http-authentication)"
msgstr ""

#. type: Bullet: '    - '
#: en/./users/09_refreshing_feeds.md:17
msgid "[For No authentication None](#for-no-authentication-none)"
msgstr ""

#. type: Bullet: '- '
#: en/./users/09_refreshing_feeds.md:17
msgid ""
"[Feed configuration of “Do not automatically refresh more often than”](#feed-"
"configuration-of-do-not-automatically-refresh-more-often-than)"
msgstr ""

#. type: Bullet: '    - '
#: en/./users/09_refreshing_feeds.md:17
msgid "[Background](#background)"
msgstr ""

#. type: Bullet: '    - '
#: en/./users/09_refreshing_feeds.md:17
msgid "[Default value](#default-value)"
msgstr ""

#. type: Bullet: '    - '
#: en/./users/09_refreshing_feeds.md:17
msgid "[Individual feed configuration](#individual-feed-configuration)"
msgstr ""

#. type: Title ##
#: en/./users/09_refreshing_feeds.md:18
#, no-wrap
msgid "Manual update"
msgstr "Mise à jour manuelle"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:21
msgid ""
"If you can’t or don’t want to use the automatic method, you can update "
"manually. There are two methods for updating all or some of the feeds."
msgstr ""
"Si vous ne pouvez pas ou ne voulez pas utiliser la méthode automatique, vous "
"pouvez le faire de façon manuelle. Il existe deux méthodes qui permettent de "
"mettre à jour tout ou partie des flux."

#. type: Title ###
#: en/./users/09_refreshing_feeds.md:22
#, no-wrap
msgid "Complete update"
msgstr "Mise à jour complète"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:25
msgid ""
"This update occurs on all feeds. To trigger it, simply click on the update "
"link in the navigation menu."
msgstr ""
"Cette mise à jour se fait pour l’ensemble des flux de l’instance. Pour "
"initier cette mise à jour, il suffit de cliquer sur le lien de mise à jour "
"disponible dans le menu de navigation."

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:27
msgid "![Navigation menu](../img/users/refresh.1.png)"
msgstr "![Menu de navigation](../img/users/refresh.1.png)"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:29
msgid ""
"When the update starts, a progress bar appears and changes while feeds are "
"processed."
msgstr ""
"Lorsque la mise à jour démarre, une barre de progression apparait et "
"s’actualise au fur et à mesure de la récupération des articles."

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:31
msgid "![Progress bar](../img/users/refresh.5.png)"
msgstr "![Barre de progression](../img/users/refresh.5.png)"

#. type: Title ###
#: en/./users/09_refreshing_feeds.md:32
#, no-wrap
msgid "Partial update"
msgstr "Mise à jour partielle"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:35
msgid ""
"This update occurs on the selected feed only. To trigger it, simply click on "
"the update link in the feed menu."
msgstr ""
"Cette mise à jour se fait pour le flux sélectionné uniquement. Pour initier "
"cette mise à jour, il suffit de cliquer sur le lien de mise à jour "
"disponible dans le menu du flux."

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:37
msgid "![Feed menu](../img/users/refresh.2.png)"
msgstr "![Menu du flux](../img/users/refresh.2.png)"

#. type: Title ##
#: en/./users/09_refreshing_feeds.md:38
#, fuzzy, no-wrap
#| msgid "Automatic update"
msgid "Automatic update with cron"
msgstr "Mise à jour automatique"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:41
msgid "This is the recommended method."
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:43
msgid ""
"This method is only available if you have access to the scheduled tasks of "
"the machine on which your FreshRSS instance is installed."
msgstr ""
"Cette méthode n’est possible que si vous avez accès aux tâches planifiées de "
"la machine sur laquelle est installée votre instance de FreshRSS."

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:45
msgid ""
"The script is named *actualize_script.php* and is located in the *app* "
"folder. The scheduled task syntax will not be explained here. However, here "
"is [a quick introduction to crontab](http://www.adminschoice.com/crontab-"
"quick-reference/) that might help you."
msgstr ""
"Le script qui permet de mettre à jour les articles s’appelle "
"*actualize_script.php* et se trouve dans le répertoire *app* de votre "
"instance de FreshRSS. La syntaxe des tâches planifiées ne sera pas expliqué "
"ici, cependant voici [une introduction rapide à crontab](http://www."
"adminschoice.com/crontab-quick-reference/) qui peut vous aider."

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:47
msgid "Here is an example to trigger article update every hour."
msgstr ""
"Ci-dessous vous trouverez un exemple permettant la mise à jour des articles "
"toutes les heures."

#. type: Fenced code block (cron)
#: en/./users/09_refreshing_feeds.md:48
#, no-wrap
msgid "0 * * * * php /path/to/FreshRSS/app/actualize_script.php > /tmp/FreshRSS.log 2>&1\n"
msgstr "0 * * * * php /chemin/vers/FreshRSS/app/actualize_script.php > /tmp/FreshRSS.log 2>&1\n"

#. type: Title ##
#: en/./users/09_refreshing_feeds.md:52
#, no-wrap
msgid "Online cron"
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:55
msgid ""
"If you do not have access to the installation server scheduled task, you can "
"still automate the update process."
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:58
msgid ""
"To do so, you need to create a scheduled task, which need to call a specific "
"URL: <https://freshrss.example.net/i/?c=feed&a=actualize> (it could be "
"different depending on your installation). Depending on your application "
"authentication method, you need to adapt the scheduled task."
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:60
msgid ""
"Special parameters to configure the script - all parameters can be combined:"
msgstr ""
"« Paramètres de configuration du script; Ils sont utilisables "
"simultanément : »"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:68
msgid ""
"- Parameter \"ajax\" <https://freshrss.example.net/i/?"
"c=feed&a=actualize&ajax=1> Only a status site is returned and not a complete "
"website. Example: \"OK\""
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:72
msgid ""
"- Parameter \"maxFeeds\" <https://freshrss.example.net/i/?"
"c=feed&a=actualize&maxFeeds=30> If *maxFeeds* is set the configured amount "
"of feeds is refreshed at once. The default setting is \"10\"."
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:76
msgid ""
"- Parameter \"token\" <https://freshrss.example.net/i/?"
"c=feed&a=actualize&token=542345872345734> Security parameter to prevent "
"unauthorized refreshes. For detailed Documentation see \"Form "
"authentication\"."
msgstr ""

#. type: Title ###
#: en/./users/09_refreshing_feeds.md:77
#, fuzzy, no-wrap
#| msgid "Form authentication"
msgid "For Form Authentication"
msgstr "Authentification par formulaire"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:80
#, fuzzy
#| msgid ""
#| "You can also configure an authentication token to grant special access on "
#| "the server."
msgid ""
"If your FreshRSS instance is using Form Authentication, you can configure an "
"authentication token to grant access to the online cron."
msgstr ""
"Vous pouvez aussi configurer un jeton d’authentification pour accorder un "
"droit spécial sur votre serveur."

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:82
msgid "![Token configuration](../img/users/token.1.png)"
msgstr "![Configuration du token](../img/users/token.1.png)"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:84
msgid ""
"You can target a specific user by adding their username to the query string, "
"with `&user=insert-username`:"
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:86
msgid "The scheduled task syntax should look as follows:"
msgstr "La tâche cron à utiliser sera de la forme suivante :"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:88
#, fuzzy
#| msgid ""
#| "0 * * * * curl 'https://freshrss.example.net/i/?"
#| "c=feed&a=actualize&user=someone&token=my-token'\n"
msgid ""
"<https://freshrss.example.net/i/?"
"c=feed&a=actualize&maxFeeds=10&ajax=1&user=someone&token=my-token>"
msgstr ""
"0 * * * * curl 'https://freshrss.exemple.net/i/?"
"c=feed&a=actualize&user=quelquun&token=mon-token'\n"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:90
#, fuzzy
#| msgid ""
#| "If you configure the application to allow anonymous reading, you can also "
#| "allow anonymous users to update feeds (“Allow anonymous refresh of the "
#| "articles”)."
msgid ""
"Alternatively, but not recommended, if you configure the application to "
"allow anonymous reading, you can also allow anonymous users to update feeds "
"(“Allow anonymous refresh of the articles”), and that does not require a "
"token."
msgstr ""
"Dans ces cas-là, si vous avez autorisé la lecture anonyme des articles, vous "
"pouvez aussi permettre à n’importe qui de rafraîchir vos flux (« Autoriser "
"le rafraîchissement anonyme des flux »)."

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:92
msgid "![Anonymous access configuration](../img/users/anonymous_access.1.png)"
msgstr ""
"![Configuration de l’accès anonymes](../img/users/anonymous_access.1.png)"

#. type: Title ###
#: en/./users/09_refreshing_feeds.md:93
#, fuzzy, no-wrap
#| msgid "HTTP authentication"
msgid "For HTTP authentication"
msgstr "Authentification HTTP"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:96
msgid ""
"If your FreshRSS instance is using HTTP authentication, you’ll need to "
"provide your credentials to the scheduled task."
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:98
#, no-wrap
msgid "**Note:** This method is discouraged as your credentials are stored in plain text.\n"
msgstr ""

#. type: Fenced code block (cron)
#: en/./users/09_refreshing_feeds.md:99
#, fuzzy, no-wrap
#| msgid "0 * * * * curl -u alice:password123 'https://freshrss.example.net/i/?c=feed&a=actualize'\n"
msgid "0 * * * * curl -u alice:password123 'https://freshrss.example.net/i/?c=feed&a=actualize&maxFeeds=10&ajax=1&user=alice'\n"
msgstr "0 * * * * curl -u alice:motdepasse123 'https://freshrss.exemple.net/i/?c=feed&a=actualize'\n"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:104
msgid "On some systems, that syntax might also work:"
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:106
#, fuzzy
#| msgid ""
#| "0 * * * * curl -u alice:password123 'https://freshrss.example.net/i/?"
#| "c=feed&a=actualize'\n"
msgid ""
"<https://alice:<EMAIL>/i/?"
"c=feed&a=actualize&maxFeeds=10&ajax=1&user=alice>"
msgstr ""
"0 * * * * curl -u alice:motdepasse123 'https://freshrss.exemple.net/i/?"
"c=feed&a=actualize'\n"

#. type: Title ###
#: en/./users/09_refreshing_feeds.md:107
#, fuzzy, no-wrap
#| msgid "Form authentication"
msgid "For No authentication (None)"
msgstr "Authentification par formulaire"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:110
msgid ""
"If your FreshRSS instance uses no authentication (public instance, default "
"user):"
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:112
#, fuzzy
#| msgid ""
#| "0 * * * * curl 'https://freshrss.example.net/i/?c=feed&a=actualize'\n"
msgid "<https://freshrss.example.net/i/?c=feed&a=actualize&maxFeeds=10&ajax=1>"
msgstr "0 * * * * curl 'https://freshrss.example.net/i/?c=feed&a=actualize'\n"

#. type: Title ##
#: en/./users/09_refreshing_feeds.md:113
#, no-wrap
msgid "Feed configuration of “Do not automatically refresh more often than”"
msgstr ""

#. type: Title ###
#: en/./users/09_refreshing_feeds.md:115
#, no-wrap
msgid "Background"
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:118
msgid ""
"FreshRSS does not, by design, supports pull refreshes at frequencies higher "
"than once every 15 minutes. But FreshRSS supports instant push (WebSub)."
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:120
msgid ""
"FreshRSS is part of an RSS ecosystem. A typical reaction that we have seen "
"from several servers is to simply ban by, IP, user-agent, or to remove their "
"RSS feed altogether. Bad user behaviours affect the larger community."
msgstr ""

#. type: Title ###
#: en/./users/09_refreshing_feeds.md:121
#, no-wrap
msgid "Default value"
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:124
#, no-wrap
msgid "The default value of “Do not automatically refresh more often than” is set in Configuration -> Archiving.\n"
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:126
msgid ""
"The lowest global/default purposely cannot be set faster than every 20 "
"minutes, to avoid wasting resources and make sure the RSS ecosystem remains "
"sane."
msgstr ""

#. type: Title ###
#: en/./users/09_refreshing_feeds.md:127
#, fuzzy, no-wrap
#| msgid "Fix server configuration"
msgid "Individual feed configuration"
msgstr "Déboguer la configuration du serveur"

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:130
msgid "Under the settings for individual feeds, you can go down to 15min."
msgstr ""

#. type: Plain text
#: en/./users/09_refreshing_feeds.md:134
msgid ""
"Read more: - [Normal, Global and Reader view](./03_Main_view.md)  - [Filter "
"the feeds and search](./10_filter.md)"
msgstr ""

#. type: Title #
#: en/./users/10_filter.md:2
#, no-wrap
msgid "Filtering articles"
msgstr "Filtrer les articles"

#. type: Title ##
#: en/./users/10_filter.md:4
#, no-wrap
msgid "Purpose"
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:7
msgid ""
"When the number of articles stored by FreshRSS inevitably grows larger, it’s "
"important to use efficient filters to display only a subset of the articles. "
"There are several methods that filter with different criteria. Usually those "
"methods can be combined."
msgstr ""
"Avec le nombre croissant d’articles stockés par FreshRSS, il devient "
"important d’avoir des filtres efficaces pour n’afficher qu’une partie des "
"articles. Il existe plusieurs méthodes qui filtrent selon des critères "
"différents. Ces méthodes peuvent être combinées dans la plus part des cas."

#. type: Title ##
#: en/./users/10_filter.md:8
#, no-wrap
msgid "By category"
msgstr "Par catégorie"

#. type: Plain text
#: en/./users/10_filter.md:11
msgid ""
"This is the easiest method. You only need to click on the category title in "
"the side panel. There are two special categories at the top of the panel:"
msgstr ""
"C’est la méthode la plus simple. Il suffit de cliquer sur le titre d’une "
"catégorie dans le panneau latéral. Il existe deux catégories spéciales qui "
"sont placées en haut dudit panneau :"

#. type: Bullet: '* '
#: en/./users/10_filter.md:14
msgid ""
"*Main feed* displays only articles from feeds marked as available in that "
"category"
msgstr ""
"*Flux principal* qui affiche uniquement les articles des flux marqués comme "
"visible dans cette catégorie"

#. type: Bullet: '* '
#: en/./users/10_filter.md:14
msgid "*Favourites* displays only articles marked as favourites"
msgstr ""
"*Favoris* qui affiche uniquement les articles, tous flux confondus, marqués "
"comme favoris"

#. type: Title ##
#: en/./users/10_filter.md:15
#, no-wrap
msgid "By feed"
msgstr "Par flux"

#. type: Plain text
#: en/./users/10_filter.md:18
msgid "There are several methods to filter articles by feed:"
msgstr "Il existe plusieurs méthodes pour filtrer les articles par flux :"

#. type: Bullet: '* '
#: en/./users/10_filter.md:23
msgid "by clicking the feed title in the side panel"
msgstr "en cliquant sur le titre du flux dans le panneau latéral"

#. type: Bullet: '* '
#: en/./users/10_filter.md:23
msgid "by clicking the feed title in the article details"
msgstr "en cliquant sur le titre du flux dans le détail de l’article"

#. type: Bullet: '* '
#: en/./users/10_filter.md:23
msgid "by filtering in the feed options from the side panel"
msgstr "en filtrant dans les options du flux dans le panneau latéral"

#. type: Bullet: '* '
#: en/./users/10_filter.md:23
msgid "by filtering in the feed configuration"
msgstr "en filtrant dans la configuration du flux"

#. type: Plain text
#: en/./users/10_filter.md:25
msgid "![Feed filter](../img/users/feed.filter.1.png)"
msgstr "![Filtrer par flux](../img/users/feed.filter.1.png)"

#. type: Title ##
#: en/./users/10_filter.md:26
#, no-wrap
msgid "By status"
msgstr "Par statut"

#. type: Plain text
#: en/./users/10_filter.md:29
msgid ""
"Each article has two attributes that can be combined. The first attribute "
"indicates whether or not the article has been read. The second attribute "
"indicates if the article was marked as favorite or not."
msgstr ""
"Chaque article possède deux attributs qui peuvent être combinés. Le premier "
"attribut indique si l’article a été lu ou non. Le second attribut indique si "
"l’article a été noté comme favori ou non."

#. type: Plain text
#: en/./users/10_filter.md:31
msgid ""
"In version 0.7, attribute filters are available in the article display "
"dropdown list. With this version, it’s not possible to combine filters. For "
"instance, it’s not possible to display only read and favorite articles."
msgstr ""
"Dans la version 0.7.x, les filtres sur les attributs sont accessibles depuis "
"la liste déroulante qui gère l’affichage des articles. Dans cette version, "
"il n’est pas possible de combiner les filtres. Par exemple, on ne peut pas "
"afficher les articles lus qui ont été notés comme favori."

#. type: Plain text
#: en/./users/10_filter.md:33
msgid "![Attribute filters in 0.7](../img/users/status.filter.0.7.png)"
msgstr ""
"![Filtrer par attribut en version 0.7](../img/users/status.filter.0.7.png)"

#. type: Plain text
#: en/./users/10_filter.md:35
msgid ""
"Starting with version 0.8, all attribute filters are visible as toggle "
"icons. They can be combined. As any combination is possible, some have the "
"same result. For instance, the result for all filters selected is the same "
"as no filter selected."
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:37
msgid "![Attribute filters in 0.8](../img/users/status.filter.0.8.png)"
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:39
msgid "By default, this filter displays only unread articles"
msgstr ""

#. type: Title ##
#: en/./users/10_filter.md:40
#, no-wrap
msgid "By content"
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:43
msgid ""
"It is possible to filter articles by their content by inputting a string in "
"the search field."
msgstr ""

#. type: Title ##
#: en/./users/10_filter.md:44
#, no-wrap
msgid "With the search field"
msgstr "Grâce au champ de recherche"

#. type: Plain text
#: en/./users/10_filter.md:47
msgid "You can use the search field to further refine results:"
msgstr ""
"Il est possible d’utiliser le champ de recherche pour raffiner les "
"résultats :"

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid "by feed ID: `f:123` or multiple feed IDs (*or*): `f:123,234,345`"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid "by author: `author:name` or `author:'composed name'`"
msgstr "par auteur : `author:nom` or `author:'nom composé'`"

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid "by title: `intitle:keyword` or `intitle:'composed keyword'`"
msgstr "par titre : `intitle:mot` or `intitle:'mot composé'`"

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid "by URL: `inurl:keyword` or `inurl:'composed keyword'`"
msgstr "par URL: `inurl:mot` or `inurl:'mot composé'`"

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid "by tag: `#tag` or `#tag+with+whitespace`"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid "by free-text: `keyword` or `'composed keyword'`"
msgstr "par texte libre : `mot` ou `'mot composé'`"

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid ""
"by date of discovery, using the [ISO 8601 time interval format](http://en."
"wikipedia.org/wiki/ISO_8601#Time_intervals): `date:<date-interval>`"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/10_filter.md:93
msgid "From a specific day, or month, or year:"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-03-30`"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-03` or `date:201403`"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014`"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/10_filter.md:93
msgid "From a specific time of a given day:"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-05-30T13`"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-05-30T13:30`"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/10_filter.md:93
msgid "Between two given dates:"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-02/2014-04`"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-02--2014-04`"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-02/04`"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-02-03/05`"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-02-03T22:00/22:15`"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-02-03T22:00/15`"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/10_filter.md:93
msgid "After a given date:"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-03/`"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/10_filter.md:93
msgid "Before a given date:"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:/2014-03`"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/10_filter.md:93
msgid "For a specific duration after a given date:"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:2014-03/P1W`"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/10_filter.md:93
msgid "For a specific duration before a given date:"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:P1W/2014-05-25T23:59:59`"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/10_filter.md:93
msgid "For the past duration before now (the trailing slash is optional):"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:P1Y/` or `date:P1Y` (past year)"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:P2M/` (past two months)"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:P3W/` (past three weeks)"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:P4D/` (past four days)"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:PT5H/` (past five hours)"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:PT30M/` (past thirty minutes)"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:PT90S/` (past ninety seconds)"
msgstr ""

#. type: Bullet: '		* '
#: en/./users/10_filter.md:93
msgid "`date:P1DT1H/` (past one day and one hour)"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid ""
"by date of publication, using the same format: `pubdate:<date-interval>`"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid ""
"by custom label ID `L:12` or multiple label IDs: `L:12,13,14` or with any "
"label: `L:*`"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid ""
"by custom label name `label:label`, `label:\"my label\"` or any label name "
"from a list (*or*): `labels:\"my label,my other label\"`"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid ""
"by several label names (*and*): `label:\"my label\" label:\"my other label\"`"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid ""
"by entry (article) ID: `e:1639310674957894` or multiple entry IDs (*or*): "
"`e:1639310674957894,1639310674957893`"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:93
msgid ""
"by user query (saved search) name: `search:myQuery`, `search:\"My query\"` "
"or saved search ID: `S:3`"
msgstr ""

#. type: Bullet: '	* '
#: en/./users/10_filter.md:93
msgid ""
"internally, those references are replaced by the corresponding user query in "
"the search expression"
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:95
msgid ""
"Be careful not to enter a space between the operator and the search value."
msgstr ""
"Attention à ne pas introduire d’espace entre l’opérateur et la valeur "
"recherchée."

#. type: Plain text
#: en/./users/10_filter.md:98
#, fuzzy
#| msgid ""
#| "Some operators can be used negatively, to exclude articles, with the same "
#| "syntax as above, but prefixed by a `!` or `-`: `-author:name`, `-intitle:"
#| "keyword`, `-inurl:keyword`, `-#tag`, `!keyword`."
msgid ""
"Some operators can be used negatively, to exclude articles, with the same "
"syntax as above, but prefixed by a `!` or `-`: `!f:234`, `-author:name`, `-"
"intitle:keyword`, `-inurl:keyword`, `-#tag`, `!keyword`, `!date:2019`, `!"
"date:P1W`, `!pubdate:P3d/`."
msgstr ""
"Certains opérateurs peuvent être utilisé négativement, pour exclure des "
"articles, avec la même syntaxe que ci-dessus, mais préfixé par `!` ou `-` :`-"
"author:nom`, `-intitle:mot`, `-inurl:mot`, `-#tag`, `!mot`."

#. type: Plain text
#: en/./users/10_filter.md:101
#, fuzzy
#| msgid ""
#| "It is also possible to combine keywords to create a more precise filter. "
#| "For example, you can enter multiple instances of `author:`, `intitle:`, "
#| "`inurl:`, `#`, and free-text."
msgid ""
"It is also possible to combine keywords to create a more precise filter.  "
"For example, you can enter multiple instances of `f:`, `author:`, `intitle:"
"`, `inurl:`, `#`, and free-text."
msgstr ""
"Il est également possible de combiner les mots-clefs pour faire un filtrage "
"encore plus précis, et il est autorisé d’avoir plusieurs instances de : "
"`author:`, `intitle:`, `inurl:`, `#`, et texte libre."

#. type: Plain text
#: en/./users/10_filter.md:104
msgid ""
"Combining several search criteria implies a logical *and*, but the keyword ` "
"OR ` can be used to combine several search criteria with a logical *or* "
"instead: `author:Dupont OR author:Dupond`"
msgstr ""
"Combiner plusieurs critères implique un *et* logique, mais le mot clef ` OR "
"` peut être utiliser pour combiner plusieurs critères avec un *ou* logique :"
"`author:Dupont OR author:Dupond`"

#. type: Plain text
#: en/./users/10_filter.md:107
msgid ""
"You don’t have to do anything special to combine multiple negative "
"operators. Writing `!intitle:'thing1' !intitle:'thing2'` implies AND, see "
"above. For more pointers on how AND and OR interact with negation, see [this "
"GitHub comment](https://github.com/FreshRSS/FreshRSS/"
"issues/3236#issuecomment-891219460).  Additional reading: [De Morgan’s laws]"
"(https://en.wikipedia.org/wiki/De_Morgan%27s_laws)."
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:109
msgid ""
"Finally, parentheses may be used to express more complex queries, with basic "
"negation support:"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:115
msgid "`(author:Alice OR intitle:hello) (author:Bob OR intitle:world)`"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:115
msgid "`(author:Alice intitle:hello) OR (author:Bob intitle:world)`"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:115
msgid "`!((author:Alice intitle:hello) OR (author:Bob intitle:world))`"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:115
msgid "`(author:Alice intitle:hello) !(author:Bob intitle:world)`"
msgstr ""

#. type: Bullet: '* '
#: en/./users/10_filter.md:115
msgid "`!(S:1 OR S:2)`"
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:117
#, no-wrap
msgid "> ℹ️ If you need to search for a parenthesis, it needs to be escaped like `\\(` or `\\)`\n"
msgstr ""

#. type: Title ##
#: en/./users/10_filter.md:118
#, no-wrap
msgid "By sorting by date"
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:121
msgid ""
"You can change the sort order by clicking the toggle button available in the "
"header."
msgstr ""

#. type: Title ##
#: en/./users/10_filter.md:122
#, no-wrap
msgid "Store your filters"
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:125
msgid ""
"Once you came up with your perfect filter, it would be a shame if you need "
"to recreate it every time you need to use it."
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:129
msgid ""
"Hopefully, there is a way to bookmark them for later use.  We call them "
"*user queries*.  You can create as many as you want, the only limit is how "
"they will be displayed on your screen."
msgstr ""

#. type: Title ###
#: en/./users/10_filter.md:130
#, no-wrap
msgid "Bookmark the current query"
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:134
msgid ""
"Display the user queries drop-down by clicking the button next to the state "
"buttons.  ![User queries drop-down](../img/users/user.queries.drop-down."
"empty.png)"
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:136
msgid "Then click on the bookmark action."
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:138
msgid "Congratulations, you’re done!"
msgstr ""

#. type: Title ###
#: en/./users/10_filter.md:139
#, fuzzy, no-wrap
#| msgid "Use bookmarklet"
msgid "Using a bookmarked query"
msgstr "Utiliser le « bookmarklet »"

#. type: Plain text
#: en/./users/10_filter.md:143
msgid ""
"Display the user queries drop-down by clicking the button next to the state "
"buttons.  ![User queries drop-down](../img/users/user.queries.drop-down.not."
"empty.png)"
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:145
msgid ""
"Then click on the bookmarked query, the previously stored query will be "
"applied."
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:148
#, no-wrap
msgid ""
"> Note that only the query is stored, not the articles.\n"
"> The results you are seeing now could be different from the results on the day you've created the query.\n"
msgstr ""

#. type: Plain text
#: en/./users/10_filter.md:152
msgid ""
"Read more: * [Normal, Global and Reader view](./03_Main_view.md)  * "
"[Refreshing the feeds](./09_refreshing_feeds.md)"
msgstr ""

#, no-wrap
#~ msgid "Join us on the mailing lists"
#~ msgstr "Rejoignez-nous sur les listes de mailing"

#~ msgid ""
#~ "Do you want to ask us some questions? Do you want to discuss with us? "
#~ "Don’t hesitate to subscribe to our mailing lists!"
#~ msgstr ""
#~ "S’il vous manque des informations, n’hésitez pas à fouiller un peu la "
#~ "documentation ou venir nous poser directement vos questions sur [la "
#~ "mailing list des développeurs](https://freshrss.org/mailman/listinfo/dev)."

#~ msgid ""
#~ "The first mailing is destined to generic information, it should be "
#~ "adapted to users. [Join <EMAIL>](https://freshrss.org/"
#~ "mailman/listinfo/mailing)."
#~ msgstr ""
#~ "Le premier mailing est destiné à l’information générique, il doit être "
#~ "adapté aux utilisateurs. [Rejoignez <EMAIL>](https://"
#~ "freshrss.org/mailman/listinfo/mailing)."

#~ msgid ""
#~ "The second mailing is mainly for developers. [Join <EMAIL>]"
#~ "(https://freshrss.org/mailman/listinfo/dev)"
#~ msgstr ""
#~ "Le deuxième mailing s’adresse principalement aux développeurs. [Rejoignez "
#~ "<EMAIL>](https://freshrss.org/mailman/listinfo/dev)"

#~ msgid ""
#~ "If you want to improve internationalization, please open a new ticket "
#~ "first and follow the advice from the *Fix a bug* section."
#~ msgstr ""
#~ "Si vous voulez améliorer l’internationalisation, ouvrez d’abord un "
#~ "nouveau ticket et suivez les conseils de la section *Fixer un bogue*."

#~ msgid "Translations are present in the subdirectories of `./app/i18n/`."
#~ msgstr ""
#~ "Les traductions sont disponibles dans les sous-répertoires de `./app/i18n/"
#~ "`."

#~ msgid ""
#~ "We’re working on a better way to handle internationalization, but don’t "
#~ "hesitate to suggest any ideas!"
#~ msgstr ""
#~ "Nous travaillons sur une meilleure façon de gérer l’internationalisation "
#~ "mais n’hésitez pas à nous suggérer des idées !"

#, no-wrap
#~ msgid "console"
#~ msgstr "console"

#, no-wrap
#~ msgid "php"
#~ msgstr "php"

#, no-wrap
#~ msgid "bash"
#~ msgstr "bash"

#~ msgid ""
#~ "On [the mailing lists](https://freshrss.org/announce-of-the-mailing-lists."
#~ "html)"
#~ msgstr ""
#~ "Sur [les listes de diffusion](https://freshrss.org/announce-of-the-"
#~ "mailing-lists.html)"

#, no-wrap
#~ msgid "Models"
#~ msgstr "Modèles"

#, no-wrap
#~ msgid "Controllers and actions"
#~ msgstr "Contrôleurs et actions"

#, no-wrap
#~ msgid "Writing URL"
#~ msgstr "Écriture des URL"

#, no-wrap
#~ msgid "Understanding internals"
#~ msgstr "Comprendres les mécanismes internes"

#, no-wrap
#~ msgid "Accessing the database"
#~ msgstr "Accès à la base de données"

#, no-wrap
#~ msgid "Writing an action and its related view"
#~ msgstr "Écrire une action et sa vue associée"

#, no-wrap
#~ msgid "Authentication"
#~ msgstr "Gestion de l’authentification"

#, no-wrap
#~ msgid "Logs"
#~ msgstr "Gestion des logs"

#, no-wrap
#~ msgid "Understanding basic mechanics (Minz and MVC)"
#~ msgstr "Comprendre les mécaniques de base (Minz et MVC)"

#, no-wrap
#~ msgid "**TODO** : move to 02_Minz.md\n"
#~ msgstr "**TODO** : bouger dans 02_Minz.md\n"

#, no-wrap
#~ msgid "html"
#~ msgstr "html"

#~ msgid ""
#~ "To access these translations, `Minz_Translate` will help us with its "
#~ "`Minz_Translate::t()` method. As this can be a bit long to type, a "
#~ "shortcut has been introduced that **must** be used in all circumstances: "
#~ "`_t()`.  Code example:"
#~ msgstr ""
#~ "Pour accéder à ces traductions, `Minz_Translate` va nous aider à l’aide "
#~ "de sa méthode `Minz_Translate::t()`. Comme cela peut être un peu long à "
#~ "taper, il a été introduit un raccourci qui **doit** être utilisé en "
#~ "toutes circonstances : `_t()`. Exemple de code :"

#, no-wrap
#~ msgid ""
#~ "<p>\n"
#~ "\t<a href=\"<?= _url('index', 'index') ?>\">\n"
#~ "\t\t<?= _t('gen.action.back_to_rss_feeds') ?>\n"
#~ "\t</a>\n"
#~ "</p>\n"
#~ msgstr ""
#~ "<p>\n"
#~ "\t<a href=\"<?= _url('index', 'index') ?>\">\n"
#~ "\t\t<?= _t('gen.action.back_to_rss_feeds') ?>\n"
#~ "\t</a>\n"
#~ "</p>\n"

#~ msgid ""
#~ "`type`: Defines the type of your extension. There are two types: `system` "
#~ "and` user`. We will study this difference right after."
#~ msgstr ""
#~ "`type` : définit le type de votre extension. Il existe deux types : "
#~ "`system` et `user`. Nous étudierons cette différence juste après."

#~ msgid ""
#~ "This is the recommended method since you can forget about it once it is "
#~ "configured."
#~ msgstr ""
#~ "C’est la méthode recommandée car il n’y a pas besoin d’y penser, elle se "
#~ "fait toute seule, à la fréquence que vous avez choisi."

#, no-wrap
#~ msgid "With the actualize_script.php script"
#~ msgstr "Par le script actualize_script.php"

#, no-wrap
#~ msgid "cron"
#~ msgstr "cron"

#, no-wrap
#~ msgid "No authentication"
#~ msgstr "Aucune authentification"

#~ msgid ""
#~ "This is the most straightforward since you have a public instance; there "
#~ "is nothing special to configure:"
#~ msgstr ""
#~ "C’est le cas le plus simple, puisque votre instance est publique, vous "
#~ "n’avez rien de particulier à préciser :"

#~ msgid ""
#~ "The URL used in the previous section will now become accessible to "
#~ "anyone. Therefore you can use the same syntax for the scheduled task."
#~ msgstr ""
#~ "L’url précédente devient donc accessible à n’importe qui et vous pouvez "
#~ "utiliser la tâche cron de la partie précédente."

#, no-wrap
#~ msgid "0 * * * * curl 'https://freshrss.example.net/i/?c=feed&a=actualize&token=my-token'\n"
#~ msgstr "0 * * * * curl 'https://freshrss.example.net/i/?c=feed&a=actualize&token=mon-token'\n"

#~ msgid ""
#~ "When using HTTP authentication, the syntax in the two previous sections "
#~ "is unusable. You’ll need to provide your credentials to the scheduled "
#~ "task. **Note that this method is highly discouraged since it means that "
#~ "your credentials will be in plain sight!**"
#~ msgstr ""
#~ "Dans ce cas-là, le token et les permissions “anonymes” sont inutilisables "
#~ "et il vous sera nécessaire d’indiquer vos identifiants dans la tâche "
#~ "cron. **Notez que cette solution est grandement déconseillée puisqu’elle "
#~ "implique que vos identifiants seront visibles en clair !**"

#~ msgid "by tag: `#tag`"
#~ msgstr "par tag: `#tag`"

#, no-wrap
#~ msgid "Feed management"
#~ msgstr "Organisation des flux"

#~ msgid ""
#~ "Available languages are: cs, de, en, es, fr, he, it, ko, nl, oc, pt-br, "
#~ "ru, tr, zh-cn."
#~ msgstr ""
#~ "Les langues disponibles sont : cs, de, en, es, fr, he, it, ko, nl, oc, pt-"
#~ "br, ru, tr, zh-cn."

#~ msgid "*Blue Lagoon* by **Mister aiR**"
#~ msgstr "*Blue Lagoon* par **Mister aiR**"

#~ msgid "*Dark* by **AD**"
#~ msgstr "*Dark* par **AD**"

#~ msgid "*Flat design* by **Marien Fressinaud**"
#~ msgstr "*Flat design* par **Marien Fressinaud**"

#~ msgid "*Origine* by **Marien Fressinaud**"
#~ msgstr "*Origine* par **Marien Fressinaud**"

#~ msgid "*Origine-compact* by **Kevin Papst**"
#~ msgstr "*Origine-compact* par **Kevin Papst**"

#~ msgid "*Pafat* by **Plopoyop**"
#~ msgstr "*Pafat* par **Plopoyop**"

#~ msgid "*Screwdriver* by **Mister aiR**"
#~ msgstr "*Screwdriver* par **Mister aiR**"

#~ msgid "*Swage* by **Patrick Crandol**"
#~ msgstr "*Swage* par **Patrick Crandol**"

#~ msgid "Tested with:"
#~ msgstr "Testé avec :"

#, no-wrap
#~ msgid ""
#~ "* Android\n"
#~ "  * [Readably](https://play.google.com/store/apps/details?id=com.isaiasmatewos.readably) (Closed source)\n"
#~ msgstr ""
#~ "* Android\n"
#~ "  * [Readably](https://play.google.com/store/apps/details?id=com.isaiasmatewos.readably) (Propriétaire)\n"

#, no-wrap
#~ msgid ""
#~ "* iOS\n"
#~ "  * [Fiery Feeds](https://apps.apple.com/app/fiery-feeds-rss-reader/id1158763303) (Closed source)\n"
#~ "  * [Unread](https://apps.apple.com/app/unread-rss-reader/id1252376153) (Commercial)\n"
#~ "  * [Reeder Classic](https://www.reederapp.com/classic/) (Commercial) (Use its Google Reader API / native FreshRSS option when possible)\n"
#~ msgstr ""
#~ "* iOS\n"
#~ "  * [Fiery Feeds](https://apps.apple.com/app/fiery-feeds-rss-reader/id1158763303) (Propriétaire)\n"
#~ "  * [Unread](https://apps.apple.com/app/unread-rss-reader/id1252376153) (Commercial)\n"
#~ "  * [Reeder Classic](https://www.reederapp.com/classic/) (Commercial) (Connectez-vous plutôt par son option Google Reader API)\n"

#, no-wrap
#~ msgid ""
#~ "* MacOS\n"
#~ "  * [ReadKit](https://apps.apple.com/app/readkit/id588726889) (Commercial)\n"
#~ msgstr ""
#~ "* MacOS\n"
#~ "  * [ReadKit](https://apps.apple.com/app/readkit/id588726889) (Commercial)\n"

#, no-wrap
#~ msgid "json"
#~ msgstr "json"

#, no-wrap
#~ msgid "sh"
#~ msgstr "sh"

#, no-wrap
#~ msgid ""
#~ "4. Click on first link “Check full server configuration”:\n"
#~ "\t* If you get *PASS* then you are done, all is good: you may proceed to step 6.\n"
#~ "\t* If you get *Bad Request!* or *Not Found*, then your server probably does not accept slashes `/` that are escaped `%2F`. Proceed to step 5.\n"
#~ "\t* If you get any other error message, proceed to step 5.\n"
#~ msgstr ""
#~ "4. Cliquer sur le premier lien “Check full server configuration”:\n"
#~ "\t* Si vous obtenez `PASS`, tout est bon : passer à l’étape 6.\n"
#~ "\t* Si vous obtenez *Bad Request!* ou *Not Found*, alors votre serveur ne semble pas accepter les slashs `/` qui sont encodés `%2F`. Passer à l’étape 5.\n"
#~ "\t* Si vous obtenez un autre message d’erreur, passer à l’étape 5.\n"

#, no-wrap
#~ msgid ""
#~ "5. Click on the second link “Check partial server configuration (without `%2F` support)”:\n"
#~ "\t* If you get `PASS`, then the problem is indeed that your server does not accept slashes `/` that are escaped `%2F`.\n"
#~ "\t\t* With Apache, remember the directive [`AllowEncodedSlashes On`](http://httpd.apache.org/docs/trunk/mod/core.html#allowencodedslashes)\n"
#~ "\t\t* Or use a client that does not escape slashes (such as EasyRSS), in which case proceed to step 6.\n"
#~ "\t* If you get *Service Unavailable!*, then check from step 1 again.\n"
#~ "\t* With __Apache__:\n"
#~ "\t\t* If you get *FAIL getallheaders!*, the combination of your PHP version and your Web server does not provide access to [`getallheaders`](http://php.net/getallheaders)\n"
#~ "\t\t\t* Turn on Apache `mod_setenvif` (often enabled by default), or `mod_rewrite` with the following procedure:\n"
#~ "\t\t\t\t* Allow [`FileInfo` in `.htaccess`](http://httpd.apache.org/docs/trunk/mod/core.html#allowoverride): see the [server setup](../admins/02_Installation.md) again.\n"
#~ "\t\t\t\t* Enable [`mod_rewrite`](http://httpd.apache.org/docs/trunk/mod/mod_rewrite.html):\n"
#~ "\t\t\t\t\t* With Debian / Ubuntu: `sudo a2enmod rewrite`\n"
#~ "\t* With __nginx__:\n"
#~ "\t\t* If you get *Bad Request!*, check your server `PATH_INFO` configuration.\n"
#~ "\t\t* If you get *File not found!*, check your server `fastcgi_split_path_info`.\n"
#~ "\t* If you get *FAIL 64-bit or GMP extension!*, then your PHP version does not pass the requirement of being 64-bit and/or have PHP [GMP](http://php.net/gmp) extension.\n"
#~ "\t\t* The easiest is to add the GMP extension. On Debian / Ubuntu: `sudo apt install php-gmp`\n"
#~ "\t* Update and try again from step 3.\n"
#~ msgstr ""
#~ "5. Cliquer sur le second lien “Check partial server configuration (without `%2F` support)”:\n"
#~ "\t* Si vous obtenez `PASS`, alors le problème est bien que votre serveur n’accepte pas les slashs `/` qui sont encodés `%2F`.\n"
#~ "\t\t* Avec Apache, vérifiez la directive [`AllowEncodedSlashes On`](http://httpd.apache.org/docs/trunk/mod/core.html#allowencodedslashes)\n"
#~ "\t\t* Ou utilisez un client qui n’encode pas les slashs (comme EasyRSS), auquel cas passer à l’étape 6.\n"
#~ "\t* Si vous obtenez *Service Unavailable!*, retourner à l’étape 6.\n"
#~ "\t* Avec __Apache__:\n"
#~ "\t\t* Si vous obtenez *FAIL getallheaders!*, alors la combinaison de votre version de PHP et de votre serveur Web ne permet pas l’accès à [`getallheaders`](http://php.net/getallheaders)\n"
#~ "\t\t\t* Activer Apache `mod_setenvif` (souvent activé par défault), ou `mod_rewrite` avec la procédure suivante :\n"
#~ "\t\t\t\t* Autoriser [`FileInfo` dans `.htaccess`](http://httpd.apache.org/docs/trunk/mod/core.html#allowoverride) : revoir [l’installation du serveur](01_Installation.md).\n"
#~ "\t\t\t\t* Activer [`mod_rewrite`](http://httpd.apache.org/docs/trunk/mod/mod_rewrite.html) :\n"
#~ "\t\t\t\t\t* Sur Debian / Ubuntu : `sudo a2enmod rewrite`\n"
#~ "\t* Avec __nginx__:\n"
#~ "\t\t* Si vous obtenez *Bad Request!*, vérifier la configuration `PATH_INFO` de votre serveur.\n"
#~ "\t\t* Si vous obtenez *File not found!*, vérifier la configuration `fastcgi_split_path_info` de votre serveur.\n"
#~ "\t* Si vous obtenez *FAIL 64-bit or GMP extension!*, alors votre installation PHP soit n’est pas en 64 bit, soit n’a pas l’extension PHP [GMP](http://php.net/gmp) activée.\n"
#~ "\t\t* Le plus simple est d’activer l’extension GMP. Sur Debian / Ubuntu : `sudo apt install php-gmp`\n"
#~ "\t* Mettre à jour et retourner à l’étape 3.\n"

#, no-wrap
#~ msgid ""
#~ "7. Pick a client supporting a Google Reader-like API. Selection:\n"
#~ "\t* Android\n"
#~ "\t\t* [News+](https://github.com/noinnion/newsplus/blob/master/apk/NewsPlus_202.apk) with [News+ Google Reader extension](https://github.com/noinnion/newsplus/tree/master/extensions/GoogleReaderCloneExtension) (Closed source)\n"
#~ "\t\t* [FeedMe 3.5.3+](https://play.google.com/store/apps/details?id=com.seazon.feedme) (Closed source)\n"
#~ "\t\t* [EasyRSS](https://github.com/Alkarex/EasyRSS) (Open source, [F-Droid](https://f-droid.org/packages/org.freshrss.easyrss/))\n"
#~ "\t* Linux\n"
#~ "\t\t* [FeedReader 2.0+](https://jangernert.github.io/FeedReader/) (Open source)\n"
#~ "\t* MacOS\n"
#~ "\t\t* [Vienna RSS](http://www.vienna-rss.com/) (Open source)\n"
#~ "\t\t* [Reeder Classic](https://www.reederapp.com/classic/) (Commercial)\n"
#~ "\t* iOS\n"
#~ "\t\t* [Reeder Classic](https://www.reederapp.com/classic/) (Commercial)\n"
#~ "\t* Firefox\n"
#~ "\t\t* [FreshRSS-Notify](https://addons.mozilla.org/firefox/addon/freshrss-notify-webextension/) (Open source)\n"
#~ msgstr ""
#~ "7. Vous pouvez maintenant tester sur une application mobile:\n"
#~ "\t* Android\n"
#~ "\t\t* [News+](https://github.com/noinnion/newsplus/blob/master/apk/NewsPlus_202.apk) avec [News+ Google Reader extension](https://github.com/noinnion/newsplus/tree/master/extensions/GoogleReaderCloneExtension) (Closed source)\n"
#~ "\t\t* [FeedMe 3.5.3+](https://play.google.com/store/apps/details?id=com.seazon.feedme) (Propriétaire)\n"
#~ "\t\t* [EasyRSS](https://github.com/Alkarex/EasyRSS) (Libre, [F-Droid](https://f-droid.org/packages/org.freshrss.easyrss/))\n"
#~ "\t* Linux\n"
#~ "\t\t* [FeedReader 2.0+](https://jangernert.github.io/FeedReader/) (Libre)\n"
#~ "\t* MacOS\n"
#~ "\t\t* [Vienna RSS](http://www.vienna-rss.com/) (Libre)\n"
#~ "\t\t* [Reeder Classic](https://www.reederapp.com/classic/) (Commercial)\n"
#~ "\t* iOS\n"
#~ "\t\t* [Reeder Classic](https://www.reederapp.com/classic/) (Commercial)\n"
#~ "\t* Firefox\n"
#~ "\t\t* [FreshRSS-Notify](https://addons.mozilla.org/firefox/addon/freshrss-notify-webextension/) (Libre)\n"

#, fuzzy
#~| msgid ""
#~| "As explained in the [security section](/en/User_documentation/"
#~| "Installation/Security), it’s highly recommended to make only the public "
#~| "section available at the domain level. With that configuration, `./p` is "
#~| "the root folder for http://demo.freshrss.org/, thus making `robots.txt` "
#~| "available at the root of the application."
#~ msgid ""
#~ "As explained in the [security section](/en/User_documentation/"
#~ "Installation/Security), it is highly recommended to make only the public "
#~ "section available at the domain level. With that configuration, ```./p``` "
#~ "is the root folder for https://demo.freshrss.org/, thus making ```robots."
#~ "txt``` available at the root of the application.\n"
#~ ">>>>>>> 8bdf7b09f208346481cba5e08edc55dfab8c8d63\n"
#~ msgstr ""
#~ "Comme expliqué dans les [conseils de sécurité](01_Installation."
#~ "md#conseils-de-securite), il est recommandé de faire pointer un nom de "
#~ "domaine vers ce sous-répertoire afin que seule la partie publique ne soit "
#~ "accessible par un navigateur web. De cette manière http://demo.freshrss."
#~ "org/ pointe vers le répertoire ```./p``` et le ```robots.txt``` se trouve "
#~ "bien à la racine du site : http://demo.freshrss.org/robots.txt."

#~ msgid "Here’s a list of feeds which don’t work:"
#~ msgstr "Voici une liste des flux qui ne fonctionnent pas :"

#~ msgid ""
#~ "http://foulab.org/fr/rss/Foulab_News: is not a W3C valid feed (November "
#~ "2014)"
#~ msgstr ""
#~ "http://foulab.org/fr/rss/Foulab_News : ne passe pas la validation W3C "
#~ "(novembre 2014)"

#~ msgid ""
#~ "http://eu.battle.net/hearthstone/fr/feed/news: is not a W3C valid feed "
#~ "(November 2014)"
#~ msgstr ""
#~ "http://eu.battle.net/hearthstone/fr/feed/news : ne passe pas la "
#~ "validation W3C (novembre 2014)"

#~ msgid ""
#~ "http://webseriesmag.blogs.liberation.fr/we/atom.xml: is not working for "
#~ "the user but passes validation (November 2014)"
#~ msgstr ""
#~ "http://webseriesmag.blogs.liberation.fr/we/atom.xml : ne fonctionne pas "
#~ "chez l’utilisateur mais passe l’ensemble des validations ci-dessus "
#~ "(novembre 2014)"

#~ msgid "Problem to solve"
#~ msgstr "Problème à résoudre"

#~ msgid "Préparer la sortie"
#~ msgstr "Préparer la sortie"

#~ msgid "S’assurer de l’état de dev"
#~ msgstr "S’assurer de l’état de dev"

#~ msgid ""
#~ "Avant de sortir une nouvelle version de FreshRSS, il faut vous assurer "
#~ "que le code est stable et ne présente pas de bugs majeurs. Idéalement, il "
#~ "faudrait que nos tests soient automatisés et exécutés avant toute "
#~ "publication."
#~ msgstr ""
#~ "Avant de sortir une nouvelle version de FreshRSS, il faut vous assurer "
#~ "que le code est stable et ne présente pas de bugs majeurs. Idéalement, il "
#~ "faudrait que nos tests soient automatisés et exécutés avant toute "
#~ "publication."

#~ msgid ""
#~ "Il faut aussi **vous assurer que le fichier CHANGELOG est à jour** dans "
#~ "la branche de dev avec les mises à jour de la ou les version(s) à sortir."
#~ msgstr ""
#~ "Il faut aussi **vous assurer que le fichier CHANGELOG est à jour** dans "
#~ "la branche de dev avec les mises à jour de la ou les version(s) à sortir."

#~ msgid "Processus Git"
#~ msgstr "Processus Git"

#~ msgid "Écriture du script de mise à jour"
#~ msgstr "Écriture du script de mise à jour"

#~ msgid "Il existe ensuite 5 fonctions à remplir :"
#~ msgstr "Il existe ensuite 5 fonctions à remplir :"

#~ msgid ""
#~ "- `apply_update()` qui se charge de sauvegarder le répertoire contenant "
#~ "les données, de vérifier sa structure, de télécharger le package "
#~ "FreshRSS, de le déployer et de tout nettoyer. Cette fonction est pré-"
#~ "remplie mais des ajustements peuvent être faits si besoin est (ex. "
#~ "réorganisation de la structure de `./data`). Elle retourne `true` si "
#~ "aucun problème n’est survenu ou une chaîne de caractères indiquant un "
#~ "soucis ; - `need_info_update()` retourne `true` si l’utilisateur doit "
#~ "intervenir durant la mise à jour ou `false` sinon ; - `ask_info_update()` "
#~ "affiche un formulaire à l’utilisateur si `need_info_update()` a retourné "
#~ "`true` ; - `save_info_update()` est chargée de sauvegarder les "
#~ "informations renseignées par l’utilisateur (issues du formulaire de "
#~ "`ask_info_update()`) ; - `do_post_update()` est exécutée à la fin de la "
#~ "mise à jour et prend en compte le code de la nouvelle version (ex. si la "
#~ "nouvelle version modifie l’objet `Minz_Configuration`, vous bénéficierez "
#~ "de ces améliorations)."
#~ msgstr ""
#~ "- `apply_update()` qui se charge de sauvegarder le répertoire contenant "
#~ "les données, de vérifier sa structure, de télécharger le package "
#~ "FreshRSS, de le déployer et de tout nettoyer. Cette fonction est pré-"
#~ "remplie mais des ajustements peuvent être faits si besoin est (ex. "
#~ "réorganisation de la structure de `./data`). Elle retourne `true` si "
#~ "aucun problème n’est survenu ou une chaîne de caractères indiquant un "
#~ "soucis ; - `need_info_update()` retourne `true` si l’utilisateur doit "
#~ "intervenir durant la mise à jour ou `false` sinon ; - `ask_info_update()` "
#~ "affiche un formulaire à l’utilisateur si `need_info_update()` a retourné "
#~ "`true` ; - `save_info_update()` est chargée de sauvegarder les "
#~ "informations renseignées par l’utilisateur (issues du formulaire de "
#~ "`ask_info_update()`) ; - `do_post_update()` est exécutée à la fin de la "
#~ "mise à jour et prend en compte le code de la nouvelle version (ex. si la "
#~ "nouvelle version modifie l’objet `Minz_Configuration`, vous bénéficierez "
#~ "de ces améliorations)."

#~ msgid "Mise à jour du fichier de versions"
#~ msgstr "Mise à jour du fichier de versions"

#~ msgid ""
#~ "Lorsque le script a été écrit et versionné, il est nécessaire de mettre à "
#~ "jour le fichier `./versions.php` qui contient une table de "
#~ "correspondances indiquant quelles versions sont mises à jour vers quelles "
#~ "autres versions."
#~ msgstr ""
#~ "Lorsque le script a été écrit et versionné, il est nécessaire de mettre à "
#~ "jour le fichier `./versions.php` qui contient une table de "
#~ "correspondances indiquant quelles versions sont mises à jour vers quelles "
#~ "autres versions."

#~ msgid "Et voici comment fonctionne cette table :"
#~ msgstr "Et voici comment fonctionne cette table :"

#~ msgid ""
#~ "- à gauche se trouve la version N, à droite la version N+1 ; - les "
#~ "versions `x.y.z-dev` sont **toutes** mises à jour vers `dev` ; - les "
#~ "versions stables sont mises à jour vers des versions stables ; - il est "
#~ "possible de sauter plusieurs versions d’un coup à condition que les "
#~ "scripts de mise à jour le prennent en charge ; - il est conseillé "
#~ "d’indiquer la correspondance de la version courante vers sa potentielle "
#~ "future version en précisant que cette version n’existe pas encore. Tant "
#~ "que le script correspondant n’existera pas, rien ne se passera."
#~ msgstr ""
#~ "- à gauche se trouve la version N, à droite la version N+1 ; - les "
#~ "versions `x.y.z-dev` sont **toutes** mises à jour vers `dev` ; - les "
#~ "versions stables sont mises à jour vers des versions stables ; - il est "
#~ "possible de sauter plusieurs versions d’un coup à condition que les "
#~ "scripts de mise à jour le prennent en charge ; - il est conseillé "
#~ "d’indiquer la correspondance de la version courante vers sa potentielle "
#~ "future version en précisant que cette version n’existe pas encore. Tant "
#~ "que le script correspondant n’existera pas, rien ne se passera."

#~ msgid ""
#~ "Il est **très fortement** indiqué de garder ce fichier rangé selon les "
#~ "numéros de versions en séparant les versions stables et de dev."
#~ msgstr ""
#~ "Il est **très fortement** indiqué de garder ce fichier rangé selon les "
#~ "numéros de versions en séparant les versions stables et de dev."

#~ msgid ""
#~ "Lorsque vous serez satisfait, mettez à jour update.freshrss.org avec le "
#~ "nouveau script et en testant de nouveau puis passez à la suite."
#~ msgstr ""
#~ "Lorsque vous serez satisfait, mettez à jour update.freshrss.org avec le "
#~ "nouveau script et en testant de nouveau puis passez à la suite."

#~ msgid "Mise à jour des services FreshRSS"
#~ msgstr "Mise à jour des services FreshRSS"

#~ msgid ""
#~ "Deux services sont à mettre à jour immédiatement après la mise à jour de "
#~ "update.freshrss.org :"
#~ msgstr ""
#~ "Deux services sont à mettre à jour immédiatement après la mise à jour de "
#~ "update.freshrss.org :"

#~ msgid "Annoncer publiquement la sortie"
#~ msgstr "Annoncer publiquement la sortie"

#~ msgid ""
#~ "Lorsque tout fonctionne, il est temps d’annoncer la sortie au monde "
#~ "entier !"
#~ msgstr ""
#~ "Lorsque tout fonctionne, il est temps d’annoncer la sortie au monde "
#~ "entier !"

#~ msgid ""
#~ "- sur GitHub en créant [une nouvelle release](https://github.com/FreshRSS/"
#~ "FreshRSS/releases/new) ; - sur le blog de freshrss.org au minimum pour "
#~ "les versions stables (écrire l’article sur [FreshRSS/freshrss.org]"
#~ "(https://github.com/FreshRSS/freshrss.org)).  - sur Twitter (compte "
#~ "[@FreshRSS](https://twitter.com/FreshRSS)) ; - et sur mailing@freshrss."
#~ "org ;"
#~ msgstr ""
#~ "- sur GitHub en créant [une nouvelle release](https://github.com/FreshRSS/"
#~ "FreshRSS/releases/new) ; - sur le blog de freshrss.org au minimum pour "
#~ "les versions stables (écrire l’article sur [FreshRSS/freshrss.org]"
#~ "(https://github.com/FreshRSS/freshrss.org)).  - sur Twitter (compte "
#~ "[@FreshRSS](https://twitter.com/FreshRSS)) ; - et sur mailing@freshrss."
#~ "org ;"

#~ msgid "Lancer la prochaine version de développement"
#~ msgstr "Lancer la prochaine version de développement"

#~ msgid ""
#~ "Pensez aussi à mettre à jour update.freshrss.org pour qu’il prenne en "
#~ "compte la version de développement actuelle."
#~ msgstr ""
#~ "Pensez aussi à mettre à jour update.freshrss.org pour qu’il prenne en "
#~ "compte la version de développement actuelle."

#~ msgid "With brackets"
#~ msgstr "Le cas des parenthèses"

#~ msgid ""
#~ "- RSS and Atom aggregation - Mark article as favorite if you liked it or "
#~ "if you want to read it later - Filter and search functionality helps to "
#~ "easily find articles - Statistics to show you the publishing frequency "
#~ "all the websites you follow - Import/export of your feeds into OPML "
#~ "format - Several themes created by the community - \"Google Reader\"-like "
#~ "API to connect Android applications - The application is \"responsive,\" "
#~ "which means it adapts to small screens so you can bring articles in your "
#~ "pocket - Self-hosted: the code is free (under AGPL3 licence), so you can "
#~ "host your own instance of FreshRSS - Multi-user, so you can also host for "
#~ "your friends and family - And a lot more!"
#~ msgstr ""
#~ "- Agrégation des flux RSS et Atom.- Utilisez les favoris pour marquer les "
#~ "articles qui vous ont plu ou que vous souhaitez lire plus tard.- Le "
#~ "système de filtrage et de recherche permettent de cibler exactement les "
#~ "articles que vous souhaitez lire.- Les statistiques permettent de savoir "
#~ "en un coup d’œil quels sont les sites qui publient le plus, ou à "
#~ "l’inverse, le moins.- Importation / exportation des flux au format OPML.- "
#~ "Multi-thèmes pour changer l’habillage de FreshRSS.- « *Responsive design* "
#~ "» : l’application s’adapte aux petits écrans pour emporter FreshRSS dans "
#~ "votre poche.- Multi-utilisateurs pour héberger plusieurs personnes sur "
#~ "une même installation.- API Google Reader pour pouvoir y brancher des "
#~ "applications Android.- Auto-hébergeable : le code source est libre "
#~ "(AGPL3) et vous pouvez donc l’héberger sur votre propre serveur.- Et bien "
#~ "d’autres !"
