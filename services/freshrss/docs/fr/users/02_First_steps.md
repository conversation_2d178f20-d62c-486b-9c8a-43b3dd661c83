Découvrir un nouveau logiciel n’est pas toujours facile. Si nous avons voulu
FreshRSS le plus intuitif possible, vous aurez peut-être besoin d’un coup de
main pour le maîtriser.

Cette section se propose de vous aider dans la prise en main de l’outil. Il
ne s’agit que de liens menant vers les autres pages de la documentation mais
ordonnées dans un ordre spécifique aux nouveaux arrivants.

[Après l’installation](../../en/admins/03_Installation.md), la première
chose à faire est d’ajouter un ou plusieurs sites à suivre. Pour cela
plusieurs choix s’offrent à vous :

1. [Ajouter un flux manuellement](04_Subscriptions.md#ajouter-un-flux)
2. [Importer un fichier OPML ou JSON](04_Subscriptions.md#import-et-export)
3. [Utiliser le bookmark dédié](04_Subscriptions.md#utiliser-le-bookmark)

Une fois que vous avez ajouté vos flux à FreshRSS, il est temps de les
lire. Pour cela, trois modes de lecture s’offrent à vous :

1. [La vue normale](03_Main_view.md#la-vue-normale) qui permet de voir et de
	lire rapidement les nouveaux articles
2. [La vue globale](03_Main_view.md#la-vue-globale) est destinée à vous
	offrir un panorama de l’état de vos flux
3. [La vue lecture](03_Main_view.md#la-vue-lecture) est pensée pour vous
	offrir un meilleur confort de lecture

Bien, vous maitrisez maintenant la vue que vous préférez ? Il est temps de
vous offrir un peu plus de confort de lecture. FreshRSS est grandement
configurable et c’est à vous de trouver la configuration qui vous conviendra
le plus. Voici tout de même quelques pistes pour améliorer votre quotidien
sur FreshRSS :

* [Rangez vos flux dans des catégories](04_Subscriptions.md#organisation-des-flux)
* [Configurez votre page d’accueil](05_Configuration.md#personnaliser-la-vue)
* [Configurez vos options de lecture](05_Configuration.md#reading)
* [Mettez à jour vos flux](03_Main_view.md#rafraîchir-les-flux)
* [Filtrez les articles](03_Main_view.md#filtrer-les-articles) pour accéder	rapidement à ceux que vous voulez lire en priorité
* [Retrouvez un article](03_Main_view.md#rechercher-des-articles) qui a été	publié il y a quelques jours ou mois
* [Accédez à vos flux même sur mobile](06_Mobile_access.md)
* [Ajoutez quelques extensions](https://github.com/FreshRSS/Extensions)
* [Foire aux questions](07_Frequently_Asked_Questions.md)
* [Recevoir les articles instantannément grâce à WebSub](08_PubSubHubbub.md)
