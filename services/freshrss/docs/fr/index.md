![Logo de FreshRSS](img/logo_freshrss.png)

FreshRSS est un agrégateur et lecteur de flux RSS. Il permet de regrouper
l’actualité de plusieurs sites différents dans un endroit unique pour que
vous puissiez la lire sans devoir aller de site en site.

FreshRSS a été conçu comme un agrégateur puissant et propose des tas de
fonctionnalités :

* Agrégation des flux RSS et Atom.
* Utilisez les favoris pour marquer les articles qui vous ont plu ou que
	vous souhaitez lire plus tard.
* Le système de filtrage et de recherche permettent de cibler exactement les
	articles que vous souhaitez lire.
* Les statistiques permettent de savoir en un coup d’œil quels sont les
	sites qui publient le plus, ou à l’inverse, le moins.
* Importation / exportation des flux au format OPML.
* Multi-thèmes pour changer l’habillage de FreshRSS.
* API Google Reader pour pouvoir y brancher des applications Android.
* « *Responsive design* » : l’application s’adapte aux petits écrans pour
	emporter FreshRSS dans votre poche.
* Auto-hébergeable : le code source est libre (AGPL3) et vous pouvez donc
	l’héberger sur votre propre serveur.
* Multi-utilisateurs pour héberger plusieurs personnes sur une même installation.
* Et bien d’autres !

Cette documentation est divisée en plusieurs parties :

* La [documentation utilisateurs](./users/02_First_steps.md) pour découvrir
	les fonctionnalités de FreshRSS.
* La [documentation administrateurs](../en/admins/01_Index.html) (en
	anglais) pour l’installation et la maintenance de FreshRSS.
* La [documentation développeurs](./developers/01_First_steps.md) pour
	savoir comment contribuer et mieux comprendre le code source de FreshRSS.
* Le [guide de contribution](./contributing.md) pour nous aider à développer FreshRSS.
