![FreshRSS logo](img/logo_freshrss.png)

# FreshRSS manual (English)

FreshRSS is an RSS aggregator and reader. It allows you to read and follow several news websites at a glance without the need to browse from one website to another.

## Features

FreshRSS has a lot of features including:

* RSS and Atom aggregation
* Mark article as favorite if you liked it or if you want to read it later
* Filter and search functionality helps to easily find articles ([more information](./users/10_filter.html))
* Statistics to show you the publishing frequency all the websites you follow
* Import/export of your feeds into OPML format ([more information](./users/04_Subscriptions.html#import--export))
* Several themes created by the community ([more information](./admins/11_Themes.html))
* Several extensions created by the community ([more information](./admins/15_extensions.html))
* "Google Reader"-like API to connect Android applications ([more information](./users/06_Mobile_access.html#access-via-mobile-app))
* The application is "responsive," which means it adapts to small screens so you can bring articles in your pocket
* Self-hosted: the code is free ([under AGPL3 licence](https://github.com/FreshRSS/FreshRSS/blob/edge/LICENSE.txt)), so you can host your own instance of FreshRSS ([more information](./admins/02_Prerequisites.md))
* Multi-user, so you can also host for your friends and family ([more information](./admins/12_User_management.md))
* share article links with a bunch of services ([more information](./users/08_sharing_services.md))
* And a lot more!

## Manual Chapters

This documentation is split into different sections:

* [User documentation](./users/02_First_steps.md), where you can discover all the possibilities offered by FreshRSS
* [Administrator documentation](./admins/01_Index.md) for detailed installation and maintenance related tasks
* [Developer documentation](./developers/01_Index.md) to guide you in the source code of FreshRSS and to help you if you want to contribute
* [Contributor guidelines](./contributing.md) for those who want to help improve FreshRSS

## Demo

The official demo of FreshRSS is available under [https://demo.freshrss.org/](https://demo.freshrss.org/).

Login credentials:

* Username: demo
* Password: demodemo

Another chance to try out, but not official supported by FreshRSS: The application is listed on Softaculous [https://www.softaculous.com/apps/rss/FreshRSS](https://www.softaculous.com/apps/rss/FreshRSS).

## Licence

FreshRSS is licensed under the [GNU Affero General Public License v3.0](https://github.com/FreshRSS/FreshRSS/blob/edge/LICENSE.txt).
