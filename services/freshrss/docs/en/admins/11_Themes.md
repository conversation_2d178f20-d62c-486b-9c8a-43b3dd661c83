# Theming

**Note: Currently personal themes are not officially supported and may be overwritten when updating. Be sure to keep backups!**

**For small theme changes, the official [CustomCSS extension](https://github.com/FreshRSS/Extensions) is recommended.**

Themes should be installed at `FreshRSS/p/themes/my-theme-name`. Docker users can use:

```sh
-v /home/<USER>/my-theme-name/:/var/www/FreshRSS/p/themes/my-theme-name/
```

or a similar method to add their theme to their FreshRSS instance.

## Creating themes

Information on creating themes can be found in [the developer documentation.](../developers/04_Frontend/02_Design.md)
