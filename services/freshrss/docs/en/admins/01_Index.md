# FreshRSS Administration

Learn how to install, update, and backup FreshRSS, as well as how to use the command line tools.

## System Basics

1. [Prerequisites](02_Prerequisites.md): What you’ll need to run FreshRSS
2. [General installation instructions](03_Installation.md) for FreshRSS
3. [Update your installation](04_Updating.md) to the latest stable or development version
4. [Logging and error messages](logs_and_errors.md) in case of any troubles

## Tutorials and Examples

### General

* [Backing up FreshRSS](05_Backup.md)
* [Installing and managing extensions](15_extensions.md)
* [Installing themes](11_Themes.md)
* [Setting Up Automatic Feed Updating](08_FeedUpdates.md)
* [Database configuration](DatabaseConfig.md)
* [Using the command line interface (CLI)](https://github.com/FreshRSS/FreshRSS/tree/edge/cli)
* [Configuration without an user interface](17_configs_not_ui.md)
* [Frequently asked questions](04_Frequently_Asked_Questions.md)

### User access

* [User management](12_User_management.md)
* [Access Control](09_AccessControl.md)
* [OpenID Connect](16_OpenID-Connect.md)
* [Configuring the email address validation](05_Configuring_email_validation.md)

### Web server configuration

* [Apache/Nginx configuration files](10_ServerConfig.md)
* [Reverse proxy with Caddy](Caddy.md)

### Special server information

* [Installation on Debian 9/Ubuntu 16.04](06_LinuxInstall.md)
* [Installation on Cloud Providers](14_CloudProviders.md)
* [Updating on Debian 9/Ubuntu 16.04](07_LinuxUpdate.md)

