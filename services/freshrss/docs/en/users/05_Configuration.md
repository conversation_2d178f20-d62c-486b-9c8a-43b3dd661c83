
# Configuration

## Display

### Language

FreshRSS is currently available in 22 languages. After confirming your choice, the interface will be displayed in your preferred language.
Depending on the language chosen, parts of the interface may not be not translated yet. If you’re willing to help translate
the missing bits or would like to add a new language, please take a look at how you can [contribute to the project](../contributing.md#contribute-to-internationalization-i18n).

Some parts of FreshRSS are not translated and are not intended to be translated either. For now, this includes the logs visible in the application as well as the log generated by automatic update scripts.

Available languages are:

| Language (English name) | Language (Endonym) | Ordered by language code (ISO-639-1) |
|:------------------------|:-----------------------|:-------------------------------------|
| Czech                   | Čeština                | cs                                   |
| German                  | Deutsch                | de                                   |
| Greek                   | Ελληνικά               | el                                   |
| English                 | English                | en                                   |
| English (United States) | English (United States) | en-us                               |
| Spanish                 | Español                | es                                   |
| French                  | Français               | fr                                   |
| Hebrew                  | עברית                  | he                                   |
| Hungarian               | Magyar                 | hu                                   |
| Indonesian              | Bahasa Indonesia       | id                                   |
| Italian                 | Italiano               | it                                   |
| Japanese                | 日本語                  | ja                                   |
| Korean                  | 한국어                  | ko                                   |
| Latvian                 | Latviešu               | lv                                   |
| Dutch                   | Nederlands             | nl                                   |
| Occitan                 | Occitan                | oc                                   |
| Polish                  | Polski                 | pl                                   |
| Brazilian Portuguese    | Português (Brasil)     | pt-br                                |
| Russian                 | Русский                | ru                                   |
| Slovak                  | Slovenčina             | sk                                   |
| Turkish                 | Türkçe                 | tr                                   |
| Chinese (Simplified, People’s Republic of China) | 简体中文      | zh-cn                 |
| Chinese (Traditional, Taiwan) | 正體中文          | zh-tw                                |

### Theme

There’s no accounting for tastes, which is why FreshRSS offers 13 official themes:

| Theme       | designed by                                   | Notes                                                         |
|:--------------|:-------------------------------------------------------|:--------------------------------------------------------------|
| Alternative Dark | Ghost | |
| Ansum | Thomas Guesnon  | |
| ~~Blue Lagoon~~     |Mister aiR | Was removed with FreshRSS V1.22.0 |
| Dark | AD | |
| Dark pink | Miicat_47 | |
| Flat design | Marien Fressinaud | |
| Mapco | Thomas Guesnon  | |
| Nord theme | joelchrono12 | |
| Origine | Marien Fressinaud | (default theme) |
| Origine-compact | Kevin Papst | |
| Pafat | Plopoyop | |
| ~~Screwdriver~~ | Mister aiR | Was removed with FreshRSS V1.22.0 |
| Swage | Patrick Crandol | |

To select a theme, simply scroll through the themes and select one that strikes your fancy. After confirmation, the theme will be applied to the interface.

If you can’t find any themes you like, it’s always possible to [create your own](../developers/04_Frontend/02_Design.md) and [install it](../admins/11_Themes.md). For small theme changes, the official [CustomCSS extension](https://github.com/FreshRSS/Extensions) is recommended.

### Content width

Some people prefer short lines of text, while others prefer to maximize the available screen space. To satisfy the maximum number of people, it’s possible to customize the width of the displayed content. There are four settings available:

* **Fine** displays content up to a maximum width of 550 pixels
* **Medium** displays content up to a maximum width of 800 pixels
* **Large** displays content up to a maximum width of 1000 pixels
* **No limit** displays the content on 100% of the available space

### Article icons

Please note that this section only affects normal view.

![Article icons configuration](../img/users/configuration.article.icons.png)

Each article is rendered with a header (top line) and a footer (bottom line).
In that section, you can choose what will be displayed in those.

If you disable every item in the top line, you’ll still be able to see it since
it contains the feed name and the article title. But if you do the same thing for
the bottom line, it will be empty.

### HTML5 notification timeout

After automatically updating the feeds, FreshRSS can pop up a notification using the HTML5 notification API.

The duration of this notification can be set. By default, the value is 0.

### Show the navigation button

By default, FreshRSS displays buttons to ease the article navigation when browsing on mobile. The drawback is that they eat up some precious space.

![navigation button configuration](../img/users/configuration.navigation.button.png)

If you don’t use those buttons because you never browse on mobile or because you browse with gestures, you can disable them from the interface.

## Reading

### Number of articles per page

This setting defines the number of articles to display at once before needing to load more. In normal and reading view, more articles are loaded automatically. In global view, a button will appear at the bottom of the list.

### Articles to display

The status of articles to display when loading FreshRSS. "Adjust showing" will display only unread articles by default, but will display all articles when there are no unread articles to show.

### Use “lazy load” mode to load images

This will set images to load as they are viewed. This can save data, but will can cause images to load in later.

## Archival

### Archiving

These are the global options for fetching and retaining articles from feeds. They can be overridden by individual feed’s settings.

### Maintenance

This allows for purging/optimizing the current user’s articles in the database.

## Sharing

To make your life easier, you can share articles straight from FreshRSS.

At the moment, FreshRSS supports [20+ sharing services](08_sharing_services.md), ranging from self-hosted services (Shaarli, etc.) to proprietary services (Facebook, etc.).

By default, the sharing list is empty.
![Sharing configuration](../img/users/configuration.sharing.png)

To add a new item to the list, please follow the following simple steps:

1. Select the desired sharing method in the drop-down list.
2. Press the ```✚``` button to add it to the list.
3. Configure the method in the list. All  names can be modified in the display. Some methods need the sharing URL to be able to work properly (ex: Shaarli).
4. Submit your changes.

To remove an item from the list, follow those simple steps:

1. Press the ```❌``` button next to the share method you want to remove.
2. Submit your changes.


## Shortcuts

To ease the use of the application, FreshRSS comes with a lot of predefined keyboard shortcuts.
They allow actions to improve the user experience with a keyboard.

Of course, if you’re not satisfied with the key mapping, you can change you configuration to fit your needs.

There are 4 types of shortcuts:

1. Views: they allow switching views with ease.
2. Navigation: they allow navigation through articles, feeds, and categories.
3. Article actions: they allow interactions with an article, like sharing or opening it on the original web-site.
4. Other actions: they allow other interactions with the application, like opening the user queries menu or accessing the documentation.

It’s worth noting that the share article action has two levels. Once you press the shortcut, a menu containing all the share options opens.
To choose one share option, you need to select it by its number. When there is only one option, it’s selected automatically though.

The same process applies to the user queries.

Be aware that there is no validation on the selected shortcuts.
This means that if you assign a shortcut to more than one action, you’ll end up with some unexpected behavior.

## User queries

You can configure your [user queries](./user_queries.md) in that section.

## Profile

You can change your email address or password here. The authentication token is required for accessing the aggregated RSS feed for a user. A blank token will disable accessing the RSS feed without being logged in.

## Extensions

Extensions can be managed from this menu. Note that while extensions can be removed from the web interface, they cannot be added from it.

Some extensions have configurations and these can be changed in the manage page, which opens with the button near the name of the extension.

### User CSS

It gives ability to create user-specific CSS rules to apply in addition of the actual theme.

#### Example: Getting rid of Top Menu Items

The Top Menu within the mobile view might look a little bit cluttered, depending on the theme. The following CSS rules allow to hide unnecessary top menu buttons or input boxes.

```css
@media (max-width: 840px)
{
    /* Hides "Actions" Menu in Mobile View */
    #nav_menu_actions {
        display: none;
    }

    /* Hides "Views" Menu in Mobile View */
    #nav_menu_views {
        display: none;
    }

    /* Hides "Search" Input Box in Mobile View */
    .nav_menu .item.search {
        display: none;
    }

    /* Hides the Dropdown Menu Button next to the "Mark all read" Button in Mobile View */
    #mark-read-menu .dropdown {
        display: none;
    }
}
```

### User JS

It gives ability to create user-specific JS.

## Users

> **TODO**

### Authentication methods

#### HTTP Authentication (Apache)

1. User control is based on the `.htaccess` file.
2. It is best practice to place the `.htaccess` file in the `./i/` subdirectory so the API and other third party services can work.
3. If you want to limit all access to registered users only, place the file in the FreshRSS directory itself or in a parent directory. Note that [WebSub](WebSub.md) and API will not work!
4. Example `.htaccess` file for a user "marie":

```apache
AuthUserFile /home/<USER>/repertoire/.htpasswd
AuthGroupFile /dev/null
AuthName "Chez Marie"
AuthType Basic
Require user marie
```

More information can be found in the [Apache documentation](http://httpd.apache.org/docs/trunk/howto/auth.html#gettingitworking).
