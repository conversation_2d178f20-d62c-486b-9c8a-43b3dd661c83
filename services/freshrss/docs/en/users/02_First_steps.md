# User Documentation

Learning how to handle a new application is not always easy. We’ve tried to make FreshRSS as intuitive as possible, but you might still need a little help to master the program.

This section will guide you to the pages you need to get started. The order is tailored to newcomers.

## Add Feeds

[After installing the application](../admins/03_Installation.md), the first step is to add some feeds. You have a few options:

1. [Add a feed manually](04_Subscriptions.md#adding-a-feed)
2. [Import an OPML or JSON file](04_Subscriptions.md#import-and-export)
3. [Use the bookmarklet](04_Subscriptions.md#use-bookmarklet)

## Reading Views

Once you have added your feeds to FreshRSS, it is time to read them. There are three available reading modes:

1. [The normal view](03_Main_view.md#normal-view) enables you to quickly read new articles
2. [The global view](03_Main_view.md#global-view) shows you an overview of the status of your feeds in one glance
3. [The reader view](03_Main_view.md#reader-view) offers you a comfortable reading experience

## Customize

Now that you’ve mastered basic use, it’s time to configure FreshRSS to improve your reading experience. It’s highly configurable, so it’s recommended to play around with them to find a configuration that suits you well. Here are a few resources to help you improve your daily FreshRSS experience:

* [Organize your feeds in categories](04_Subscriptions.md#feed-management)
* [Change the home page](05_Configuration.md#changing-the-view)
* [Choose the reading options](05_Configuration.md#reading-options)
* [Refresh feeds](09_refreshing_feeds.md)
* [Filter articles](10_filter.md) for a fast access to a selection
* [search for an article](10_filter.md#with-the-search-field) published some time ago
* [Access your feeds on a mobile device](06_Mobile_access.md)
* [Add some extensions](https://github.com/FreshRSS/Extensions)
* [Frequently asked questions](07_Frequently_Asked_Questions.md)
* [Receive articles instantly with WebSub](WebSub.md)

FreshRSS has a built-in engine that [scrapes a website to create an own feed](11_website_scraping.md).
