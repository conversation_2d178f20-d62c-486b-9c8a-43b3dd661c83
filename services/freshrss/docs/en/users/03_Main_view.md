# Views

FreshRSS has three primary viewing modes: Normal, Global, and Reader view.

## Normal view

Normal view will allow you to view articles in a compressed view. They can be separated by category or individual feed, or viewed in the "main stream" containing all feeds. Clicking a feed in the sidebar (mobile users will need to click the folder icon to open it) will open that feed’s view.

### Article List

By default, the normal view includes six items per article. From left to right:
* **Read status:** An envelope icon to show if the article has been read or not. Closed envelopes are unread, open envelopes are read. Clicking on the icon will toggle the read status.
* **Favourite status:** A star icon to show if the article has been favourited or not. Filled stars are favourited, empty stars are not. Clicking on the icon will toggle the favourite status.
* **Feed name:** The name of the feed that the article is from. Clicking the feed name will move to that feed’s view in normal view.
* **Article title:** The title of the article. Clicking will open the article for viewing within FreshRSS.
* **Article date/time:** The time the article was posted.
* **Link to original article:** A globe icon that can be clicked to go to the article on the original website.

### Normal View Sidebar

Clicking the gear icon next to an individual feed will display additional options for that feed.
* **Filter:** Run the defined filter to mark articles as read
* **Statistics:** View statistics about the feed
* **See website:** Open the feed’s website in another tab
* **Manage:** Configure the feed
* **Actualize:** Force-update the feed
* **Mark as read:** Mark all items in the feed as read

## Global view

Global view allows quick views of feed’s statuses at once. Feeds and categories are shown with the number of unread articles next to them. Clicking a feed’s name will open it in a view similar to normal view.

## Reader view

Reader view will display a feed will all articles already open for reading. Feeds can be switched by clicking the folder icon at the top to bring up the category/feed sidebar.

---
Read more:
* [Refreshing the feeds](./09_refreshing_feeds.md)
* [Filter the feeds and search](./10_filter.md)
* [User queries](./user_queries.md)
