# FreshRSS Development

## First Steps

Start by creating your development environment. A guide to setting up FreshRSS’s development environment can be found on [the appropriate page](02_First_steps.md).

## After That

* [GitHub Branching and Pushing](02_GitHub.md)
* [Running tests](03_Running_tests.md)
* [Creating a pull request](04_Pull_requests.md)
* [Releasing a new version](05_Release_new_version.md)
* [Reporting bugs](06_Reporting_Bugs.md)
* [Fever API](06_Fever_API.md)
* [GoogleReader API](06_GoogleReader_API.md)

## Backend Development

* [Making extensions for FreshRSS](03_Backend/05_Extensions.md)
* [Database Schema](03_Backend/01_Database_schema.md)
* [External libraries](03_Backend/03_External_libraries.md)

## Frontend Development

* [View files](04_Frontend/01_View_files.md)
* [Design (Themes/Theming)](04_Frontend/02_Design.md)

## Namespaces

* [OPML FreshRSS namespace](OPML.md)

## Minz

Minz is the homemade PHP framework used by FreshRSS. See the [dedicated documentation](Minz/index.md).
