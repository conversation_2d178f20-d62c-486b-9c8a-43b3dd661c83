# WARNING: this file is generated with translation-update.sh
# DO NOT modify this file manually!
[po4a_langs] fr
[po4a_paths] i18n/templates/freshrss.pot $lang:i18n/freshrss.$lang.po
[type: text] en/./contributing.md $lang:$lang/./contributing.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/01_Index.md $lang:$lang/./developers/01_Index.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/02_First_steps.md $lang:$lang/./developers/02_First_steps.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/02_GitHub.md $lang:$lang/./developers/02_GitHub.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/03_Backend/01_Database_schema.md $lang:$lang/./developers/03_Backend/01_Database_schema.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/03_Backend/03_External_libraries.md $lang:$lang/./developers/03_Backend/03_External_libraries.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/03_Backend/05_Extensions.md $lang:$lang/./developers/03_Backend/05_Extensions.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/03_Running_tests.md $lang:$lang/./developers/03_Running_tests.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/04_Frontend/01_View_files.md $lang:$lang/./developers/04_Frontend/01_View_files.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/04_Frontend/02_Design.md $lang:$lang/./developers/04_Frontend/02_Design.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/04_Pull_requests.md $lang:$lang/./developers/04_Pull_requests.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/05_Release_new_version.md $lang:$lang/./developers/05_Release_new_version.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/06_Fever_API.md $lang:$lang/./developers/06_Fever_API.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/06_GoogleReader_API.md $lang:$lang/./developers/06_GoogleReader_API.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/06_Reporting_Bugs.md $lang:$lang/./developers/06_Reporting_Bugs.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/Minz/index.md $lang:$lang/./developers/Minz/index.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/Minz/migrations.md $lang:$lang/./developers/Minz/migrations.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./developers/OPML.md $lang:$lang/./developers/OPML.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./index.md $lang:$lang/./index.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./internationalization.md $lang:$lang/./internationalization.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./users/02_First_steps.md $lang:$lang/./users/02_First_steps.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./users/03_Main_view.md $lang:$lang/./users/03_Main_view.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./users/04_Subscriptions.md $lang:$lang/./users/04_Subscriptions.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./users/05_Configuration.md $lang:$lang/./users/05_Configuration.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./users/06_Mobile_access.md $lang:$lang/./users/06_Mobile_access.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./users/07_Frequently_Asked_Questions.md $lang:$lang/./users/07_Frequently_Asked_Questions.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./users/08_sharing_services.md $lang:$lang/./users/08_sharing_services.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./users/09_refreshing_feeds.md $lang:$lang/./users/09_refreshing_feeds.md opt:"-o markdown" opt:"-M utf-8"
[type: text] en/./users/10_filter.md $lang:$lang/./users/10_filter.md opt:"-o markdown" opt:"-M utf-8"
