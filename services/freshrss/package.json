{"name": "freshrss", "type": "module", "description": "A free, self-hostable aggregator", "homepage": "https://freshrss.org/", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/FreshRSS/FreshRSS/issues"}, "keywords": ["news", "aggregator", "RSS", "Atom", "WebSub"], "repository": {"type": "git", "url": "https://github.com/FreshRSS/FreshRSS.git"}, "license": "AGPL-3.0", "engines": {"node": ">=18"}, "scripts": {"eslint": "eslint .", "eslint_fix": "eslint --fix .", "markdownlint": "markdownlint '**/*.md'", "markdownlint_fix": "markdownlint --fix '**/*.md'", "rtlcss": "rtlcss -d p/themes/ && find p/themes/ -type f -name '*.rtl.rtl.css' -delete", "stylelint": "stylelint '**/*.css' && stylelint --custom-syntax postcss-scss '**/*.scss'", "stylelint_fix": "stylelint --fix '**/*.css' && stylelint --fix --custom-syntax postcss-scss '**/*.scss'", "test": "npm run eslint && npm run stylelint && npm run markdownlint", "fix": "npm run rtlcss && npm run stylelint_fix && npm run eslint_fix && npm run markdownlint_fix"}, "devDependencies": {"eslint": "^9.30.0", "@eslint/js": "^9.20.0", "globals": "^16.3.0", "markdownlint-cli": "^0.45.0", "neostandard": "^0.12.1", "rtlcss": "^4.3.0", "sass": "^1.89.2", "stylelint": "^16.21.0", "stylelint-config-recommended-scss": "^15.0.1", "stylelint-order": "^7.0.0", "@stylistic/stylelint-plugin": "^3.1.3"}, "rtlcssConfig": {}}