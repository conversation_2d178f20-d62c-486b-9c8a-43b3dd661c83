<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="FreshRSS">
	<arg name="extensions" value="php,phtml"/>
	<arg name="tab-width" value="4"/>
	<exclude-pattern>./.git/</exclude-pattern>
	<exclude-pattern>./data/config.php</exclude-pattern>
	<exclude-pattern>./data/update.php</exclude-pattern>
	<exclude-pattern>./data/users/*/config.php</exclude-pattern>
	<exclude-pattern>./extensions/</exclude-pattern>
	<exclude-pattern>./lib/http-conditional.php</exclude-pattern>
	<exclude-pattern>./lib/marienfressinaud/</exclude-pattern>
	<exclude-pattern>./lib/phpgt/</exclude-pattern>
	<exclude-pattern>./lib/phpmailer/</exclude-pattern>
	<exclude-pattern>./lib/simplepie/</exclude-pattern>
	<exclude-pattern>./node_modules/</exclude-pattern>
	<exclude-pattern>./p/scripts/vendor/</exclude-pattern>
	<exclude-pattern>./vendor/</exclude-pattern>
	<!-- Additional exclusions for Extensions: -->
	<exclude-pattern>./symbolic/</exclude-pattern>
	<exclude-pattern>./third-party/</exclude-pattern>
	<exclude-pattern>./tmp/</exclude-pattern>
	<exclude-pattern>./xExtension-TTRSS_API</exclude-pattern>
	<rule ref="PSR12">
		<exclude name="Generic.ControlStructures.InlineControlStructure.NotAllowed"/>
		<exclude name="Generic.Formatting.DisallowMultipleStatements.SameLine"/>
		<exclude name="Generic.WhiteSpace.DisallowTabIndent.NonIndentTabsUsed"/>
		<exclude name="Generic.WhiteSpace.DisallowTabIndent.TabsUsed"/>
		<exclude name="Generic.WhiteSpace.DisallowTabIndent.TabsUsedHeredocCloser"/>
		<exclude name="PSR1.Classes.ClassDeclaration.MissingNamespace"/>
		<exclude name="PSR1.Classes.ClassDeclaration.MultipleClasses"/>
		<exclude name="PSR1.Files.SideEffects.FoundWithSymbols"/>
		<exclude name="PSR1.Methods.CamelCapsMethodName.NotCamelCaps"/>
		<exclude name="PSR12.Classes.OpeningBraceSpace.Found"/><!-- Consider using PSR12 defaults instead -->
		<exclude name="PSR12.ControlStructures.ControlStructureSpacing.CloseParenthesisLine"/>
		<exclude name="PSR12.ControlStructures.ControlStructureSpacing.FirstExpressionLine"/><!-- Consider using PSR12 defaults instead -->
		<exclude name="PSR12.Files.FileHeader.IncorrectOrder"/><!-- Consider using PSR12 defaults instead -->
		<exclude name="PSR12.Files.FileHeader.SpacingAfterBlock"/><!-- Consider using PSR12 defaults instead -->
		<exclude name="PSR12.Traits.UseDeclaration.MultipleImport"/>
		<exclude name="PSR2.Classes.ClassDeclaration.CloseBraceAfterBody"/><!-- Consider using PSR12 defaults instead -->
		<exclude name="PSR2.Classes.ClassDeclaration.OpenBraceNewLine"/><!-- Consider using PSR12 defaults instead -->
		<exclude name="PSR2.ControlStructures.SwitchDeclaration.BodyOnNextLineCASE"/>
		<exclude name="PSR2.ControlStructures.SwitchDeclaration.BreakNotNewLine"/>
		<exclude name="PSR2.Functions.FunctionCallSignature.ContentAfterOpenBracket"/>
		<exclude name="PSR2.Methods.FunctionCallSignature.CloseBracketLine"/>
		<exclude name="PSR2.Methods.FunctionCallSignature.ContentAfterOpenBracket"/>
		<exclude name="PSR2.Methods.FunctionCallSignature.Indent"/>
		<exclude name="PSR2.Methods.FunctionCallSignature.MultipleArguments"/>
		<exclude name="PSR2.Methods.MethodDeclaration.Underscore"/>
		<exclude name="Squiz.Classes.ValidClassName.NotCamelCaps"/>
		<exclude name="Squiz.Functions.MultiLineFunctionDeclaration.BraceOnSameLine"/>
		<exclude name="Squiz.Functions.MultiLineFunctionDeclaration.CloseBracketLine"/>
		<exclude name="Squiz.Functions.MultiLineFunctionDeclaration.ContentAfterBrace"/>
		<exclude name="Squiz.Functions.MultiLineFunctionDeclaration.FirstParamSpacing"/>
		<exclude name="Squiz.Functions.MultiLineFunctionDeclaration.Indent"/>
		<exclude name="Squiz.Functions.MultiLineFunctionDeclaration.OneParamPerLine"/>
		<exclude name="Squiz.WhiteSpace.ScopeClosingBrace.ContentBefore"/>
	</rule>
	<rule ref="Generic.Classes.DuplicateClassName"/>
	<rule ref="Generic.CodeAnalysis.EmptyStatement"/>
	<rule ref="Generic.CodeAnalysis.UnconditionalIfStatement"/>
	<rule ref="Generic.CodeAnalysis.UnnecessaryFinalModifier"/>
	<rule ref="Generic.CodeAnalysis.UselessOverridingMethod"/>
	<rule ref="Generic.Files.LineLength">
		<properties>
			<property name="lineLimit" value="165"/>
			<property name="absoluteLineLimit" value="190"/>
		</properties>
		<exclude-pattern>/app/i18n/*\.php$</exclude-pattern>
		<exclude-pattern>*\.phtml$</exclude-pattern>
	</rule>
	<rule ref="Generic.Functions.OpeningFunctionBraceKernighanRitchie"/><!-- Consider using PSR12 defaults instead -->
	<rule ref="Generic.PHP.DeprecatedFunctions"/>
	<rule ref="Generic.Strings.UnnecessaryStringConcat">
		<properties>
			<property name="allowMultiline" value="true"/>
		</properties>
	</rule>
	<rule ref="Generic.WhiteSpace.DisallowSpaceIndent"/>
	<rule ref="Generic.WhiteSpace.ScopeIndent.Incorrect">
		<exclude-pattern>*\.phtml$</exclude-pattern>
		<exclude-pattern>/app/install.php</exclude-pattern>
	</rule>
	<rule ref="Generic.WhiteSpace.ScopeIndent.IncorrectExact">
		<exclude-pattern>*\.phtml$</exclude-pattern>
		<exclude-pattern>/app/install.php</exclude-pattern>
	</rule>
	<rule ref="Internal.NoCodeFound">
		<exclude-pattern>*\.phtml$</exclude-pattern>
	</rule>
	<!-- <rule ref="Squiz.Commenting.ClassComment.Missing"/> --><!-- Consider adding -->

	<rule ref="Squiz.ControlStructures.ControlSignature">
		<include-pattern>*\.phtml$</include-pattern>
		<properties>
			<property name="requiredSpacesBeforeColon" value="0" />
		</properties>
	</rule>
	<rule ref="Squiz.ControlStructures.ControlSignature">
		<include-pattern>*\.php$</include-pattern>
	</rule>

	<rule ref="Squiz.ControlStructures.ControlSignature.NewlineAfterOpenBrace">
		<exclude-pattern>*\.phtml$</exclude-pattern>
	</rule>
	<rule ref="Squiz.WhiteSpace.OperatorSpacing">
		<properties>
			<property name="ignoreNewlines" value="true"/>
		</properties>
	</rule>
	<rule ref="Squiz.WhiteSpace.ScopeClosingBrace.Indent">
		<exclude-pattern>*\.phtml$</exclude-pattern>
	</rule>
	<rule ref="Squiz.WhiteSpace.SemicolonSpacing"/>
</ruleset>
