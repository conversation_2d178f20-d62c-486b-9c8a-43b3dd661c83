parameters:
	phpVersion:
		min: 80100	# PHP 8.1
		max: 80499	# PHP 8.4
	level: 10	# https://phpstan.org/user-guide/rule-levels
	fileExtensions:
		- php
		- phtml
	paths:
		- .
	excludePaths:
		analyse:
			- lib/marienfressinaud/*
			- lib/phpgt/*
			- lib/phpmailer/*
			- lib/simplepie/*
		analyseAndScan:
			- .git/*?
			- extensions/node_modules?
			- extensions/symbolic?
			- extensions/vendor?
			- node_modules/*?
			- vendor/*
	bootstrapFiles:
		- cli/_cli.php
		- lib/favicons.php
	dynamicConstantNames:
		- CACHE_PATH
		- COPY_LOG_TO_SYSLOG
		- COPY_SYSLOG_TO_STDERR
		- DATA_PATH
		- MAX_LOG_SIZE
		- PUBLIC_RELATIVE
		- SIMPLEPIE_SYSLOG_ENABLED
		- STDERR
		- STDOUT
		- TMP_PATH
		- USERS_PATH
	checkBenevolentUnionTypes: true
	checkImplicitMixed: true
	checkMissingOverrideMethodAttribute: true
	checkTooWideReturnTypesInProtectedAndPublicMethods: true
	reportAnyTypeWideningInVarTag: true
	treatPhpDocTypesAsCertain: false
	strictRules:
		disallowedEmpty: false
		disallowedLooseComparison: false
		disallowedShortTernary: false
		strictArrayFilter: false	# TODO pass
	exceptions:
		check:
			missingCheckedExceptionInThrows: false	# TODO pass
			tooWideThrowType: true
		implicitThrows: false
		checkedExceptionClasses:
			- 'Minz_Exception'
	ignoreErrors:
		- '#Only booleans are allowed in (a negated boolean|a ternary operator condition|an elseif condition|an if condition|&&|\|\|), (bool|false|int(<[0-9, max]+>)?|true|null|\|)+ given.*#'
includes:
	- vendor/phpstan/phpstan-phpunit/extension.neon
	- vendor/phpstan/phpstan-phpunit/rules.neon
	- vendor/phpstan/phpstan-strict-rules/rules.neon
