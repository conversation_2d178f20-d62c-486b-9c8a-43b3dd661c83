<?php
	declare(strict_types=1);
	/** @var UserCSSExtension $this */
?>
<form action="<?= _url('extension', 'configure', 'e', urlencode($this->getName())); ?>" method="post">
	<input type="hidden" name="_csrf" value="<?= FreshRSS_Auth::csrfToken() ?>" />
	<div class="form-group">
		<label class="group-name" for="css-rules"><?= _t('ext.user_css.write_css') ?></label>
		<div class="group-controls">
			<textarea name="css-rules" id="css-rules"><?= $this->css_rules ?></textarea>
		</div>
	</div>

	<div class="form-group form-actions">
		<div class="group-controls">
			<button type="submit" class="btn btn-important"><?= _t('gen.action.submit') ?></button>
			<button type="reset" class="btn"><?= _t('gen.action.cancel') ?></button>
		</div>
	</div>
</form>
