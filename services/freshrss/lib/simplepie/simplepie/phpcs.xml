<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="Simple<PERSON>ie">
	<arg name="extensions" value="php"/>
	<exclude-pattern>./.git/</exclude-pattern>
	<exclude-pattern>./compatibility_test/</exclude-pattern>
	<exclude-pattern>./vendor/</exclude-pattern>
	<rule ref="PSR12">
		<exclude name="PSR1.Files.SideEffects.FoundWithSymbols"/>
		<exclude name="PSR1.Methods.CamelCapsMethodName.NotCamelCaps"/>
		<exclude name="PSR12.ControlStructures.ControlStructureSpacing.CloseParenthesisLine"/>
		<exclude name="PSR12.ControlStructures.ControlStructureSpacing.FirstExpressionLine"/>
		<exclude name="Squiz.ControlStructures.ControlSignature.SpaceAfterCloseBrace"/>
	</rule>
	<rule ref="Generic.Files.LineLength">
		<properties>
			<property name="lineLimit" value="165"/>
			<property name="absoluteLineLimit" value="400"/>
		</properties>
	</rule>
	<rule ref="Generic.WhiteSpace.DisallowTabIndent.TabsUsed">
		<exclude-pattern>./demo/</exclude-pattern>
	</rule>
	<rule ref="PSR1.Classes.ClassDeclaration.MissingNamespace">
		<exclude-pattern>./library/</exclude-pattern>
	</rule>
	<rule ref="Squiz.Classes.ValidClassName.NotCamelCaps">
		<exclude-pattern>./library/</exclude-pattern>
	</rule>
</ruleset>
