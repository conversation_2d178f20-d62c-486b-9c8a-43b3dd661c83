{"name": "HawaiiHub自动化工作流配置", "version": "1.0.0", "description": "基于本地LLM的HawaiiHub.net自动化工作流配置", "workflows": {"content_scraping": {"name": "智能内容爬取工作流", "description": "使用本地LLM驱动的智能爬虫，自动抓取夏威夷本地新闻和华人社区内容", "trigger": {"type": "cron", "schedule": "0 */2 * * *", "description": "每2小时执行一次"}, "nodes": [{"id": "trigger", "type": "<PERSON><PERSON>", "name": "定时触发器", "parameters": {"rule": {"interval": [{"field": "hours", "value": 2}]}}}, {"id": "llm_analyzer", "type": "HTTP Request", "name": "本地LLM内容分析", "parameters": {"url": "http://localhost:11434/api/generate", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": {"model": "llama3.2:3b", "prompt": "分析以下网站内容，提取夏威夷本地新闻和华人社区相关信息：{{ $json.content }}", "stream": false}}}, {"id": "web_scraper", "type": "HTTP Request", "name": "网页内容抓取", "parameters": {"url": "{{ $json.target_url }}", "method": "GET", "headers": {"User-Agent": "HawaiiHub-Bot/1.0"}}}, {"id": "content_classifier", "type": "Function", "name": "内容智能分类", "parameters": {"functionCode": "// 调用本地AI分类系统\nconst axios = require('axios');\n\nconst classifyContent = async (title, content) => {\n  try {\n    const response = await axios.post('http://localhost:8000/classify', {\n      title: title,\n      content: content\n    });\n    return response.data;\n  } catch (error) {\n    console.error('分类失败:', error);\n    return { category: 'unknown', confidence: 0 };\n  }\n};\n\nconst result = await classifyContent($json.title, $json.content);\nreturn [{ json: { ...result, original: $json } }];"}}, {"id": "duplicate_detector", "type": "Function", "name": "重复内容检测", "parameters": {"functionCode": "// 调用重复检测系统\nconst axios = require('axios');\n\nconst detectDuplicate = async (content) => {\n  try {\n    const response = await axios.post('http://localhost:8001/detect-duplicate', {\n      articles: [content]\n    });\n    return response.data;\n  } catch (error) {\n    console.error('重复检测失败:', error);\n    return { is_duplicate: false };\n  }\n};\n\nconst result = await detectDuplicate($json);\nreturn [{ json: { ...result, original: $json } }];"}}, {"id": "freshrss_integration", "type": "HTTP Request", "name": "FreshRSS集成", "parameters": {"url": "http://localhost:8080/api/greader.php/reader/api/0/stream/contents", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "GoogleLogin auth={{ $json.auth_token }}"}, "body": {"title": "{{ $json.title }}", "content": "{{ $json.content }}", "category": "{{ $json.category }}", "url": "{{ $json.url }}"}}}], "connections": [{"from": "trigger", "to": "web_scraper"}, {"from": "web_scraper", "to": "llm_analyzer"}, {"from": "llm_analyzer", "to": "content_classifier"}, {"from": "content_classifier", "to": "duplicate_detector"}, {"from": "duplicate_detector", "to": "freshrss_integration"}]}, "local_project_deployment": {"name": "本地项目自动部署", "description": "监控代码变更并自动部署本地项目", "trigger": {"type": "webhook", "path": "/deploy-ha<PERSON><PERSON><PERSON>", "description": "通过Webhook触发部署"}, "nodes": [{"id": "webhook_trigger", "type": "Webhook", "name": "部署触发器", "parameters": {"path": "deploy-<PERSON><PERSON><PERSON><PERSON>", "httpMethod": "POST"}}, {"id": "git_pull", "type": "Execute Command", "name": "拉取最新代码", "parameters": {"command": "cd /Users/<USER>/Documents/华人平台/hawaiihub.net && git pull origin main"}}, {"id": "dependency_install", "type": "Execute Command", "name": "安装依赖", "parameters": {"command": "cd /Users/<USER>/Documents/华人平台/hawaiihub.net && npm install && pip install -r requirements.txt"}}, {"id": "ai_module_test", "type": "Execute Command", "name": "AI模块测试", "parameters": {"command": "cd /Users/<USER>/Documents/华人平台/hawaiihub.net/services/ai-testing && python3 ai_module_tester.py"}}, {"id": "service_restart", "type": "Execute Command", "name": "重启服务", "parameters": {"command": "cd /Users/<USER>/Documents/华人平台/hawaiihub.net && ./scripts/restart_services.sh"}}, {"id": "health_check", "type": "HTTP Request", "name": "健康检查", "parameters": {"url": "http://localhost:8080/api/", "method": "GET", "timeout": 10000}}, {"id": "notification", "type": "Function", "name": "部署通知", "parameters": {"functionCode": "const deployStatus = $json.statusCode === 200 ? '成功' : '失败';\nconst message = `HawaiiHub.net 部署${deployStatus}\\n时间: ${new Date().toLocaleString()}\\n状态: ${$json.statusCode}`;\nconsole.log(message);\nreturn [{ json: { message, status: deployStatus } }];"}}], "connections": [{"from": "webhook_trigger", "to": "git_pull"}, {"from": "git_pull", "to": "dependency_install"}, {"from": "dependency_install", "to": "ai_module_test"}, {"from": "ai_module_test", "to": "service_restart"}, {"from": "service_restart", "to": "health_check"}, {"from": "health_check", "to": "notification"}]}, "intelligent_monitoring": {"name": "智能监控工作流", "description": "使用本地LLM分析系统状态并自动处理异常", "trigger": {"type": "cron", "schedule": "*/5 * * * *", "description": "每5分钟检查一次"}, "nodes": [{"id": "monitor_trigger", "type": "<PERSON><PERSON>", "name": "监控触发器", "parameters": {"rule": {"interval": [{"field": "minutes", "value": 5}]}}}, {"id": "system_status", "type": "Execute Command", "name": "系统状态检查", "parameters": {"command": "cd /Users/<USER>/Documents/华人平台/hawaiihub.net/services/ai-testing && python3 system_monitor.py --check-once"}}, {"id": "log_analysis", "type": "Function", "name": "日志分析", "parameters": {"functionCode": "const fs = require('fs');\nconst path = require('path');\n\n// 读取最新日志\nconst logDir = '/Users/<USER>/Documents/华人平台/hawaiihub.net/data/logs';\nconst logFiles = fs.readdirSync(logDir).filter(f => f.endsWith('.log'));\nconst latestLog = logFiles.sort().pop();\n\nif (latestLog) {\n  const logContent = fs.readFileSync(path.join(logDir, latestLog), 'utf8');\n  const recentLines = logContent.split('\\n').slice(-50).join('\\n');\n  return [{ json: { logs: recentLines, file: latestLog } }];\n} else {\n  return [{ json: { logs: '', file: 'none' } }];\n}"}}, {"id": "llm_log_analyzer", "type": "HTTP Request", "name": "LLM日志分析", "parameters": {"url": "http://localhost:11434/api/generate", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": {"model": "llama3.2:3b", "prompt": "分析以下系统日志，识别潜在问题和异常：\\n{{ $json.logs }}\\n\\n请提供：1. 问题严重程度 2. 具体问题描述 3. 建议解决方案", "stream": false}}}, {"id": "auto_fix", "type": "Function", "name": "自动修复", "parameters": {"functionCode": "const analysis = JSON.parse($json.response);\nconst severity = analysis.severity || 'low';\n\nif (severity === 'high') {\n  // 执行自动修复脚本\n  const { exec } = require('child_process');\n  exec('cd /Users/<USER>/Documents/华人平台/hawaiihub.net/services/ai-testing && python3 auto_optimization.py', (error, stdout, stderr) => {\n    if (error) {\n      console.error('自动修复失败:', error);\n    } else {\n      console.log('自动修复完成:', stdout);\n    }\n  });\n}\n\nreturn [{ json: { ...analysis, auto_fix_triggered: severity === 'high' } }];"}}], "connections": [{"from": "monitor_trigger", "to": "system_status"}, {"from": "system_status", "to": "log_analysis"}, {"from": "log_analysis", "to": "llm_log_analyzer"}, {"from": "llm_log_analyzer", "to": "auto_fix"}]}, "content_quality_enhancement": {"name": "内容质量增强工作流", "description": "使用本地LLM提升内容质量和用户体验", "trigger": {"type": "webhook", "path": "/enhance-content", "description": "内容增强触发器"}, "nodes": [{"id": "content_trigger", "type": "Webhook", "name": "内容触发器", "parameters": {"path": "enhance-content", "httpMethod": "POST"}}, {"id": "content_translation", "type": "HTTP Request", "name": "智能翻译", "parameters": {"url": "http://localhost:11434/api/generate", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": {"model": "llama3.2:3b", "prompt": "将以下内容翻译为中文，保持原意和专业性：\\n{{ $json.content }}", "stream": false}}}, {"id": "content_summary", "type": "HTTP Request", "name": "内容摘要", "parameters": {"url": "http://localhost:11434/api/generate", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": {"model": "llama3.2:3b", "prompt": "为以下内容生成简洁的中文摘要（100字以内）：\\n{{ $json.content }}", "stream": false}}}, {"id": "keyword_extraction", "type": "HTTP Request", "name": "关键词提取", "parameters": {"url": "http://localhost:11434/api/generate", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": {"model": "llama3.2:3b", "prompt": "从以下内容中提取5-10个关键词，用逗号分隔：\\n{{ $json.content }}", "stream": false}}}, {"id": "content_enhancement", "type": "Function", "name": "内容增强整合", "parameters": {"functionCode": "const enhanced = {\n  original: $('content_trigger').first().json,\n  translation: JSON.parse($('content_translation').first().json.response),\n  summary: JSON.parse($('content_summary').first().json.response),\n  keywords: JSON.parse($('keyword_extraction').first().json.response).split(',').map(k => k.trim()),\n  enhanced_at: new Date().toISOString()\n};\n\nreturn [{ json: enhanced }];"}}, {"id": "save_enhanced", "type": "HTTP Request", "name": "保存增强内容", "parameters": {"url": "http://localhost:8080/api/enhanced-content", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": "{{ $json }}"}}], "connections": [{"from": "content_trigger", "to": "content_translation"}, {"from": "content_trigger", "to": "content_summary"}, {"from": "content_trigger", "to": "keyword_extraction"}, {"from": "content_translation", "to": "content_enhancement"}, {"from": "content_summary", "to": "content_enhancement"}, {"from": "keyword_extraction", "to": "content_enhancement"}, {"from": "content_enhancement", "to": "save_enhanced"}]}}, "local_llm_config": {"ollama_endpoint": "http://localhost:11434", "models": [{"name": "llama3.2:3b", "purpose": "通用文本处理和分析", "context_length": 4096}, {"name": "qwen2.5:7b", "purpose": "中文内容处理优化", "context_length": 8192}, {"name": "codellama:7b", "purpose": "代码分析和生成", "context_length": 4096}], "api_endpoints": {"classification": "http://localhost:8000/classify", "duplicate_detection": "http://localhost:8001/detect-duplicate", "content_enhancement": "http://localhost:8002/enhance"}}, "monitoring_config": {"health_check_interval": 300, "log_retention_days": 7, "alert_thresholds": {"cpu_usage": 80, "memory_usage": 85, "error_rate": 5}, "notification_channels": [{"type": "console", "enabled": true}, {"type": "file", "enabled": true, "path": "/tmp/n8n_notifications.log"}]}, "deployment_config": {"project_path": "/Users/<USER>/Documents/华人平台/hawaiihub.net", "services": ["rsshub", "freshrss", "ai-classifier", "ai-testing"], "health_check_urls": ["http://localhost:1200", "http://localhost:8080", "http://localhost:8000", "http://localhost:8001"]}}