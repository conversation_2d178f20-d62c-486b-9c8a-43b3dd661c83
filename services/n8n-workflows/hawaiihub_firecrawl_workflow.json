{"name": "HawaiiHub-Firecrawl-Integration", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "value": 2}]}}, "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"url": "http://localhost:8000/firecrawl-scrape", "options": {"headers": {"Content-Type": "application/json"}}, "bodyParametersUi": {"parameter": [{"name": "urls", "value": "={{JSON.stringify([\n  'https://www.hawaiinewsnow.com',\n  'https://www.staradvertiser.com',\n  'https://www.hawaiitribune-herald.com'\n])}}"}, {"name": "api_key", "value": "fc-0a2c801f433d4718bcd8189f2742edf4"}]}}, "name": "Firecrawl Scrape", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [460, 300]}, {"parameters": {"url": "http://localhost:8000/classify", "options": {"headers": {"Content-Type": "application/json"}}, "bodyParametersUi": {"parameter": [{"name": "title", "value": "={{$json.title}}"}, {"name": "content", "value": "={{$json.content}}"}]}}, "name": "AI Classification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [680, 300]}, {"parameters": {"url": "http://localhost:8000/detect-duplicate", "options": {"headers": {"Content-Type": "application/json"}}, "bodyParametersUi": {"parameter": [{"name": "articles", "value": "={{JSON.stringify([$json])}}"}]}}, "name": "Duplicate Detection", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [900, 300]}], "connections": {"Schedule Trigger": {"main": [[{"node": "Firecrawl Scrape", "type": "main", "index": 0}]]}, "Firecrawl Scrape": {"main": [[{"node": "AI Classification", "type": "main", "index": 0}]]}, "AI Classification": {"main": [[{"node": "Duplicate Detection", "type": "main", "index": 0}]]}}}