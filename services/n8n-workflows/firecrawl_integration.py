#!/usr/bin/env python3
"""
Firecrawl API集成测试和n8n工作流配置
HawaiiHub.net内容采集系统
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

class FirecrawlIntegration:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.firecrawl.dev/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
    def test_connection(self) -> Dict[str, Any]:
        """测试API连接"""
        try:
            # 使用一个简单的scrape请求来测试连接
            test_payload = {
                "url": "https://example.com",
                "formats": ["markdown"]
            }
            response = requests.post(
                f"{self.base_url}/scrape",
                headers=self.headers,
                json=test_payload,
                timeout=30
            )
            return {
                "success": response.status_code in [200, 400, 402],  # 402表示credits不足但连接正常
                "status_code": response.status_code,
                "message": "API连接正常" if response.status_code in [200, 400, 402] else f"API连接失败: {response.text}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "API连接测试失败"
            }
    
    def scrape_url(self, url: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """爬取单个URL"""
        if options is None:
            options = {}
            
        payload = {
            "url": url,
            "formats": ["markdown", "json"],
            "onlyMainContent": True,
            "includeTags": ["title", "meta", "article", "main"],
            **options
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/scrape",
                headers=self.headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "success": True,
                    "data": data,
                    "credits_used": 1,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                error_detail = ""
                try:
                    error_data = response.json()
                    error_detail = error_data.get("error", response.text)
                except:
                    error_detail = response.text

                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}",
                    "message": error_detail,
                    "credits_used": 0
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "credits_used": 0
            }
    
    def crawl_website(self, url: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """爬取整个网站"""
        if options is None:
            options = {}
            
        payload = {
            "url": url,
            "formats": ["markdown", "json"],
            "onlyMainContent": True,
            "maxDepth": 2,
            "limit": 50,
            **options
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/crawl",
                headers=self.headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "success": True,
                    "job_id": data.get("id"),
                    "status": data.get("status"),
                    "message": "爬取任务已启动"
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}",
                    "message": response.text
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_crawl_status(self, job_id: str) -> Dict[str, Any]:
        """获取爬取任务状态"""
        try:
            response = requests.get(
                f"{self.base_url}/crawl/{job_id}",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "data": response.json()
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}",
                    "message": response.text
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def search_web(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """网络搜索"""
        payload = {
            "query": query,
            "limit": limit,
            "formats": ["markdown", "json"]
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/search",
                headers=self.headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "success": True,
                    "data": data,
                    "credits_used": limit,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}",
                    "message": response.text,
                    "credits_used": 0
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "credits_used": 0
            }

def test_hawaii_news_sources():
    """测试夏威夷新闻源"""
    api_key = "fc-0a2c801f433d4718bcd8189f2742edf4"
    firecrawl = FirecrawlIntegration(api_key)
    
    # 测试连接
    print("🔍 测试Firecrawl API连接...")
    connection_test = firecrawl.test_connection()
    print(f"连接状态: {connection_test}")
    
    if not connection_test["success"]:
        print("❌ API连接失败，请检查API Key")
        return
    
    # 测试夏威夷新闻源 - 先测试简单URL
    hawaii_sources = [
        "https://example.com",  # 简单测试
        "https://www.hawaiinewsnow.com",
        "https://www.staradvertiser.com"
    ]
    
    results = []
    total_credits = 0
    
    for url in hawaii_sources:
        print(f"\n🌺 测试爬取: {url}")
        result = firecrawl.scrape_url(url, {
            "waitFor": 3000,
            "screenshot": False
        })
        
        if result["success"]:
            data = result["data"]
            print(f"✅ 成功爬取")
            print(f"   标题: {data.get('metadata', {}).get('title', 'N/A')}")
            print(f"   内容长度: {len(data.get('markdown', ''))}")
            print(f"   Credits消耗: {result['credits_used']}")
            total_credits += result["credits_used"]
        else:
            print(f"❌ 爬取失败: {result.get('error', 'Unknown error')}")
        
        results.append({
            "url": url,
            "result": result
        })
        
        # 避免频率限制
        time.sleep(2)
    
    print(f"\n📊 测试总结:")
    print(f"   成功率: {sum(1 for r in results if r['result']['success'])}/{len(results)}")
    print(f"   总Credits消耗: {total_credits}")
    print(f"   预估月成本: ${total_credits * 125 * 0.0053:.2f}")  # 125页面/天
    
    return results

def create_n8n_workflow_config():
    """创建n8n工作流配置"""
    workflow_config = {
        "name": "HawaiiHub-Firecrawl-Integration",
        "nodes": [
            {
                "parameters": {
                    "rule": {
                        "interval": [{"field": "hours", "value": 2}]
                    }
                },
                "name": "Schedule Trigger",
                "type": "n8n-nodes-base.scheduleTrigger",
                "typeVersion": 1,
                "position": [240, 300]
            },
            {
                "parameters": {
                    "url": "http://localhost:8000/firecrawl-scrape",
                    "options": {
                        "headers": {
                            "Content-Type": "application/json"
                        }
                    },
                    "bodyParametersUi": {
                        "parameter": [
                            {
                                "name": "urls",
                                "value": "={{JSON.stringify([\n  'https://www.hawaiinewsnow.com',\n  'https://www.staradvertiser.com',\n  'https://www.hawaiitribune-herald.com'\n])}}"
                            },
                            {
                                "name": "api_key",
                                "value": "fc-0a2c801f433d4718bcd8189f2742edf4"
                            }
                        ]
                    }
                },
                "name": "Firecrawl Scrape",
                "type": "n8n-nodes-base.httpRequest",
                "typeVersion": 4,
                "position": [460, 300]
            },
            {
                "parameters": {
                    "url": "http://localhost:8000/classify",
                    "options": {
                        "headers": {
                            "Content-Type": "application/json"
                        }
                    },
                    "bodyParametersUi": {
                        "parameter": [
                            {
                                "name": "title",
                                "value": "={{$json.title}}"
                            },
                            {
                                "name": "content",
                                "value": "={{$json.content}}"
                            }
                        ]
                    }
                },
                "name": "AI Classification",
                "type": "n8n-nodes-base.httpRequest",
                "typeVersion": 4,
                "position": [680, 300]
            },
            {
                "parameters": {
                    "url": "http://localhost:8000/detect-duplicate",
                    "options": {
                        "headers": {
                            "Content-Type": "application/json"
                        }
                    },
                    "bodyParametersUi": {
                        "parameter": [
                            {
                                "name": "articles",
                                "value": "={{JSON.stringify([$json])}}"
                            }
                        ]
                    }
                },
                "name": "Duplicate Detection",
                "type": "n8n-nodes-base.httpRequest",
                "typeVersion": 4,
                "position": [900, 300]
            }
        ],
        "connections": {
            "Schedule Trigger": {
                "main": [
                    [
                        {
                            "node": "Firecrawl Scrape",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Firecrawl Scrape": {
                "main": [
                    [
                        {
                            "node": "AI Classification",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "AI Classification": {
                "main": [
                    [
                        {
                            "node": "Duplicate Detection",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            }
        }
    }
    
    return workflow_config

if __name__ == "__main__":
    print("🔥 Firecrawl集成测试开始...")
    test_results = test_hawaii_news_sources()
    
    print("\n📋 生成n8n工作流配置...")
    workflow_config = create_n8n_workflow_config()
    
    with open("hawaiihub_firecrawl_workflow.json", "w", encoding="utf-8") as f:
        json.dump(workflow_config, f, indent=2, ensure_ascii=False)
    
    print("✅ 集成测试完成！")
    print("📁 工作流配置已保存到: hawaiihub_firecrawl_workflow.json")
