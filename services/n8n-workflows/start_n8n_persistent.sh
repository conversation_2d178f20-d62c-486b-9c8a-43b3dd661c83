#!/bin/bash

# HawaiiHub n8n持久化启动脚本
# 包含API Key环境变量配置

echo "🚀 启动HawaiiHub n8n工作流自动化平台..."

# n8n API Key (持久化配置)
N8N_API_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMDJjMjM0MC00MTRjLTQyNDUtOTQwYS1lMDE2Mjk4ZDg2ZTgiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUzNzMzNzI4fQ.Z8Vi1OBeFkFdWOw_kaE7xiEcdhEHeLQIuphM6Y9TyUg"

# 停止现有容器（如果存在）
echo "🛑 停止现有n8n容器..."
container stop n8n-hawaiihub-persistent 2>/dev/null || true
container rm n8n-hawaiihub-persistent 2>/dev/null || true

# 确保网络存在
echo "🌐 检查网络配置..."
container network create hawaiihub-network 2>/dev/null || true

# 确保数据目录存在
echo "📁 创建数据目录..."
mkdir -p /tmp/n8n_data

# 启动n8n容器
echo "🔥 启动n8n容器..."
container run -d \
  --name n8n-hawaiihub-persistent \
  --network hawaiihub-network \
  -e N8N_HOST=0.0.0.0 \
  -e N8N_PORT=5678 \
  -e N8N_PROTOCOL=http \
  -e WEBHOOK_URL=http://localhost:5678 \
  -e N8N_RUNNERS_ENABLED=true \
  -e N8N_API_KEY="$N8N_API_KEY" \
  -e N8N_SECURE_COOKIE=false \
  -e N8N_METRICS=true \
  --mount type=bind,source=/tmp/n8n_data,target=/home/<USER>/.n8n \
  n8nio/n8n

# 等待容器启动
echo "⏳ 等待n8n启动..."
sleep 10

# 检查容器状态
echo "📊 检查容器状态..."
container list | grep n8n-hawaiihub-persistent

# 获取容器IP
CONTAINER_IP=$(container list | grep n8n-hawaiihub-persistent | awk '{print $6}')

echo ""
echo "✅ n8n启动完成！"
echo "🌐 访问地址: http://$CONTAINER_IP:5678"
echo "🔑 API Key已配置: $N8N_API_KEY"
echo "📚 API文档: http://$CONTAINER_IP:5678/api/v1/docs"
echo ""
echo "🎯 下一步："
echo "1. 访问 http://$CONTAINER_IP:5678 配置工作流"
echo "2. 使用API Key进行程序化访问"
echo "3. 集成Firecrawl和本地LLM服务"
echo ""
