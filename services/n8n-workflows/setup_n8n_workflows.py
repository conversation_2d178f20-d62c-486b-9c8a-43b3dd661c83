#!/usr/bin/env python3
"""
n8n工作流自动配置脚本
配置基于本地LLM的HawaiiHub.net自动化工作流
"""

import json
import requests
import time
import sys
from pathlib import Path
from typing import Dict, List, Any

class N8NWorkflowManager:
    """n8n工作流管理器"""
    
    def __init__(self, n8n_url: str = "http://192.168.65.3:5678"):
        self.n8n_url = n8n_url
        self.api_url = f"{n8n_url}/rest"
        self.session = requests.Session()
        self.workflows_dir = Path(__file__).parent
        
    def check_n8n_health(self) -> bool:
        """检查n8n服务健康状态"""
        try:
            response = self.session.get(f"{self.n8n_url}/healthz", timeout=10)
            return response.status_code == 200
        except Exception as e:
            print(f"❌ n8n健康检查失败: {str(e)}")
            return False
    
    def wait_for_n8n(self, max_wait: int = 60) -> bool:
        """等待n8n服务启动"""
        print("⏳ 等待n8n服务启动...")
        
        for i in range(max_wait):
            if self.check_n8n_health():
                print("✅ n8n服务已就绪")
                return True
            
            print(f"⏳ 等待中... ({i+1}/{max_wait})")
            time.sleep(1)
        
        print("❌ n8n服务启动超时")
        return False
    
    def load_workflow_config(self) -> Dict[str, Any]:
        """加载工作流配置"""
        config_file = self.workflows_dir / "hawaiihub-automation-config.json"
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {str(e)}")
            return {}
    
    def create_n8n_workflow(self, workflow_name: str, workflow_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建n8n工作流"""
        
        # 转换为n8n格式
        n8n_workflow = {
            "name": workflow_config["name"],
            "nodes": [],
            "connections": {},
            "active": True,
            "settings": {
                "executionOrder": "v1"
            },
            "staticData": {},
            "meta": {
                "templateCredsSetupCompleted": True
            },
            "pinData": {},
            "versionId": "1"
        }
        
        # 转换节点
        for i, node_config in enumerate(workflow_config["nodes"]):
            node = {
                "parameters": node_config.get("parameters", {}),
                "id": node_config["id"],
                "name": node_config["name"],
                "type": node_config["type"],
                "typeVersion": 1,
                "position": [
                    200 + (i % 3) * 300,  # x坐标
                    200 + (i // 3) * 200  # y坐标
                ]
            }
            n8n_workflow["nodes"].append(node)
        
        # 转换连接
        connections = {}
        for connection in workflow_config.get("connections", []):
            from_node = connection["from"]
            to_node = connection["to"]
            
            if from_node not in connections:
                connections[from_node] = {"main": [[]]}
            
            connections[from_node]["main"][0].append({
                "node": to_node,
                "type": "main",
                "index": 0
            })
        
        n8n_workflow["connections"] = connections
        
        return n8n_workflow
    
    def upload_workflow(self, workflow: Dict[str, Any]) -> bool:
        """上传工作流到n8n"""
        try:
            response = self.session.post(
                f"{self.api_url}/workflows",
                json=workflow,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 201:
                workflow_data = response.json()
                print(f"✅ 工作流 '{workflow['name']}' 创建成功 (ID: {workflow_data.get('id')})")
                return True
            else:
                print(f"❌ 工作流 '{workflow['name']}' 创建失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 上传工作流失败: {str(e)}")
            return False
    
    def setup_credentials(self) -> bool:
        """设置凭据"""
        credentials = [
            {
                "name": "Ollama本地LLM",
                "type": "httpBasicAuth",
                "data": {
                    "user": "",
                    "password": ""
                }
            },
            {
                "name": "HawaiiHub本地API",
                "type": "httpBasicAuth", 
                "data": {
                    "user": "hawaiihub",
                    "password": "local-api-2025"
                }
            }
        ]
        
        success_count = 0
        for cred in credentials:
            try:
                response = self.session.post(
                    f"{self.api_url}/credentials",
                    json=cred,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 201:
                    print(f"✅ 凭据 '{cred['name']}' 创建成功")
                    success_count += 1
                else:
                    print(f"⚠️ 凭据 '{cred['name']}' 创建失败: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 创建凭据失败: {str(e)}")
        
        return success_count > 0
    
    def create_local_llm_api_endpoints(self) -> bool:
        """创建本地LLM API端点"""
        print("🔧 创建本地LLM API端点...")
        
        # 创建简单的API服务器脚本
        api_script = self.workflows_dir.parent / "ai-classifier" / "llm_api_server.py"
        
        api_content = '''#!/usr/bin/env python3
"""
本地LLM API服务器
为n8n工作流提供LLM接口
"""

from flask import Flask, request, jsonify
import requests
import json
import sys
from pathlib import Path

# 添加AI模块路径
sys.path.append(str(Path(__file__).parent))
from content_classifier import HawaiiContentClassifier
from duplicate_detector import DuplicateDetectionIntegration

app = Flask(__name__)

# 初始化AI模块
classifier = HawaiiContentClassifier()
duplicate_integration = DuplicateDetectionIntegration()

@app.route('/classify', methods=['POST'])
def classify_content():
    """内容分类API"""
    try:
        data = request.json
        title = data.get('title', '')
        content = data.get('content', '')
        
        result = classifier.classify_content(title, content)
        
        if result:
            return jsonify({
                'category': result.category,
                'subcategory': result.subcategory,
                'confidence': result.confidence,
                'success': True
            })
        else:
            return jsonify({
                'category': 'unknown',
                'subcategory': 'unknown',
                'confidence': 0.0,
                'success': False
            })
            
    except Exception as e:
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/detect-duplicate', methods=['POST'])
def detect_duplicate():
    """重复检测API"""
    try:
        data = request.json
        articles = data.get('articles', [])
        
        duplicate_groups = duplicate_integration.detector.detect_duplicates(articles)
        
        return jsonify({
            'duplicate_groups': len(duplicate_groups),
            'total_duplicates': sum(len(group.articles) for group in duplicate_groups),
            'is_duplicate': len(duplicate_groups) > 0,
            'success': True
        })
        
    except Exception as e:
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/ollama-proxy', methods=['POST'])
def ollama_proxy():
    """Ollama代理API"""
    try:
        data = request.json
        
        # 转发到本地Ollama
        response = requests.post(
            'http://localhost:11434/api/generate',
            json=data,
            timeout=30
        )
        
        return jsonify(response.json())
        
    except Exception as e:
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'services': {
            'classifier': True,
            'duplicate_detector': True,
            'ollama': True
        }
    })

if __name__ == '__main__':
    print("🚀 启动本地LLM API服务器...")
    app.run(host='0.0.0.0', port=8000, debug=False)
'''
        
        try:
            with open(api_script, 'w', encoding='utf-8') as f:
                f.write(api_content)
            
            api_script.chmod(0o755)
            print(f"✅ LLM API服务器脚本已创建: {api_script}")
            return True
            
        except Exception as e:
            print(f"❌ 创建API服务器失败: {str(e)}")
            return False
    
    def setup_all_workflows(self) -> bool:
        """设置所有工作流"""
        print("🚀 开始配置HawaiiHub n8n自动化工作流...")
        
        # 1. 等待n8n服务
        if not self.wait_for_n8n():
            return False
        
        # 2. 加载配置
        config = self.load_workflow_config()
        if not config:
            return False
        
        # 3. 创建API端点
        if not self.create_local_llm_api_endpoints():
            print("⚠️ API端点创建失败，但继续配置工作流...")
        
        # 4. 设置凭据
        self.setup_credentials()
        
        # 5. 创建工作流
        workflows = config.get("workflows", {})
        success_count = 0
        
        for workflow_name, workflow_config in workflows.items():
            print(f"\n📋 配置工作流: {workflow_config['name']}")
            
            n8n_workflow = self.create_n8n_workflow(workflow_name, workflow_config)
            
            if self.upload_workflow(n8n_workflow):
                success_count += 1
            
            time.sleep(1)  # 避免API限制
        
        print(f"\n🎉 工作流配置完成!")
        print(f"✅ 成功创建: {success_count}/{len(workflows)} 个工作流")
        print(f"🌐 n8n访问地址: {self.n8n_url}")
        
        return success_count > 0
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """获取工作流状态"""
        try:
            response = self.session.get(f"{self.api_url}/workflows")
            
            if response.status_code == 200:
                workflows = response.json()
                return {
                    'total': len(workflows),
                    'active': sum(1 for w in workflows if w.get('active', False)),
                    'workflows': [{'name': w['name'], 'active': w.get('active', False)} for w in workflows]
                }
            else:
                return {'error': f"获取工作流状态失败: {response.status_code}"}
                
        except Exception as e:
            return {'error': str(e)}

def main():
    """主函数"""
    print("🔧 HawaiiHub n8n工作流自动配置器")
    print("=" * 50)
    
    # 创建管理器
    manager = N8NWorkflowManager()
    
    # 设置所有工作流
    if manager.setup_all_workflows():
        print("\n📊 工作流状态:")
        status = manager.get_workflow_status()
        
        if 'error' not in status:
            print(f"总工作流数: {status['total']}")
            print(f"活跃工作流: {status['active']}")
            print("\n工作流列表:")
            for workflow in status['workflows']:
                status_icon = "✅" if workflow['active'] else "⏸️"
                print(f"  {status_icon} {workflow['name']}")
        
        print(f"\n🎯 下一步操作:")
        print(f"1. 访问 http://192.168.65.3:5678 打开n8n界面")
        print(f"2. 启动本地LLM API: python3 services/ai-classifier/llm_api_server.py")
        print(f"3. 确保Ollama服务运行: ollama serve")
        print(f"4. 测试工作流执行")
        
    else:
        print("❌ 工作流配置失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
