#!/usr/bin/env python3
"""
系统监控脚本
定期检查AI模块和集成服务的运行状态
"""

import time
import json
import logging
import requests
import sqlite3
from datetime import datetime
from pathlib import Path

class SystemMonitor:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent.parent
        self.log_file = self.base_dir / "data" / "logs" / "system_monitor.log"
        self.setup_logging()
        
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def check_services(self):
        """检查服务状态"""
        services = {
            "FreshRSS": "http://localhost:8080",
            "RSSHub": "http://localhost:1200"
        }
        
        status = {}
        for service, url in services.items():
            try:
                response = requests.get(url, timeout=10)
                status[service] = response.status_code == 200
                self.logger.info(f"{service}: {'✅ 正常' if status[service] else '❌ 异常'}")
            except Exception as e:
                status[service] = False
                self.logger.error(f"{service}: ❌ 异常 - {str(e)}")
        
        return status
    
    def check_database(self):
        """检查数据库状态"""
        try:
            db_path = self.base_dir / "services" / "freshrss" / "data" / "users" / "aloha" / "db.sqlite"
            if not db_path.exists():
                self.logger.error("数据库文件不存在")
                return False
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM entry")
            count = cursor.fetchone()[0]
            conn.close()
            
            self.logger.info(f"数据库: ✅ 正常 ({count} 条记录)")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库: ❌ 异常 - {str(e)}")
            return False
    
    def run_monitoring(self):
        """运行监控"""
        self.logger.info("开始系统监控...")
        
        while True:
            try:
                # 检查服务
                service_status = self.check_services()
                
                # 检查数据库
                db_status = self.check_database()
                
                # 记录监控结果
                monitoring_result = {
                    "timestamp": datetime.now().isoformat(),
                    "services": service_status,
                    "database": db_status
                }
                
                # 保存监控数据
                monitor_data_file = self.base_dir / "data" / "logs" / f"monitor_{datetime.now().strftime('%Y%m%d')}.json"
                
                if monitor_data_file.exists():
                    with open(monitor_data_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                else:
                    data = []
                
                data.append(monitoring_result)
                
                with open(monitor_data_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                
                # 等待下次检查
                time.sleep(300)
                
            except KeyboardInterrupt:
                self.logger.info("监控停止")
                break
            except Exception as e:
                self.logger.error(f"监控异常: {str(e)}")
                time.sleep(60)  # 出错后等待1分钟再继续

if __name__ == "__main__":
    monitor = SystemMonitor()
    monitor.run_monitoring()
