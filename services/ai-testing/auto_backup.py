#!/usr/bin/env python3
"""
自动备份脚本
定期备份数据库和配置文件
"""

import shutil
import sqlite3
from datetime import datetime
from pathlib import Path

def backup_database():
    """备份数据库"""
    base_dir = Path(__file__).parent.parent.parent
    db_path = base_dir / "services" / "freshrss" / "data" / "users" / "aloha" / "db.sqlite"
    
    if db_path.exists():
        backup_dir = base_dir / "data" / "backups"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = backup_dir / f"db_backup_{timestamp}.sqlite"
        
        shutil.copy2(db_path, backup_path)
        print(f"数据库备份完成: {backup_path}")
    else:
        print("数据库文件不存在")

def backup_configs():
    """备份配置文件"""
    base_dir = Path(__file__).parent.parent.parent
    config_files = [
        "services/ai-classifier/duplicate_config.json",
        "services/ai-classifier/cache_config.json"
    ]
    
    backup_dir = base_dir / "data" / "backups" / "configs"
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    for config_file in config_files:
        config_path = base_dir / config_file
        if config_path.exists():
            backup_path = backup_dir / f"{config_path.stem}_{timestamp}.json"
            shutil.copy2(config_path, backup_path)
            print(f"配置备份完成: {backup_path}")

if __name__ == "__main__":
    print("开始自动备份...")
    backup_database()
    backup_configs()
    print("备份完成")
