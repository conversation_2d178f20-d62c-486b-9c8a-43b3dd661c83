#!/usr/bin/env python3
"""
自动优化脚本
定期运行系统优化任务
"""

import sys
from pathlib import Path

# 添加模块路径
sys.path.append(str(Path(__file__).parent.parent / "ai-classifier"))

def run_classification_optimization():
    """运行分类优化"""
    try:
        from classifier_optimizer import ClassifierOptimizer, OptimizationConfig
        
        config = OptimizationConfig()
        optimizer = ClassifierOptimizer(config)
        
        results = optimizer.run_comprehensive_optimization()
        
        if results["success"]:
            print("✅ 分类系统优化完成")
        else:
            print(f"❌ 分类系统优化失败: {results['message']}")
            
    except Exception as e:
        print(f"❌ 分类优化异常: {str(e)}")

def run_duplicate_optimization():
    """运行重复检测优化"""
    try:
        from duplicate_optimizer import DuplicateOptimizer, DuplicateOptimizationConfig
        
        config = DuplicateOptimizationConfig()
        optimizer = DuplicateOptimizer(config)
        
        results = optimizer.run_comprehensive_optimization()
        
        if results["success"]:
            print("✅ 重复检测系统优化完成")
        else:
            print(f"❌ 重复检测系统优化失败: {results['message']}")
            
    except Exception as e:
        print(f"❌ 重复检测优化异常: {str(e)}")

if __name__ == "__main__":
    print("开始自动优化...")
    run_classification_optimization()
    run_duplicate_optimization()
    print("优化完成")
