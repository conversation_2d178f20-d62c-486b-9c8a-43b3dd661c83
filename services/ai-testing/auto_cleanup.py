#!/usr/bin/env python3
"""
自动清理脚本
清理旧日志、缓存和临时文件
"""

import os
import shutil
from datetime import datetime, timedelta
from pathlib import Path

def cleanup_logs():
    """清理旧日志"""
    base_dir = Path(__file__).parent.parent.parent
    log_dir = base_dir / "data" / "logs"
    
    if not log_dir.exists():
        return
    
    # 删除7天前的日志
    cutoff_date = datetime.now() - timedelta(days=7)
    
    for log_file in log_dir.glob("*.log"):
        if log_file.stat().st_mtime < cutoff_date.timestamp():
            log_file.unlink()
            print(f"删除旧日志: {log_file}")

def cleanup_cache():
    """清理缓存"""
    base_dir = Path(__file__).parent.parent.parent
    cache_dirs = [
        base_dir / "services" / "ai-classifier" / "cache",
        base_dir / "data" / "cache"
    ]
    
    for cache_dir in cache_dirs:
        if cache_dir.exists():
            # 清理3天前的缓存
            cutoff_date = datetime.now() - timedelta(days=3)
            
            for cache_file in cache_dir.glob("*"):
                if cache_file.stat().st_mtime < cutoff_date.timestamp():
                    if cache_file.is_file():
                        cache_file.unlink()
                    elif cache_file.is_dir():
                        shutil.rmtree(cache_file)
                    print(f"删除旧缓存: {cache_file}")

if __name__ == "__main__":
    print("开始自动清理...")
    cleanup_logs()
    cleanup_cache()
    print("清理完成")
