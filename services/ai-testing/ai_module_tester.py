#!/usr/bin/env python3
"""
AI模块综合测试框架
统一测试智能分类系统和重复检测系统的性能、准确性和稳定性
"""

import sys
import json
import time
import psutil
import logging
import sqlite3
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass, asdict

# 添加AI分类器模块路径
sys.path.append(str(Path(__file__).parent.parent / "ai-classifier"))

try:
    from content_classifier import HawaiiContentClassifier
    from duplicate_detector import DuplicateDetector, DuplicateDetectionIntegration
except ImportError as e:
    print(f"❌ 导入AI模块失败: {e}")
    sys.exit(1)

@dataclass
class TestMetrics:
    """测试指标数据类"""
    test_name: str
    start_time: float
    end_time: float
    duration: float
    memory_usage_mb: float
    cpu_usage_percent: float
    success_count: int
    total_count: int
    accuracy_rate: float
    error_messages: List[str]
    additional_data: Dict[str, Any]

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.start_memory = 0
        self.start_cpu = 0
        self.start_time = 0
    
    def start_monitoring(self):
        """开始监控"""
        self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.start_cpu = self.process.cpu_percent()
        self.start_time = time.time()
    
    def get_metrics(self) -> Tuple[float, float, float]:
        """获取性能指标"""
        current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        current_cpu = self.process.cpu_percent()
        duration = time.time() - self.start_time
        
        return duration, current_memory, current_cpu

class AIModuleTester:
    """AI模块综合测试器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent.parent
        self.test_results_dir = self.base_dir / "data" / "ai_testing"
        self.test_results_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化日志
        self.setup_logging()
        
        # 初始化AI模块
        self.classifier = HawaiiContentClassifier()
        self.duplicate_detector = DuplicateDetector()
        self.duplicate_integration = DuplicateDetectionIntegration()
        
        # 性能监控器
        self.monitor = PerformanceMonitor()
        
        # 测试结果存储
        self.test_results: List[TestMetrics] = []
    
    def setup_logging(self):
        """设置日志"""
        log_file = self.test_results_dir / f"ai_testing_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def get_test_articles(self, limit: int = 50) -> List[Dict]:
        """从FreshRSS获取测试文章"""
        try:
            freshrss_db = self.base_dir / "services" / "freshrss" / "data" / "users" / "aloha" / "db.sqlite"
            
            if not freshrss_db.exists():
                self.logger.error(f"FreshRSS数据库不存在: {freshrss_db}")
                return []
            
            conn = sqlite3.connect(freshrss_db)
            cursor = conn.cursor()
            
            query = """
            SELECT id, title, content, link, author, date
            FROM entry 
            WHERE content IS NOT NULL AND content != ''
            ORDER BY date DESC 
            LIMIT ?
            """
            
            cursor.execute(query, (limit,))
            rows = cursor.fetchall()
            conn.close()
            
            articles = []
            for row in rows:
                articles.append({
                    'id': str(row[0]),
                    'title': row[1] or '',
                    'content': row[2] or '',
                    'url': row[3] or '',
                    'source': row[4] or 'Unknown',
                    'publish_date': row[5] or ''
                })
            
            self.logger.info(f"获取了 {len(articles)} 篇测试文章")
            return articles
            
        except Exception as e:
            self.logger.error(f"获取测试文章失败: {str(e)}")
            return []
    
    def test_classification_accuracy(self, articles: List[Dict]) -> TestMetrics:
        """测试分类准确性"""
        self.logger.info("开始测试智能分类系统准确性...")
        
        self.monitor.start_monitoring()
        errors = []
        success_count = 0
        classification_results = []
        
        try:
            for article in articles:
                try:
                    result = self.classifier.classify_content(
                        article['title'], 
                        article['content']
                    )
                    
                    if result and hasattr(result, 'category'):
                        success_count += 1
                        classification_results.append({
                            'article_id': article['id'],
                            'title': article['title'][:50] + '...',
                            'category': result.category,
                            'subcategory': result.subcategory or '',
                            'confidence': result.confidence or 0
                        })
                    else:
                        errors.append(f"分类失败: {article['id']}")
                        
                except Exception as e:
                    errors.append(f"处理文章 {article['id']} 时出错: {str(e)}")
            
            duration, memory, cpu = self.monitor.get_metrics()
            accuracy = success_count / len(articles) * 100 if articles else 0
            
            metrics = TestMetrics(
                test_name="智能分类准确性测试",
                start_time=self.monitor.start_time,
                end_time=time.time(),
                duration=duration,
                memory_usage_mb=memory,
                cpu_usage_percent=cpu,
                success_count=success_count,
                total_count=len(articles),
                accuracy_rate=accuracy,
                error_messages=errors,
                additional_data={
                    'classification_results': classification_results,
                    'avg_processing_time': duration / len(articles) if articles else 0
                }
            )
            
            self.logger.info(f"分类测试完成: 准确率 {accuracy:.1f}%, 处理速度 {duration/len(articles):.3f}秒/篇")
            return metrics
            
        except Exception as e:
            self.logger.error(f"分类测试异常: {str(e)}")
            duration, memory, cpu = self.monitor.get_metrics()
            
            return TestMetrics(
                test_name="智能分类准确性测试",
                start_time=self.monitor.start_time,
                end_time=time.time(),
                duration=duration,
                memory_usage_mb=memory,
                cpu_usage_percent=cpu,
                success_count=success_count,
                total_count=len(articles),
                accuracy_rate=0,
                error_messages=[str(e)],
                additional_data={}
            )
    
    def test_duplicate_detection_accuracy(self, articles: List[Dict]) -> TestMetrics:
        """测试重复检测准确性"""
        self.logger.info("开始测试重复检测系统准确性...")
        
        self.monitor.start_monitoring()
        errors = []
        
        try:
            # 执行重复检测
            duplicate_groups = self.duplicate_detector.detect_duplicates(articles)
            
            duration, memory, cpu = self.monitor.get_metrics()
            
            # 计算检测统计
            total_duplicates = sum(len(group.articles) for group in duplicate_groups)
            detection_rate = total_duplicates / len(articles) * 100 if articles else 0
            
            # 分析检测结果
            detection_analysis = {
                'total_groups': len(duplicate_groups),
                'total_duplicates': total_duplicates,
                'detection_rate': detection_rate,
                'group_sizes': [len(group.articles) for group in duplicate_groups],
                'avg_similarity_scores': []
            }
            
            for group in duplicate_groups:
                if group.similarity_scores:
                    avg_sim = sum(group.similarity_scores) / len(group.similarity_scores)
                    detection_analysis['avg_similarity_scores'].append(avg_sim)
            
            metrics = TestMetrics(
                test_name="重复检测准确性测试",
                start_time=self.monitor.start_time,
                end_time=time.time(),
                duration=duration,
                memory_usage_mb=memory,
                cpu_usage_percent=cpu,
                success_count=len(duplicate_groups),
                total_count=len(articles),
                accuracy_rate=detection_rate,
                error_messages=errors,
                additional_data=detection_analysis
            )
            
            self.logger.info(f"重复检测完成: 发现 {len(duplicate_groups)} 个重复组, 检测率 {detection_rate:.1f}%")
            return metrics
            
        except Exception as e:
            self.logger.error(f"重复检测测试异常: {str(e)}")
            duration, memory, cpu = self.monitor.get_metrics()
            
            return TestMetrics(
                test_name="重复检测准确性测试",
                start_time=self.monitor.start_time,
                end_time=time.time(),
                duration=duration,
                memory_usage_mb=memory,
                cpu_usage_percent=cpu,
                success_count=0,
                total_count=len(articles),
                accuracy_rate=0,
                error_messages=[str(e)],
                additional_data={}
            )
    
    def run_comprehensive_test(self, article_limit: int = 100) -> Dict[str, Any]:
        """运行综合测试"""
        self.logger.info(f"开始AI模块综合测试 (文章数量: {article_limit})")
        
        # 获取测试数据
        test_articles = self.get_test_articles(article_limit)
        
        if not test_articles:
            self.logger.error("无法获取测试文章，测试终止")
            return {"success": False, "message": "无法获取测试数据"}
        
        # 执行各项测试
        classification_metrics = self.test_classification_accuracy(test_articles)
        self.test_results.append(classification_metrics)
        
        duplicate_metrics = self.test_duplicate_detection_accuracy(test_articles)
        self.test_results.append(duplicate_metrics)
        
        # 生成综合报告
        report = self.generate_comprehensive_report()
        
        # 保存测试结果
        self.save_test_results()
        
        return {
            "success": True,
            "message": "综合测试完成",
            "test_results": self.test_results,
            "report": report
        }
    
    def generate_comprehensive_report(self) -> str:
        """生成综合测试报告"""
        if not self.test_results:
            return "暂无测试结果"
        
        report = f"""
🧪 AI模块综合测试报告
{'=' * 60}

📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📊 测试模块数量: {len(self.test_results)}

"""
        
        for i, metrics in enumerate(self.test_results, 1):
            report += f"""
{i}. {metrics.test_name}
{'-' * 40}
⏱️  执行时间: {metrics.duration:.2f} 秒
💾 内存使用: {metrics.memory_usage_mb:.1f} MB
🖥️  CPU使用: {metrics.cpu_usage_percent:.1f}%
✅ 成功率: {metrics.success_count}/{metrics.total_count} ({metrics.accuracy_rate:.1f}%)
⚡ 处理速度: {metrics.duration/metrics.total_count:.3f} 秒/篇

"""
            
            if metrics.error_messages:
                report += f"❌ 错误信息:\n"
                for error in metrics.error_messages[:5]:  # 只显示前5个错误
                    report += f"   - {error}\n"
                if len(metrics.error_messages) > 5:
                    report += f"   ... 还有 {len(metrics.error_messages) - 5} 个错误\n"
                report += "\n"
        
        # 总体评估
        avg_accuracy = sum(m.accuracy_rate for m in self.test_results) / len(self.test_results)
        total_duration = sum(m.duration for m in self.test_results)
        max_memory = max(m.memory_usage_mb for m in self.test_results)
        
        report += f"""
📈 总体评估
{'-' * 40}
🎯 平均准确率: {avg_accuracy:.1f}%
⏱️  总执行时间: {total_duration:.2f} 秒
💾 峰值内存使用: {max_memory:.1f} MB
🏆 系统状态: {'✅ 优秀' if avg_accuracy >= 90 else '⚠️ 需要优化' if avg_accuracy >= 70 else '❌ 需要修复'}

"""
        
        return report
    
    def save_test_results(self):
        """保存测试结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细结果
        results_file = self.test_results_dir / f"test_results_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump([asdict(result) for result in self.test_results], f, indent=2, ensure_ascii=False)
        
        # 保存报告
        report_file = self.test_results_dir / f"test_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_comprehensive_report())
        
        self.logger.info(f"测试结果已保存: {results_file}")
        self.logger.info(f"测试报告已保存: {report_file}")

def main():
    """主函数"""
    print("🧪 AI模块综合测试框架")
    print("=" * 50)
    
    tester = AIModuleTester()
    
    # 运行综合测试
    results = tester.run_comprehensive_test(article_limit=50)
    
    if results["success"]:
        print("✅ 综合测试完成!")
        print(results["report"])
    else:
        print(f"❌ 测试失败: {results['message']}")

if __name__ == "__main__":
    main()
